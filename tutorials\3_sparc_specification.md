文件路径: tutorials/3_sparc_specification.md

# SPARC 开发流程：3. 规范 (Specification)

规范阶段是 SPARC 开发流程的基石，旨在确保项目团队对要解决的问题、目标以及如何衡量成功有清晰、共同的理解。这个阶段的核心是**理解和定义**。

## 3.1 目标

规范阶段的主要目标包括：

- 识别并理解所有利益相关者的需求。
- 明确项目的范围和边界。
- 定义清晰、可衡量、可实现、相关和有时间限制 (SMART) 的目标。
- 识别项目的约束和假设。
- 建立衡量项目成功的标准。

## 3.2 关键活动

在规范阶段，通常会进行以下关键活动：

### 3.2.1 需求收集 (Requirements Gathering)

与利益相关者（如客户、用户、团队成员等）进行访谈、研讨会、问卷调查等，以收集他们的需求和期望。

### 3.2.2 需求分析 (Requirements Analysis)

对收集到的原始需求进行审查、组织和提炼，识别冲突、不一致或不完整之处。

### 3.2.3 用户故事 (User Stories)

以用户的角度描述功能需求，通常采用以下格式：

“**作为** [用户角色]，**我想要** [目标/愿望]，**以便** [价值/好处]。”

例如：
“**作为**注册用户，**我想要**能够重置我的密码，**以便**在我忘记密码时可以重新访问我的账户。”

### 3.2.4 验收标准 (Acceptance Criteria)

为每个用户故事或功能定义明确的条件，用于判断该功能是否已正确实现并满足需求。通常使用 Gherkin 语法（Given-When-Then）来编写：

**Given** [初始条件]
**When** [执行某个动作]
**Then** [预期的结果]

例如：
**Given** 我是一个注册用户并且忘记了密码
**When** 我在登录页面点击“忘记密码”并输入我的注册邮箱
**Then** 系统应该发送一封包含密码重置链接的邮件到我的邮箱

### 3.2.5 范围定义 (Scope Definition)

明确项目将包含哪些功能和特性，以及不包含哪些。这有助于管理期望并防止范围蔓延 (Scope Creep)。

### 3.2.6 约束和假设 (Constraints and Assumptions)

识别可能影响项目的限制（如时间、预算、技术限制）和在规划时假定的条件。

### 3.2.7 文档编写 (Documentation)

将所有收集、分析和定义的需求、目标、范围、约束等记录在规范文档中。

## 3.3 产出物

规范阶段的主要产出物通常包括：

- 需求文档 (Requirements Document)
- 用户故事列表 (User Story Backlog)
- 验收标准 (Acceptance Criteria)
- 项目范围说明书 (Project Scope Statement)

## 3.4 与其他阶段的关系

规范阶段为后续的伪代码、架构、优化和完成阶段提供了必要的基础和指导。清晰的规范有助于减少返工，提高开发效率和项目成功率。

接下来，我们可以深入探讨需求收集的具体技术，或者继续学习 SPARC 的下一个阶段：伪代码。