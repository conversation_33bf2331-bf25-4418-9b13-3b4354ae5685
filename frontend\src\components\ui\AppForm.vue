<template>
  <el-form
    :model="model"
    :rules="rules"
    :label-position="labelPosition"
    :label-width="labelWidth"
    :inline="inline"
    :disabled="disabled"
    :class="['app-form', customClass]"
    ref="formRef"
  >
    <slot></slot>
    <div v-if="showActions" class="app-form-actions">
      <slot name="actions">
        <el-button @click="handleReset">{{ resetText }}</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">{{ submitText }}</el-button>
      </slot>
    </div>
  </el-form>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  // 表单数据对象
  model: {
    type: Object,
    required: true
  },
  // 表单验证规则
  rules: {
    type: Object,
    default: () => ({})
  },
  // 标签位置
  labelPosition: {
    type: String,
    default: 'right',
    validator: (value) => {
      return ['left', 'right', 'top'].includes(value)
    }
  },
  // 标签宽度
  labelWidth: {
    type: String,
    default: '100px'
  },
  // 是否行内表单
  inline: {
    type: Boolean,
    default: false
  },
  // 是否禁用表单
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否显示操作按钮
  showActions: {
    type: Boolean,
    default: true
  },
  // 提交按钮文本
  submitText: {
    type: String,
    default: '提交'
  },
  // 重置按钮文本
  resetText: {
    type: String,
    default: '重置'
  },
  // 是否加载中
  loading: {
    type: Boolean,
    default: false
  },
  // 自定义类名
  customClass: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['submit', 'reset'])

const formRef = ref(null)

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    emit('submit', props.model)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 重置表单
const handleReset = () => {
  if (!formRef.value) return
  
  formRef.value.resetFields()
  emit('reset')
}

// 暴露方法给父组件
defineExpose({
  validate: () => formRef.value?.validate(),
  validateField: (field) => formRef.value?.validateField(field),
  resetFields: () => formRef.value?.resetFields(),
  scrollToField: (field) => formRef.value?.scrollToField(field),
  clearValidate: (fields) => formRef.value?.clearValidate(fields),
  formRef
})
</script>

<style scoped>
.app-form {
  width: 100%;
}

.app-form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--spacing-6);
  gap: var(--spacing-3);
}
</style>
