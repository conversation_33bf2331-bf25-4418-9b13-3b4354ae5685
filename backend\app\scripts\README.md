# 数据库脚本

本目录包含与数据库相关的脚本，用于初始化、迁移和维护数据库。

## 脚本列表

1. `create_tables.py` - 创建数据库表
2. `create_admin.py` - 创建管理员用户
3. `add_order_id_to_license_keys.py` - 为 license_keys 表添加 order_id 列

## 使用方法

### 创建数据库表

```bash
# 在Docker环境中
docker-compose exec backend python app/scripts/create_tables.py

# 在本地环境中
cd backend
python -m app.scripts.create_tables
```

### 创建管理员用户

```bash
# 在Docker环境中
docker-compose exec backend python app/scripts/create_admin.py

# 在本地环境中
cd backend
python -m app.scripts.create_admin
```

### 为 license_keys 表添加 order_id 列

此脚本用于修复 license_keys 表缺少 order_id 列的问题。它会创建一个临时表，将现有数据迁移到新表中，并为每个许可密钥关联一个订单ID。

```bash
# 在Docker环境中
docker-compose exec backend python app/scripts/add_order_id_to_license_keys.py

# 在本地环境中
cd backend
python -m app.scripts.add_order_id_to_license_keys
```

## 注意事项

1. 在执行迁移脚本之前，请确保已备份数据库。
2. 脚本执行过程中可能会暂时影响系统的可用性。
3. 建议在非高峰期执行数据库迁移操作。
