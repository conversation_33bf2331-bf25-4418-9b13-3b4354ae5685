/**
 * 浏览器兼容性检测工具
 * 用于检测浏览器是否支持应用所需的关键功能
 */

// 需要检测的功能列表
const REQUIRED_FEATURES = [
  {
    name: 'ES6 语法',
    test: () => {
      try {
        // 测试箭头函数
        eval('() => {}')
        // 测试解构赋值
        eval('const { a } = { a: 1 }')
        // 测试模板字符串
        eval('`test ${1}`')
        // 测试 Promise
        return typeof Promise === 'function'
      } catch (e) {
        return false
      }
    }
  },
  {
    name: 'Fetch API',
    test: () => typeof fetch === 'function'
  },
  {
    name: 'localStorage',
    test: () => {
      try {
        localStorage.setItem('test', 'test')
        localStorage.removeItem('test')
        return true
      } catch (e) {
        return false
      }
    }
  },
  {
    name: 'sessionStorage',
    test: () => {
      try {
        sessionStorage.setItem('test', 'test')
        sessionStorage.removeItem('test')
        return true
      } catch (e) {
        return false
      }
    }
  },
  {
    name: 'Intersection Observer',
    test: () => typeof IntersectionObserver === 'function'
  },
  {
    name: 'CSS Grid',
    test: () => {
      const el = document.createElement('div')
      return typeof el.style.grid === 'string' || 
             typeof el.style.gridTemplate === 'string'
    }
  },
  {
    name: 'CSS Flexbox',
    test: () => {
      const el = document.createElement('div')
      return typeof el.style.flex === 'string' || 
             typeof el.style.flexGrow === 'string'
    }
  },
  {
    name: 'CSS Variables',
    test: () => {
      const el = document.createElement('div')
      el.style.setProperty('--test', '0')
      return Boolean(el.style.getPropertyValue('--test'))
    }
  },
  {
    name: 'WebP 图片格式',
    test: () => {
      const canvas = document.createElement('canvas')
      if (canvas.getContext && canvas.getContext('2d')) {
        // 尝试将canvas转换为WebP格式
        return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0
      }
      return false
    }
  }
]

// 推荐的浏览器列表
const RECOMMENDED_BROWSERS = [
  { name: 'Chrome', version: '88+' },
  { name: 'Firefox', version: '85+' },
  { name: 'Safari', version: '14+' },
  { name: 'Edge', version: '88+' }
]

/**
 * 检测浏览器兼容性
 * @returns {Object} 兼容性检测结果
 */
export function checkBrowserCompatibility() {
  const results = {
    isCompatible: true,
    missingFeatures: [],
    browserInfo: getBrowserInfo(),
    recommendedBrowsers: RECOMMENDED_BROWSERS
  }
  
  // 检测每个必需功能
  REQUIRED_FEATURES.forEach(feature => {
    const isSupported = feature.test()
    
    if (!isSupported) {
      results.isCompatible = false
      results.missingFeatures.push(feature.name)
    }
  })
  
  return results
}

/**
 * 获取浏览器信息
 * @returns {Object} 浏览器名称和版本
 */
function getBrowserInfo() {
  const ua = navigator.userAgent
  let browserName = 'Unknown'
  let browserVersion = 'Unknown'
  
  // 检测Edge
  if (ua.indexOf('Edg') > -1) {
    browserName = 'Edge'
    browserVersion = ua.match(/Edg\/([\d.]+)/)[1]
  }
  // 检测Chrome
  else if (ua.indexOf('Chrome') > -1) {
    browserName = 'Chrome'
    browserVersion = ua.match(/Chrome\/([\d.]+)/)[1]
  }
  // 检测Firefox
  else if (ua.indexOf('Firefox') > -1) {
    browserName = 'Firefox'
    browserVersion = ua.match(/Firefox\/([\d.]+)/)[1]
  }
  // 检测Safari
  else if (ua.indexOf('Safari') > -1) {
    browserName = 'Safari'
    browserVersion = ua.match(/Version\/([\d.]+)/)[1]
  }
  // 检测IE
  else if (ua.indexOf('MSIE') > -1 || ua.indexOf('Trident/') > -1) {
    browserName = 'Internet Explorer'
    browserVersion = ua.indexOf('MSIE') > -1 ? 
      ua.match(/MSIE ([\d.]+)/)[1] : 
      ua.match(/rv:([\d.]+)/)[1]
  }
  
  return {
    name: browserName,
    version: browserVersion,
    userAgent: ua
  }
}

/**
 * 显示兼容性警告
 * @param {Object} results - 兼容性检测结果
 */
export function showCompatibilityWarning(results) {
  if (!results.isCompatible) {
    console.warn('浏览器兼容性警告:', results)
    
    // 创建警告元素
    const warningEl = document.createElement('div')
    warningEl.className = 'browser-compatibility-warning'
    warningEl.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      background-color: #fff3cd;
      color: #856404;
      padding: 12px 20px;
      font-size: 14px;
      text-align: center;
      z-index: 9999;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    `
    
    // 警告内容
    warningEl.innerHTML = `
      <strong>浏览器兼容性警告:</strong> 
      您当前使用的浏览器 (${results.browserInfo.name} ${results.browserInfo.version}) 
      可能无法正常显示本网站的全部功能。
      我们推荐使用 ${results.recommendedBrowsers.map(b => `${b.name} ${b.version}`).join('、')}。
      <button style="margin-left: 10px; padding: 2px 8px; cursor: pointer;">关闭</button>
    `
    
    // 添加关闭按钮事件
    warningEl.querySelector('button').addEventListener('click', () => {
      document.body.removeChild(warningEl)
      // 记住用户已关闭警告
      localStorage.setItem('compatibility_warning_dismissed', 'true')
    })
    
    // 如果用户之前没有关闭过警告，则显示
    if (!localStorage.getItem('compatibility_warning_dismissed')) {
      // 等待DOM加载完成后添加警告
      if (document.body) {
        document.body.appendChild(warningEl)
      } else {
        window.addEventListener('DOMContentLoaded', () => {
          document.body.appendChild(warningEl)
        })
      }
    }
  }
}

/**
 * 初始化兼容性检测
 */
export function initCompatibilityCheck() {
  const results = checkBrowserCompatibility()
  
  // 如果不兼容，显示警告
  if (!results.isCompatible) {
    showCompatibilityWarning(results)
  }
  
  // 返回检测结果
  return results
}
