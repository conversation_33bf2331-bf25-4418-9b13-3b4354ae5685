import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
// 导入自定义样式
import './assets/styles/main.css'
// 导入自定义组件
import UIComponents from './components/ui'
import App from './App.vue'
import router from './router'

// 注释掉尚未创建的导入
// 如需使用这些功能，请先创建相应的文件
// import lazyload from './directives/lazyload'
// import { initPerformanceMonitoring } from './utils/performance'
// import { initCompatibilityCheck } from './utils/compatibility'

// 创建Pinia实例
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

// 创建应用实例
const app = createApp(App)

// 使用插件
app.use(pinia)
app.use(router)
app.use(ElementPlus, {
  size: 'default', // 设置组件默认尺寸
  zIndex: 3000 // 设置弹出组件的z-index
})
app.use(UIComponents)
// app.use(lazyload) // 注释掉尚未创建的指令

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue错误:', err)
  console.error('错误组件:', vm)
  console.error('错误信息:', info)

  // 在生产环境中可以将错误发送到错误跟踪服务
  if (process.env.NODE_ENV === 'production') {
    // sendToErrorTrackingService(err, vm, info)
  }
}

// 挂载应用
app.mount('#app')

// 注释掉尚未创建的功能初始化
// // 初始化性能监控（仅在生产环境）
// if (process.env.NODE_ENV === 'production') {
//   initPerformanceMonitoring()
// }

// // 初始化兼容性检测
// initCompatibilityCheck()

// 开发环境下的控制台提示
if (process.env.NODE_ENV === 'development') {
  console.log('%c软件和设备销售平台 - 开发模式', 'color: #409EFF; font-size: 16px; font-weight: bold;')
  console.log('Vue版本:', app.version)
  console.log('环境:', process.env.NODE_ENV)
}