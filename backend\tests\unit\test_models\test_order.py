import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime
import uuid

from app.models.order import Order, OrderItem, OrderStatus, LicenseKey
from app.models.product import Product
from app.models.user import User
from app.core.database import get_db

pytestmark = pytest.mark.asyncio


async def test_create_order(db_session: AsyncSession):
    """测试创建订单"""
    # 创建测试用户
    user = User(
        username="testuser",
        email="<EMAIL>",
        hashed_password="hashed_password",
        is_active=True,
        is_admin=False,
        address="Test Address"
    )
    db_session.add(user)
    await db_session.flush()

    # 创建测试产品
    product = Product(
        name="Test Product",
        description="Test Description",
        price=100.0,
        image_url="test.jpg",
        is_active=True,
        product_type="software"
    )
    db_session.add(product)
    await db_session.flush()

    # 创建订单
    order = Order(
        user_id=user.id,
        order_number=f"ORD-{int(datetime.now().timestamp())}-{user.id}-{uuid.uuid4().hex[:4]}",
        status=OrderStatus.PENDING,
        total_amount=100.0
    )
    db_session.add(order)
    await db_session.flush()

    # 创建订单项
    order_item = OrderItem(
        order_id=order.id,
        product_id=product.id,
        quantity=1,
        price=100.0
    )
    db_session.add(order_item)
    await db_session.commit()

    # 验证订单是否创建成功
    db_order = await db_session.get(Order, order.id)
    assert db_order is not None
    assert db_order.user_id == user.id
    assert db_order.status == OrderStatus.PENDING
    assert db_order.total_amount == 100.0

    # 验证订单项是否创建成功
    db_order_items = db_order.order_items
    assert len(db_order_items) == 1
    assert db_order_items[0].product_id == product.id
    assert db_order_items[0].quantity == 1
    assert db_order_items[0].price == 100.0


async def test_update_order_status(db_session: AsyncSession):
    """测试更新订单状态"""
    # 创建测试用户
    user = User(
        username="testuser2",
        email="<EMAIL>",
        hashed_password="hashed_password",
        is_active=True,
        is_admin=False,
        address="Test Address"
    )
    db_session.add(user)
    await db_session.flush()

    # 创建测试产品
    product = Product(
        name="Test Product 2",
        description="Test Description 2",
        price=200.0,
        image_url="test2.jpg",
        is_active=True,
        product_type="software"
    )
    db_session.add(product)
    await db_session.flush()

    # 创建订单
    order = Order(
        user_id=user.id,
        order_number=f"ORD-{int(datetime.now().timestamp())}-{user.id}-{uuid.uuid4().hex[:4]}",
        status=OrderStatus.PENDING,
        total_amount=200.0
    )
    db_session.add(order)
    await db_session.flush()

    # 创建订单项
    order_item = OrderItem(
        order_id=order.id,
        product_id=product.id,
        quantity=1,
        price=200.0
    )
    db_session.add(order_item)
    await db_session.commit()

    # 更新订单状态
    db_order = await db_session.get(Order, order.id)
    db_order.status = OrderStatus.PAID
    db_order.payment_method = "alipay"
    await db_session.commit()

    # 验证订单状态是否更新成功
    updated_order = await db_session.get(Order, order.id)
    assert updated_order.status == OrderStatus.PAID
    assert updated_order.payment_method == "alipay"


async def test_create_license_key_for_order(db_session: AsyncSession):
    """测试为订单创建许可密钥"""
    # 创建测试用户
    user = User(
        username="testuser3",
        email="<EMAIL>",
        hashed_password="hashed_password",
        is_active=True,
        is_admin=False,
        address="Test Address"
    )
    db_session.add(user)
    await db_session.flush()

    # 创建测试产品
    product = Product(
        name="Test Product 3",
        description="Test Description 3",
        price=300.0,
        image_url="test3.jpg",
        is_active=True,
        product_type="software"
    )
    db_session.add(product)
    await db_session.flush()

    # 创建订单
    order = Order(
        user_id=user.id,
        order_number=f"ORD-{int(datetime.now().timestamp())}-{user.id}-{uuid.uuid4().hex[:4]}",
        status=OrderStatus.PAID,
        total_amount=300.0,
        payment_method="alipay"
    )
    db_session.add(order)
    await db_session.flush()

    # 创建订单项
    order_item = OrderItem(
        order_id=order.id,
        product_id=product.id,
        quantity=1,
        price=300.0
    )
    db_session.add(order_item)
    await db_session.flush()

    # 创建许可密钥
    license_key = LicenseKey(
        user_id=user.id,
        product_id=product.id,
        order_id=order.id,
        license_key=f"LICENSE-{uuid.uuid4().hex}",
        is_active=True
    )
    db_session.add(license_key)
    await db_session.commit()

    # 验证许可密钥是否创建成功
    db_license_key = await db_session.get(LicenseKey, license_key.id)
    assert db_license_key is not None
    assert db_license_key.user_id == user.id
    assert db_license_key.product_id == product.id
    assert db_license_key.order_id == order.id
    assert db_license_key.is_active == True
    assert "LICENSE-" in db_license_key.license_key
