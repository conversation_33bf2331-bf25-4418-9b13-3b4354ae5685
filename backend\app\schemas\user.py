from datetime import datetime
from typing import Optional

from pydantic import BaseModel, EmailStr, Field


# 用户基础模型
class UserBase(BaseModel):
    """
    用户基础模型

    Attributes:
        username: 用户名
        email: 电子邮箱
    """
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="电子邮箱")


# 用户创建模型
class UserCreate(UserBase):
    """
    用户创建模型

    Attributes:
        username: 用户名
        email: 电子邮箱
        password: 密码
        address: 地址
    """
    password: str = Field(..., min_length=6, description="密码")
    address: Optional[str] = Field(None, description="地址")


# 用户更新模型
class UserUpdate(BaseModel):
    """
    用户更新模型

    Attributes:
        username: 用户名
        email: 电子邮箱
        address: 地址
    """
    username: Optional[str] = Field(None, min_length=3, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(None, description="电子邮箱")
    address: Optional[str] = Field(None, description="地址")


# 用户登录模型
class UserLogin(BaseModel):
    """
    用户登录模型

    Attributes:
        username: 用户名
        password: 密码
    """
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")


# 用户响应模型
class User(UserBase):
    """
    用户响应模型

    Attributes:
        id: 用户ID
        is_active: 是否活跃
        role: 角色
        address: 地址
        created_at: 创建时间
    """
    id: int = Field(..., description="用户ID")
    is_active: bool = Field(..., description="是否活跃")
    role: str = Field(..., description="角色")
    address: Optional[str] = Field(None, description="地址")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


# 令牌模型
class Token(BaseModel):
    """
    令牌模型

    Attributes:
        access_token: 访问令牌
        token_type: 令牌类型
    """
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(..., description="令牌类型")


# 令牌数据模型
class TokenData(BaseModel):
    """
    令牌数据模型

    Attributes:
        user_id: 用户ID
    """
    user_id: Optional[int] = Field(None, description="用户ID")