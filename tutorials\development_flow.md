# SPARC开发流程图

## Obsidian Canvas图形说明

```mermaid
graph TD
    %% 核心开发理念
    A[核心开发理念] --- B1[简单性]
    A --- B2[迭代优化]
    A --- B3[专注]
    A --- B4[质量]
    A --- B5[协作]

    %% 开发阶段
    C[开发阶段] --- D1[规格说明]
    C --- D2[伪代码]
    C --- D3[架构]
    C --- D4[优化]
    C --- D5[完成]
    
    %% 阶段关系
    D1 --> D2 --> D3 --> D4 --> D5

    %% 专门角色分组
    E[专门角色] --- F1[核心开发角色]
    E --- F2[支持角色]
    E --- F3[专业角色]

    %% 工作流特点
    G[工作流特点] --- H1[结构化工作流]
    G --- H2[灵活性]
    G --- H3[智能演进]
    G --- H4[持续集成]

    %% 外部服务
    I[外部服务] --- J1[SSO认证]
    I --- J2[支付服务]
    I --- J3[日志服务]

    %% 主要关系连接
    A --> C
    C --> E
    E --> G
    G --> I

    %% 样式设置
    classDef core fill:#f9f,stroke:#333,stroke-width:2px;
    classDef phase fill:#bbf,stroke:#333,stroke-width:2px;
    classDef role fill:#bfb,stroke:#333,stroke-width:2px;
    classDef flow fill:#fbf,stroke:#333,stroke-width:2px;
    classDef service fill:#fbb,stroke:#333,stroke-width:2px;

    class A,B1,B2,B3,B4,B5 core;
    class C,D1,D2,D3,D4,D5 phase;
    class E,F1,F2,F3 role;
    class G,H1,H2,H3,H4 flow;
    class I,J1,J2,J3 service;
```

## Obsidian Canvas设置说明

要在Obsidian中使用此流程图：

1. 创建新的Canvas文件
2. 将以下图形元素添加到Canvas中：

### 主要节点组
- 核心开发理念 (左上角)
- 开发阶段 (中上)
- 专门角色 (右上)
- 工作流特点 (中下)
- 外部服务 (右下)

### 颜色方案
- 核心理念: #f9f9f9
- 开发阶段: #bbbbff
- 专门角色: #bbffbb
- 工作流: #fbfbfb
- 外部服务: #ffbbbb

### 连接关系
- 使用箭头连接表示流程方向
- 使用直线连接表示归属关系
- 使用虚线连接表示协作关系

### 布局建议
- 采用层级结构布局
- 保持足够的空间间距
- 使用组来管理相关元素
- 添加说明标注提高可读性

## 更新维护

此文档将帮助团队成员理解和维护SPARC开发流程图。当流程发生变化时，请同步更新此文档和相应的Canvas文件。

版本：1.0
更新日期：2025/5/2