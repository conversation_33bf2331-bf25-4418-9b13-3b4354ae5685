<template>
  <div :class="['page-container', customClass]">
    <div v-if="title || $slots.header" class="page-header">
      <slot name="header">
        <h1 class="page-title">{{ title }}</h1>
        <p v-if="subtitle" class="page-subtitle">{{ subtitle }}</p>
      </slot>
    </div>
    <div class="page-content">
      <slot></slot>
    </div>
    <div v-if="$slots.footer" class="page-footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  // 页面标题
  title: {
    type: String,
    default: ''
  },
  // 页面副标题
  subtitle: {
    type: String,
    default: ''
  },
  // 自定义类名
  customClass: {
    type: String,
    default: ''
  }
})
</script>

<style scoped>
.page-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: var(--spacing-4);
  width: 100%;
}

.page-header {
  margin-bottom: var(--spacing-6);
  padding-bottom: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
}

.page-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.page-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin: 0;
}

.page-content {
  margin-bottom: var(--spacing-6);
}

.page-footer {
  margin-top: var(--spacing-6);
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--border-color);
}

@media (max-width: 768px) {
  .page-container {
    padding: var(--spacing-3);
  }
  
  .page-title {
    font-size: var(--font-size-2xl);
  }
  
  .page-subtitle {
    font-size: var(--font-size-base);
  }
}
</style>
