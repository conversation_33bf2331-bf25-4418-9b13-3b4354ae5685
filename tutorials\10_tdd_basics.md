# 10. 测试驱动开发 (TDD) 基础

## 什么是 TDD？
测试驱动开发（Test-Driven Development）是一种软件开发方法，强调在编写实际功能代码之前先编写自动化测试。

## TDD 循环 (红-绿-重构)
TDD 遵循一个简单的三步循环：

1.  **红 (Red)**
    *   编写一个针对你即将实现的功能的自动化测试。
    *   运行所有测试，确保你刚刚编写的新测试失败。这是“红”阶段，表示你有一个失败的测试，因为它所测试的功能还没有实现。

2.  **绿 (Green)**
    *   编写最少量的代码，使刚刚失败的测试通过。
    *   运行所有测试，确保所有测试都通过。这是“绿”阶段，表示你的代码功能已经实现，并且没有破坏现有功能。

3.  **重构 (Refactor)**
    *   在所有测试都通过的前提下，改进你的代码结构、可读性或性能。
    *   再次运行所有测试，确保重构没有引入新的错误。这是“重构”阶段，确保代码质量。

重复这个循环：先写一个失败的测试（红），然后编写代码使其通过（绿），最后改进代码（重构）。

## TDD 的好处
*   **确保代码可测试性**: 强制你在编写功能代码时就考虑如何测试它。
*   **提供即时反馈**: 快速知道你的代码是否按预期工作。
*   **作为活文档**: 测试用例清晰地展示了代码应该如何使用和行为。
*   **鼓励模块化设计**: 编写易于测试的代码通常意味着代码是模块化的，职责单一。
*   **减少 Bug**: 在开发早期捕获错误。
*   **提高开发信心**: 让你在修改或重构代码时更有信心，因为有测试作为保障。

## 如何开始
1.  选择一个小的、明确的功能点。
2.  根据该功能的需求编写一个测试用例。
3.  运行测试，确认它失败（红）。
4.  编写实现该功能的最少代码。
5.  运行测试，确认它通过（绿）。
6.  重构代码，改进设计。
7.  重复以上步骤，直到完成所有功能点。

## 文件路径: tutorials/10_tdd_basics.md