<template>
  <div class="order-list">
    <div class="order-list-header">
      <el-button type="primary" @click="$emit('refresh')" :loading="orderStore.loading">
        刷新订单
      </el-button>
    </div>

    <div class="order-list-content" v-if="orders.length > 0">
      <el-card v-for="order in orders" :key="order.id" class="order-card">
        <div class="order-header">
          <div class="order-number">
            <span class="label">订单号:</span>
            <span class="value">{{ order.order_number }}</span>
          </div>
          <div class="order-status">
            <el-tag :type="getStatusType(order.status)">
              {{ getStatusText(order.status) }}
            </el-tag>
          </div>
        </div>

        <div class="order-items">
          <div v-for="item in order.items" :key="item.id" class="order-item">
            <div class="item-name">{{ item.product?.name || `产品 ${item.product_id}` }}</div>
            <div class="item-price">¥{{ item.price.toFixed(2) }}</div>
            <div class="item-quantity">x{{ item.quantity }}</div>
          </div>
        </div>

        <div class="order-footer">
          <div class="order-total">
            <span class="label">总计:</span>
            <span class="value">¥{{ order.total_amount.toFixed(2) }}</span>
          </div>
          <div class="order-time">
            <span class="label">创建时间:</span>
            <span class="value">{{ formatDate(order.created_at) }}</span>
          </div>
          <div class="order-actions">
            <el-button
              type="primary"
              size="small"
              @click="viewOrderDetail(order.id)"
            >
              查看详情
            </el-button>
            <!-- 普通用户才能看到去支付按钮 -->
            <el-button
              v-if="order.status === 'pending' && !isAdmin"
              type="success"
              size="small"
              @click="payOrder(order.id)"
            >
              去支付
            </el-button>
            <!-- 管理员才能看到模拟支付按钮 -->
            <el-button
              v-if="order.status === 'pending' && isAdmin"
              type="warning"
              size="small"
              @click="mockPayment(order.id)"
            >
              模拟支付
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <el-empty v-else description="暂无订单" />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useOrderStore, useUserStore } from '../store'
import { ElMessage, ElMessageBox } from 'element-plus'

// 定义属性
const props = defineProps({
  orders: {
    type: Array,
    required: true
  }
})

// 定义事件
const emit = defineEmits(['refresh'])

// 状态管理
const orderStore = useOrderStore()
const userStore = useUserStore()
const router = useRouter()

// 计算属性
const isAdmin = computed(() => userStore.isAdmin)

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'pending':
      return 'warning'
    case 'paid':
      return 'success'
    case 'cancelled':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'pending':
      return '待支付'
    case 'paid':
      return '已支付'
    case 'cancelled':
      return '已取消'
    default:
      return '未知状态'
  }
}

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 查看订单详情
const viewOrderDetail = (orderId) => {
  router.push(`/orders/${orderId}`)
}

// 支付订单
const payOrder = async (orderId) => {
  const result = await orderStore.initiatePayment(orderId)
  if (result.success) {
    // 跳转到支付页面
    window.location.href = result.paymentUrl
  } else {
    ElMessage.error(result.message)
  }
}

// 模拟支付（仅管理员）
const mockPayment = async (orderId) => {
  if (!isAdmin.value) {
    ElMessage.error('无权执行此操作')
    return
  }

  try {
    await ElMessageBox.confirm(
      '确定要模拟支付此订单吗？此操作仅用于测试。',
      '模拟支付',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await orderStore.mockPaymentSuccess(orderId)
    if (result.success) {
      ElMessage.success('模拟支付成功')
      emit('refresh')
    } else {
      ElMessage.error(result.message)
    }
  } catch {
    // 用户取消操作
  }
}
</script>

<style scoped>
.order-list {
  width: 100%;
}

.order-list-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}

.order-card {
  margin-bottom: 20px;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.order-number .label,
.order-total .label,
.order-time .label {
  color: #909399;
  margin-right: 5px;
}

.order-items {
  margin-bottom: 15px;
}

.order-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.item-name {
  flex: 1;
}

.item-price,
.item-quantity {
  margin-left: 20px;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 10px;
  border-top: 1px solid #ebeef5;
}

.order-total .value {
  font-weight: bold;
  color: #f56c6c;
}

.order-actions {
  display: flex;
  gap: 10px;
}
</style>
