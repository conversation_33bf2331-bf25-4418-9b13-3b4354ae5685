from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.dependencies import get_current_user
from app.models.user import User
from app.schemas.order import (
    LicenseKeyResponse,
    OrderCreate,
    OrderResponse,
    PaymentInitiateResponse,
)
from app.services.payment import order_service, payment_service

router = APIRouter()


@router.post("/orders", response_model=OrderResponse, status_code=status.HTTP_201_CREATED)
async def create_order(
    order_create: OrderCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    创建订单

    Args:
        order_create: 订单创建模型
        current_user: 当前用户
        db: 数据库会话

    Returns:
        OrderResponse: 订单响应模型
    """
    try:
        order, _ = await order_service.create_order(db, current_user.id, order_create)
        # 获取包含订单项的完整订单
        order_with_items = await order_service.get_order_by_id(db, order.id, include_items=True)
        return order_with_items
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建订单失败: {str(e)}",
        )


@router.get("/orders", response_model=List[OrderResponse])
async def get_user_orders(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取用户订单列表

    Args:
        current_user: 当前用户
        db: 数据库会话

    Returns:
        List[OrderResponse]: 订单响应模型列表
    """
    try:
        orders = await order_service.get_user_orders(db, current_user.id, include_items=True)
        return orders
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取订单列表失败: {str(e)}",
        )


@router.get("/orders/{order_id}", response_model=OrderResponse)
async def get_order(
    order_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取订单详情

    Args:
        order_id: 订单ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        OrderResponse: 订单响应模型
    """
    try:
        order = await order_service.get_order_by_id(db, order_id, include_items=True)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在",
            )

        # 检查订单所有权
        if order.user_id != current_user.id and current_user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此订单",
            )

        return order
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取订单详情失败: {str(e)}",
        )


@router.post("/orders/{order_id}/pay", response_model=PaymentInitiateResponse)
async def initiate_payment(
    order_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    发起支付

    Args:
        order_id: 订单ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        PaymentInitiateResponse: 支付发起响应模型
    """
    try:
        # 获取订单
        order = await order_service.get_order_by_id(db, order_id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在",
            )

        # 检查订单所有权
        if order.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此订单",
            )

        # 检查订单状态
        if order.status != "pending":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="订单状态不正确",
            )

        # 生成支付链接
        payment_url = await payment_service.generate_payment_url(db, order_id)

        return PaymentInitiateResponse(order_id=order_id, payment_url=payment_url)
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"发起支付失败: {str(e)}",
        )


@router.get("/orders/{order_id}/status")
async def check_payment_status(
    order_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    查询支付状态

    Args:
        order_id: 订单ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        dict: 支付状态信息
    """
    try:
        # 获取订单
        order = await order_service.get_order_by_id(db, order_id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在",
            )

        # 检查订单所有权
        if order.user_id != current_user.id and current_user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此订单",
            )

        # 查询支付状态
        return await payment_service.check_payment_status(db, order_id)
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询支付状态失败: {str(e)}",
        )


@router.get("/license-keys", response_model=List[LicenseKeyResponse])
async def get_user_license_keys(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取用户软件许可密钥列表

    Args:
        current_user: 当前用户
        db: 数据库会话

    Returns:
        List[LicenseKeyResponse]: 软件许可密钥响应模型列表
    """
    try:
        license_keys = await order_service.get_user_license_keys(db, current_user.id)
        return license_keys
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取软件许可密钥列表失败: {str(e)}",
        )


# 仅用于测试的模拟支付成功接口
@router.post("/orders/{order_id}/mock-payment-success")
async def mock_payment_success(
    order_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    模拟支付成功（仅用于测试）

    Args:
        order_id: 订单ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        dict: 处理结果
    """
    try:
        # 检查用户权限
        if current_user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权执行此操作",
            )

        # 模拟支付成功
        result = await payment_service.mock_payment_success(db, order_id)
        return {"success": result, "message": "模拟支付成功"}
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"模拟支付失败: {str(e)}",
        )
