# 前端视图伪代码

## 1. 主页面 (views/Home.vue)

```vue
<template>
  <div class="home-container">
    <!-- 公司介绍部分 -->
    <section class="company-intro">
      <h1>{{ siteInfo.company_name }}</h1>
      <p>{{ siteInfo.description }}</p>
    </section>

    <!-- 产品展示部分 -->
    <section class="products-showcase">
      <h2>我们的产品</h2>

      <!-- 软件产品 -->
      <div class="product-section">
        <h3>软件产品</h3>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" v-for="product in softwareProducts" :key="product.id">
            <el-card class="product-card">
              <h4>{{ product.name }}</h4>
              <p>{{ product.description }}</p>
              <p class="price">¥{{ product.price }}</p>
              <el-button type="primary" @click="goToProductDetail(product.id)">查看详情</el-button>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 设备产品 -->
      <div class="product-section">
        <h3>设备产品</h3>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" v-for="product in deviceProducts" :key="product.id">
            <el-card class="product-card">
              <h4>{{ product.name }}</h4>
              <p>{{ product.description }}</p>
              <p class="price">¥{{ product.price }}</p>
              <el-button type="primary" @click="goToProductDetail(product.id)">查看详情</el-button>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </section>

    <!-- 联系信息部分 -->
    <section class="contact-info">
      <h2>联系我们</h2>
      <p>{{ siteInfo.contact_info }}</p>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { productApi } from '@/api/product';
import { siteApi } from '@/api/site';

const router = useRouter();
const softwareProducts = ref([]);
const deviceProducts = ref([]);
const siteInfo = ref({
  company_name: '',
  description: '',
  contact_info: ''
});

// 获取产品数据
const fetchProducts = async () => {
  try {
    // 获取软件产品
    const softwareResponse = await productApi.getProducts({ product_type: 'software' });
    if (softwareResponse.success) {
      softwareProducts.value = softwareResponse.data;
    }

    // 获取设备产品
    const deviceResponse = await productApi.getProducts({ product_type: 'device' });
    if (deviceResponse.success) {
      deviceProducts.value = deviceResponse.data;
    }
  } catch (error) {
    console.error('获取产品失败:', error);
  }
};

// 获取站点信息
const fetchSiteInfo = async () => {
  try {
    const response = await siteApi.getSiteInfo();
    if (response.success) {
      siteInfo.value = response.data;
    }
  } catch (error) {
    console.error('获取站点信息失败:', error);
  }
};

// 跳转到产品详情页
const goToProductDetail = (productId) => {
  router.push(`/products/${productId}`);
};

onMounted(() => {
  fetchProducts();
  fetchSiteInfo();
});
</script>
```

## 2. 产品详情页 (views/ProductDetail.vue)

```vue
<template>
  <div class="product-detail-container" v-if="product">
    <el-card>
      <h1>{{ product.name }}</h1>
      <p class="description">{{ product.description }}</p>
      <p class="price">¥{{ product.price }}</p>

      <div class="actions">
        <el-button type="primary" @click="handleBuy" :loading="loading">立即购买</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { productApi } from '@/api/product';
import { paymentApi } from '@/api/payment';
import { useUserStore } from '@/store/user';

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const productId = route.params.id;
const product = ref(null);
const loading = ref(false);

// 获取产品详情
const fetchProductDetail = async () => {
  try {
    const response = await productApi.getProduct(productId);
    if (response.success) {
      product.value = response.data;
    } else {
      ElMessage.error('获取产品详情失败');
    }
  } catch (error) {
    console.error('获取产品详情失败:', error);
    ElMessage.error('获取产品详情失败');
  }
};

// 处理购买
const handleBuy = async () => {
  // 检查用户是否登录
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录');
    router.push(`/login?redirect=/products/${productId}`);
    return;
  }

  loading.value = true;
  try {
    // 创建订单
    const orderResponse = await paymentApi.createOrder(productId);
    if (orderResponse.success) {
      const orderId = orderResponse.data.id;

      // 获取支付链接
      const paymentResponse = await paymentApi.getPaymentUrl(orderId);
      if (paymentResponse.success) {
        // 在新窗口打开支付页面
        window.open(paymentResponse.data.payment_url, '_blank');
        ElMessage.success('支付页面已在新窗口打开，请完成支付');
      } else {
        ElMessage.error('获取支付链接失败');
      }
    } else {
      ElMessage.error('创建订单失败');
    }
  } catch (error) {
    console.error('购买失败:', error);
    ElMessage.error('购买失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchProductDetail();
});
</script>
```
