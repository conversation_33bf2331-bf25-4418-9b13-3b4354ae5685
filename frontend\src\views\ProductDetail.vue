<template>
  <PageContainer customClass="product-detail-page">
    <!-- 返回按钮 -->
    <div class="back-link">
      <AppButton type="text" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        返回产品列表
      </AppButton>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <!-- 产品详情 -->
    <div v-else-if="product" class="product-detail">
      <div class="product-grid">
        <!-- 产品图片 -->
        <div class="product-image-section">
          <div class="product-image-container">
            <img :src="getProductImage(product.product_type)" class="product-image" :alt="product.name" />
            <div class="product-tag">
              <el-tag
                :type="product.product_type === 'software' ? 'success' : 'warning'"
                size="large"
                effect="dark"
              >
                {{ product.product_type === 'software' ? '软件产品' : '硬件产品' }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 产品信息 -->
        <div class="product-info-section">
          <div class="product-info">
            <h1 class="product-title">{{ product.name }}</h1>

            <div class="product-meta">
              <div class="product-price">
                <span class="price-value">¥{{ product.price.toFixed(2) }}</span>
              </div>

              <div class="product-stock" :class="{ 'low-stock': product.stock < 10 }">
                <el-icon><Goods /></el-icon>
                <span>库存: {{ product.stock }} {{ product.product_type === 'software' ? '份' : '件' }}</span>
              </div>
            </div>

            <div class="product-description">
              <h3>产品描述</h3>
              <p>{{ product.description }}</p>
            </div>

            <div class="product-features" v-if="product.product_type === 'software'">
              <h3>软件特性</h3>
              <ul>
                <li>即时下载，快速安装</li>
                <li>提供详细的使用文档</li>
                <li>购买后获得专业技术支持</li>
                <li>定期更新，持续优化</li>
              </ul>
            </div>

            <div class="product-features" v-else>
              <h3>硬件特性</h3>
              <ul>
                <li>高品质材料，精工制造</li>
                <li>快速发货，安全包装</li>
                <li>提供详细的安装指南</li>
                <li>一年质保，售后无忧</li>
              </ul>
            </div>

            <div class="product-actions">
              <AppButton
                type="primary"
                size="large"
                round
                :icon="ShoppingCart"
                @click="handleBuy"
              >
                立即购买
              </AppButton>
              <AppButton
                size="large"
                round
                @click="goBack"
              >
                继续浏览
              </AppButton>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 产品不存在 -->
    <div v-else class="product-not-found">
      <el-empty description="产品不存在或已下架">
        <AppButton type="primary" @click="goBack">返回产品列表</AppButton>
      </el-empty>
    </div>
  </PageContainer>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useProductStore } from '../store/product'
import { useUserStore } from '../store'
import PageContainer from '../components/layout/PageContainer.vue'
import { ArrowLeft, Goods, ShoppingCart } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const productId = route.params.id
const productStore = useProductStore()
const userStore = useUserStore()

// 计算属性
const product = computed(() => productStore.currentProduct)
const loading = computed(() => productStore.loading)
const isAdmin = computed(() => userStore.isAdmin)

// 获取产品详情
const fetchProductDetail = async () => {
  const result = await productStore.fetchProductById(productId)

  if (!result.success) {
    ElMessage.error(result.message || '获取产品详情失败')
  }
}

// 获取产品图片
const getProductImage = (type) => {
  if (type === 'software') {
    return 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80'
  } else {
    return 'https://images.unsplash.com/photo-1518770660439-4636190af475?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80'
  }
}

// 处理购买
const handleBuy = async () => {
  // 检查用户是否登录
  if (!userStore.isLoggedIn) {
    ElMessage({
      message: '请先登录后再购买',
      type: 'warning',
      duration: 3000,
      showClose: true
    })
    router.push({
      path: '/login',
      query: { redirect: route.fullPath }
    })
    return
  }

  // 检查是否是管理员
  if (isAdmin.value) {
    ElMessage({
      message: '管理员账号仅用于管理，不能购买产品',
      type: 'info',
      duration: 3000,
      showClose: true
    })
    return
  }

  try {
    // 创建订单
    const orderData = {
      items: [
        {
          product_id: parseInt(productId),
          quantity: 1
        }
      ]
    }

    // 导入订单状态管理
    const { useOrderStore } = await import('../store')
    const orderStore = useOrderStore()

    // 创建订单
    const result = await orderStore.createOrder(orderData)

    if (result.success) {
      ElMessage.success('订单创建成功，即将跳转到支付页面')

      // 发起支付
      const paymentResult = await orderStore.initiatePayment(result.order.id)

      if (paymentResult.success) {
        // 在新窗口打开支付页面
        window.open(paymentResult.paymentUrl, '_blank')
        ElMessage.success('支付页面已在新窗口打开，请完成支付')
      } else {
        ElMessage.error(paymentResult.message || '发起支付失败')
      }
    } else {
      ElMessage.error(result.message || '创建订单失败')
    }
  } catch (error) {
    console.error('购买过程中发生错误', error)
    ElMessage.error('购买过程中发生错误')
  }
}

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.back()
  } else {
    router.push('/products')
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchProductDetail()

  // 滚动到页面顶部
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
})
</script>

<style scoped>
.product-detail-page {
  max-width: var(--container-max-width);
  margin: 0 auto;
}

.back-link {
  margin-bottom: var(--spacing-6);
}

.loading-container {
  padding: var(--spacing-10) 0;
}

.product-not-found {
  padding: var(--spacing-16) 0;
  text-align: center;
}

.product-detail {
  margin-bottom: var(--spacing-10);
}

.product-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-8);
}

.product-image-section {
  position: sticky;
  top: 100px;
  align-self: start;
}

.product-image-container {
  position: relative;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: box-shadow var(--transition-normal) ease;
}

.product-image-container:hover {
  box-shadow: var(--shadow-lg);
}

.product-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform var(--transition-normal) ease;
}

.product-image-container:hover .product-image {
  transform: scale(1.02);
}

.product-tag {
  position: absolute;
  top: var(--spacing-4);
  right: var(--spacing-4);
  z-index: 1;
}

.product-info-section {
  display: flex;
  flex-direction: column;
}

.product-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-4) 0;
  line-height: 1.2;
}

.product-meta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-6);
  padding-bottom: var(--spacing-6);
  border-bottom: 1px solid var(--border-color);
}

.product-price {
  display: flex;
  align-items: center;
}

.price-value {
  color: var(--accent-color);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
}

.product-stock {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--text-secondary);
  font-size: var(--font-size-base);
}

.product-stock.low-stock {
  color: var(--warning-color);
}

.product-description {
  margin: var(--spacing-6) 0;
}

.product-description h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-3);
}

.product-description p {
  line-height: 1.8;
  color: var(--text-secondary);
  white-space: pre-line;
}

.product-features {
  margin: var(--spacing-6) 0;
}

.product-features h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-3);
}

.product-features ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.product-features li {
  position: relative;
  padding-left: var(--spacing-6);
  margin-bottom: var(--spacing-3);
  line-height: 1.6;
  color: var(--text-secondary);
}

.product-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--success-color);
  font-weight: var(--font-weight-bold);
}

.product-actions {
  display: flex;
  gap: var(--spacing-4);
  margin-top: var(--spacing-8);
  padding-top: var(--spacing-6);
  border-top: 1px solid var(--border-color);
}

@media (max-width: 992px) {
  .product-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
  }

  .product-image-section {
    position: relative;
    top: 0;
  }

  .product-title {
    font-size: var(--font-size-2xl);
  }

  .price-value {
    font-size: var(--font-size-2xl);
  }
}

@media (max-width: 576px) {
  .product-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-3);
  }

  .product-actions {
    flex-direction: column;
  }
}
</style>
