from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.services.payment import payment_service

router = APIRouter()


@router.post("/notify")
async def alipay_notify(request: Request, db: AsyncSession = Depends(get_db)):
    """
    支付宝异步通知接口

    Args:
        request: 请求对象
        db: 数据库会话

    Returns:
        str: 处理结果
    """
    try:
        # 获取请求数据
        data = await request.form()
        data_dict = dict(data)

        # 处理支付回调
        result = await payment_service.handle_payment_notification(db, data_dict)

        # 返回处理结果
        if result:
            return "success"  # 支付宝要求返回字符串 "success"
        else:
            return "fail"
    except Exception as e:
        # 记录错误日志
        print(f"支付回调处理失败: {str(e)}")
        return "fail"