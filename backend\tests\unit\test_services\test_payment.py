import pytest
from unittest.mock import patch, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime
import uuid
import json

from app.models.order import Order, OrderItem, OrderStatus, LicenseKey
from app.models.product import Product
from app.models.user import User
from app.services.payment import PaymentService
from app.core.config import settings

pytestmark = pytest.mark.asyncio


async def test_generate_payment_url(db_session: AsyncSession):
    """测试生成支付URL"""
    # 创建测试用户
    user = User(
        username="paymentuser",
        email="<EMAIL>",
        hashed_password="hashed_password",
        is_active=True,
        is_admin=False,
        address="Payment Address"
    )
    db_session.add(user)
    await db_session.flush()

    # 创建测试产品
    product = Product(
        name="Payment Product",
        description="Payment Description",
        price=100.0,
        image_url="payment.jpg",
        is_active=True,
        product_type="software"
    )
    db_session.add(product)
    await db_session.flush()

    # 创建订单
    order = Order(
        user_id=user.id,
        order_number=f"ORD-{int(datetime.now().timestamp())}-{user.id}-{uuid.uuid4().hex[:4]}",
        status=OrderStatus.PENDING,
        total_amount=100.0
    )
    db_session.add(order)
    await db_session.flush()

    # 创建订单项
    order_item = OrderItem(
        order_id=order.id,
        product_id=product.id,
        quantity=1,
        price=100.0
    )
    db_session.add(order_item)
    await db_session.commit()

    # 使用mock替代真实的支付宝SDK
    with patch('app.services.payment.AliPay') as mock_alipay:
        # 配置mock对象
        mock_alipay_instance = MagicMock()
        mock_alipay_instance.api_alipay_trade_page_pay.return_value = "https://openapi.alipaydev.com/gateway.do?biz_content=..."
        mock_alipay.return_value = mock_alipay_instance

        # 调用支付服务生成支付URL
        payment_service = PaymentService(db_session)
        payment_url = await payment_service.generate_payment_url(order.id, user.id)

        # 验证支付URL是否生成成功
        assert payment_url is not None
        assert "https://openapi.alipaydev.com/gateway.do" in payment_url

        # 验证支付宝SDK是否被正确调用
        mock_alipay_instance.api_alipay_trade_page_pay.assert_called_once()
        call_args = mock_alipay_instance.api_alipay_trade_page_pay.call_args[1]
        assert call_args["out_trade_no"] == order.order_number
        assert float(call_args["total_amount"]) == order.total_amount
        assert call_args["subject"] == "Payment Product"


async def test_process_payment_notification(db_session: AsyncSession):
    """测试处理支付通知"""
    # 创建测试用户
    user = User(
        username="notifyuser",
        email="<EMAIL>",
        hashed_password="hashed_password",
        is_active=True,
        is_admin=False,
        address="Notify Address"
    )
    db_session.add(user)
    await db_session.flush()

    # 创建测试产品
    product = Product(
        name="Notify Product",
        description="Notify Description",
        price=200.0,
        image_url="notify.jpg",
        is_active=True,
        product_type="software"
    )
    db_session.add(product)
    await db_session.flush()

    # 创建订单
    order_number = f"ORD-{int(datetime.now().timestamp())}-{user.id}-{uuid.uuid4().hex[:4]}"
    order = Order(
        user_id=user.id,
        order_number=order_number,
        status=OrderStatus.PENDING,
        total_amount=200.0
    )
    db_session.add(order)
    await db_session.flush()

    # 创建订单项
    order_item = OrderItem(
        order_id=order.id,
        product_id=product.id,
        quantity=1,
        price=200.0
    )
    db_session.add(order_item)
    await db_session.commit()

    # 模拟支付宝通知数据
    notify_data = {
        "out_trade_no": order_number,
        "trade_no": "2023112122001473981234567890",
        "trade_status": "TRADE_SUCCESS",
        "total_amount": "200.00",
        "gmt_payment": "2023-11-21 12:34:56"
    }

    # 使用mock替代真实的支付宝SDK
    with patch('app.services.payment.AliPay') as mock_alipay:
        # 配置mock对象
        mock_alipay_instance = MagicMock()
        mock_alipay_instance.verify.return_value = notify_data
        mock_alipay.return_value = mock_alipay_instance

        # 调用支付服务处理通知
        payment_service = PaymentService(db_session)
        result = await payment_service.process_payment_notification(notify_data)

        # 验证处理结果
        assert result is True

        # 验证订单状态是否更新
        updated_order = await db_session.get(Order, order.id)
        assert updated_order.status == OrderStatus.PAID
        assert updated_order.payment_id == "2023112122001473981234567890"

        # 验证是否生成了许可密钥
        license_keys = await payment_service.get_license_keys_by_user(user.id)
        assert len(license_keys) > 0
        assert license_keys[0].user_id == user.id
        assert license_keys[0].product_id == product.id
        assert license_keys[0].order_id == order.id


async def test_check_payment_status(db_session: AsyncSession):
    """测试查询支付状态"""
    # 创建测试用户
    user = User(
        username="statususer",
        email="<EMAIL>",
        hashed_password="hashed_password",
        is_active=True,
        is_admin=False,
        address="Status Address"
    )
    db_session.add(user)
    await db_session.flush()

    # 创建测试产品
    product = Product(
        name="Status Product",
        description="Status Description",
        price=300.0,
        image_url="status.jpg",
        is_active=True,
        product_type="software"
    )
    db_session.add(product)
    await db_session.flush()

    # 创建订单
    order_number = f"ORD-{int(datetime.now().timestamp())}-{user.id}-{uuid.uuid4().hex[:4]}"
    order = Order(
        user_id=user.id,
        order_number=order_number,
        status=OrderStatus.PENDING,
        total_amount=300.0
    )
    db_session.add(order)
    await db_session.flush()

    # 创建订单项
    order_item = OrderItem(
        order_id=order.id,
        product_id=product.id,
        quantity=1,
        price=300.0
    )
    db_session.add(order_item)
    await db_session.commit()

    # 使用mock替代真实的支付宝SDK
    with patch('app.services.payment.AliPay') as mock_alipay:
        # 配置mock对象
        mock_alipay_instance = MagicMock()
        mock_alipay_instance.api_alipay_trade_query.return_value = {
            "code": "10000",
            "msg": "Success",
            "trade_no": "2023112122001473981234567891",
            "out_trade_no": order_number,
            "trade_status": "TRADE_SUCCESS",
            "total_amount": "300.00",
            "gmt_payment": "2023-11-21 13:45:56"
        }
        mock_alipay.return_value = mock_alipay_instance

        # 调用支付服务查询支付状态
        payment_service = PaymentService(db_session)
        status = await payment_service.check_payment_status(order.id)

        # 验证状态查询结果
        assert status is not None
        assert status["status"] == "paid"
        assert status["payment_id"] == "2023112122001473981234567891"

        # 验证订单状态是否更新
        updated_order = await db_session.get(Order, order.id)
        assert updated_order.status == OrderStatus.PAID
        assert updated_order.payment_id == "2023112122001473981234567891"


async def test_mock_payment_success(db_session: AsyncSession):
    """测试模拟支付成功"""
    # 创建测试用户
    user = User(
        username="mockuser",
        email="<EMAIL>",
        hashed_password="hashed_password",
        is_active=True,
        is_admin=False,
        address="Mock Address"
    )
    db_session.add(user)
    await db_session.flush()

    # 创建测试产品
    product = Product(
        name="Mock Product",
        description="Mock Description",
        price=400.0,
        image_url="mock.jpg",
        is_active=True,
        product_type="software"
    )
    db_session.add(product)
    await db_session.flush()

    # 创建订单
    order = Order(
        user_id=user.id,
        order_number=f"ORD-{int(datetime.now().timestamp())}-{user.id}-{uuid.uuid4().hex[:4]}",
        status=OrderStatus.PENDING,
        total_amount=400.0
    )
    db_session.add(order)
    await db_session.flush()

    # 创建订单项
    order_item = OrderItem(
        order_id=order.id,
        product_id=product.id,
        quantity=1,
        price=400.0
    )
    db_session.add(order_item)
    await db_session.commit()

    # 调用支付服务模拟支付成功
    payment_service = PaymentService(db_session)
    result = await payment_service.mock_payment_success(order.id)

    # 验证模拟支付结果
    assert result is True

    # 验证订单状态是否更新
    updated_order = await db_session.get(Order, order.id)
    assert updated_order.status == OrderStatus.PAID
    assert updated_order.payment_method == "mock"

    # 验证是否生成了许可密钥
    license_keys = await payment_service.get_license_keys_by_user(user.id)
    assert len(license_keys) > 0
    assert license_keys[0].user_id == user.id
    assert license_keys[0].product_id == product.id
    assert license_keys[0].order_id == order.id
