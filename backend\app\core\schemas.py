from typing import Any, Generic, Optional, TypeVar

from pydantic import BaseModel, Field

# 定义泛型类型变量
T = TypeVar("T")


class ResponseModel(BaseModel, Generic[T]):
    """
    统一响应模型

    Attributes:
        success: 操作是否成功
        message: 响应消息
        data: 响应数据
    """
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")


class PaginatedResponseModel(ResponseModel, Generic[T]):
    """
    分页响应模型

    Attributes:
        success: 操作是否成功
        message: 响应消息
        data: 响应数据
        total: 总记录数
        page: 当前页码
        page_size: 每页记录数
    """
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页记录数")