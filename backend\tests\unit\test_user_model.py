"""
用户模型单元测试
"""
import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate
from app.services.user import user_service
from app.core.security import verify_password


@pytest_asyncio.fixture(scope="function")
async def test_user(db: AsyncSession) -> User:
    """创建测试用户"""
    user_create = UserCreate(
        username="testuser",
        email="<EMAIL>",
        password="password123",
    )
    user = await user_service.create_user(db, user_create)
    return user


@pytest_asyncio.fixture(scope="function")
async def admin_user(db: AsyncSession) -> User:
    """创建管理员用户"""
    user_create = UserCreate(
        username="admin",
        email="<EMAIL>",
        password="admin123",
    )
    user = await user_service.create_user(db, user_create)
    # 设置为管理员
    user.role = "admin"
    db.add(user)
    await db.commit()
    await db.refresh(user)
    return user


@pytest.mark.asyncio
async def test_create_user(db: AsyncSession):
    """测试创建用户"""
    # 创建用户
    user_create = UserCreate(
        username="testuser",
        email="<EMAIL>",
        password="password123"
    )
    user = await user_service.create_user(db, user_create)

    # 验证用户字段
    assert user.username == "testuser"
    assert user.email == "<EMAIL>"
    assert verify_password("password123", user.password_hash)
    assert user.is_active is True
    assert user.role == "user"
    assert user.address is None


@pytest.mark.asyncio
async def test_get_user_by_id(db: AsyncSession, test_user: User):
    """测试通过ID获取用户"""
    # 获取用户
    users = await user_service.get_users(db, skip=0, limit=10)
    user = next((u for u in users if u.id == test_user.id), None)

    # 验证用户字段
    assert user is not None
    assert user.id == test_user.id
    assert user.username == test_user.username
    assert user.email == test_user.email


@pytest.mark.asyncio
async def test_get_user_by_username(db: AsyncSession, test_user: User):
    """测试通过用户名获取用户"""
    # 获取用户
    user = await user_service.get_user_by_username(db, test_user.username)

    # 验证用户字段
    assert user is not None
    assert user.id == test_user.id
    assert user.username == test_user.username
    assert user.email == test_user.email


@pytest.mark.asyncio
async def test_get_user_by_email(db: AsyncSession, test_user: User):
    """测试通过邮箱获取用户"""
    # 获取用户
    user = await user_service.get_user_by_email(db, test_user.email)

    # 验证用户字段
    assert user is not None
    assert user.id == test_user.id
    assert user.username == test_user.username
    assert user.email == test_user.email


@pytest.mark.asyncio
async def test_get_users(db: AsyncSession, test_user: User, admin_user: User):
    """测试获取用户列表"""
    # 获取用户列表
    users = await user_service.get_users(db)

    # 验证用户列表
    assert len(users) == 2
    assert any(user.username == test_user.username for user in users)
    assert any(user.username == admin_user.username for user in users)


@pytest.mark.asyncio
async def test_update_user_username(db: AsyncSession, test_user: User):
    """测试更新用户名"""
    # 更新用户名
    user_update = UserUpdate(username="newusername")
    user = await user_service.update_user(db, test_user.id, user_update)

    # 验证用户字段
    assert user.username == "newusername"
    assert user.email == test_user.email
    assert user.address == test_user.address


@pytest.mark.asyncio
async def test_update_user_email(db: AsyncSession, test_user: User):
    """测试更新邮箱"""
    # 更新邮箱
    user_update = UserUpdate(email="<EMAIL>")
    user = await user_service.update_user(db, test_user.id, user_update)

    # 验证用户字段
    assert user.username == test_user.username
    assert user.email == "<EMAIL>"
    assert user.address == test_user.address


@pytest.mark.asyncio
async def test_update_user_address(db: AsyncSession, test_user: User):
    """测试更新地址"""
    # 更新地址
    user_update = UserUpdate(address="123 Test St")
    user = await user_service.update_user(db, test_user.id, user_update)

    # 验证用户字段
    assert user.username == test_user.username
    assert user.email == test_user.email
    assert user.address == "123 Test St"


@pytest.mark.asyncio
async def test_update_user_multiple_fields(db: AsyncSession, test_user: User):
    """测试更新多个字段"""
    # 更新多个字段
    user_update = UserUpdate(
        username="newusername",
        email="<EMAIL>",
        address="123 Test St"
    )
    user = await user_service.update_user(db, test_user.id, user_update)

    # 验证用户字段
    assert user.username == "newusername"
    assert user.email == "<EMAIL>"
    assert user.address == "123 Test St"


@pytest.mark.asyncio
async def test_authenticate_user_success(db: AsyncSession, test_user: User):
    """测试用户认证成功"""
    # 认证用户
    user = await user_service.authenticate_user(
        db, test_user.username, "password123"
    )

    # 验证用户字段
    assert user is not None
    assert user.id == test_user.id
    assert user.username == test_user.username
    assert user.email == test_user.email


@pytest.mark.asyncio
async def test_authenticate_user_wrong_password(db: AsyncSession, test_user: User):
    """测试用户认证失败（密码错误）"""
    # 认证用户
    user = await user_service.authenticate_user(
        db, test_user.username, "wrongpassword"
    )

    # 验证用户为空
    assert user is None


@pytest.mark.asyncio
async def test_authenticate_user_not_found(db: AsyncSession):
    """测试用户认证失败（用户不存在）"""
    # 认证用户
    user = await user_service.authenticate_user(
        db, "nonexistentuser", "password123"
    )

    # 验证用户为空
    assert user is None
