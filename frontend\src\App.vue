<template>
  <div class="app-container">
    <el-config-provider>
      <Navbar />
      <div class="content-container">
        <router-view v-slot="{ Component }">
          <transition name="page" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </div>
      <Footer />
    </el-config-provider>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import Navbar from './components/Navbar.vue'
import Footer from './components/layout/Footer.vue'
import { useSiteStore } from './store'

const siteStore = useSiteStore()

// 在应用启动时获取站点信息
onMounted(async () => {
  if (!siteStore.isLoaded) {
    await siteStore.fetchSiteInfo()
  }
})
</script>

<style>
.app-container {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.content-container {
  flex: 1;
  padding-top: 80px; /* 为固定导航栏留出空间 */
  min-height: calc(100vh - 80px - 300px); /* 减去导航栏和页脚的高度 */
}

/* 页面过渡动画 */
.page-enter-active,
.page-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}
</style>