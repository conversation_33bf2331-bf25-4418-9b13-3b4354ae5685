# 生产环境 Docker Compose 配置
# 使用方法: docker-compose -f docker-compose.prod.yml up -d

version: '3.8'

services:
  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: sales_platform_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - frontend_dist:/usr/share/nginx/html:ro
      - backend_static:/usr/share/nginx/static:ro
    depends_on:
      - backend
      - frontend
    restart: always
    networks:
      - sales_platform_network

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: sales_platform_backend
    expose:
      - "8000"
    volumes:
      - backend_static:/app/static
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
      - ./backend/keys:/app/keys:ro
    depends_on:
      - database
      - redis
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@database:5432/${POSTGRES_DB}
      - REDIS_URL=redis://redis:6379/0
    env_file:
      - .env
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - sales_platform_network

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    container_name: sales_platform_frontend
    volumes:
      - frontend_dist:/app/dist
    environment:
      - NODE_ENV=production
      - VITE_API_BASE_URL=https://${DOMAIN}/api/v1
    env_file:
      - .env
    restart: always
    networks:
      - sales_platform_network

  # 数据库服务
  database:
    image: postgres:16-alpine
    container_name: sales_platform_database
    volumes:
      - db_data_prod:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d:ro
      - db_backups:/backups
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    env_file:
      - .env
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    networks:
      - sales_platform_network

  # Redis 缓存服务
  redis:
    image: redis:7-alpine
    container_name: sales_platform_redis
    command: redis-server --requirepass ${REDIS_PASSWORD} --appendonly yes
    volumes:
      - redis_data:/data
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - sales_platform_network

  # 数据库备份服务
  db_backup:
    image: postgres:16-alpine
    container_name: sales_platform_backup
    volumes:
      - db_backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_HOST=database
      - BACKUP_RETENTION_DAYS=30
    depends_on:
      - database
    command: /bin/sh -c "chmod +x /backup.sh && crond -f"
    restart: always
    networks:
      - sales_platform_network

  # 监控服务 (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: sales_platform_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: always
    networks:
      - sales_platform_network

  # Grafana 监控面板 (可选)
  grafana:
    image: grafana/grafana:latest
    container_name: sales_platform_grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    depends_on:
      - prometheus
    restart: always
    networks:
      - sales_platform_network

volumes:
  db_data_prod:
    name: sales_platform_db_data_prod
  db_backups:
    name: sales_platform_db_backups
  redis_data:
    name: sales_platform_redis_data
  backend_static:
    name: sales_platform_backend_static
  backend_uploads:
    name: sales_platform_backend_uploads
  backend_logs:
    name: sales_platform_backend_logs
  frontend_dist:
    name: sales_platform_frontend_dist
  prometheus_data:
    name: sales_platform_prometheus_data
  grafana_data:
    name: sales_platform_grafana_data

networks:
  sales_platform_network:
    name: sales_platform_network
    driver: bridge