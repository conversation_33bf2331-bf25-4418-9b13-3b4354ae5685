import { defineStore } from 'pinia'
import api from '../api'

// 导出订单状态
export * from './order'

// 站点信息状态
export const useSiteStore = defineStore('site', {
  state: () => ({
    siteInfo: {
      company_name: '软件和设备销售平台',
      description: '提供高质量的软件和设备销售服务',
      contact_info: '',
      logo_url: ''
    },
    isLoaded: false
  }),
  actions: {
    // 获取站点信息
    async fetchSiteInfo() {
      try {
        const response = await api.site.getSiteInfo()
        if (response.data.success) {
          this.siteInfo = response.data.data
          this.isLoaded = true
          return { success: true }
        }
        return { success: false, message: response.data.message || '获取站点信息失败' }
      } catch (error) {
        console.error('获取站点信息失败:', error)
        return { success: false, message: error.response?.data?.message || '获取站点信息失败' }
      }
    }
  },
  persist: {
    key: 'site-store',
    storage: localStorage,
    paths: ['siteInfo', 'isLoaded']
  }
})

// 用户状态
export const useUserStore = defineStore('user', {
  state: () => ({
    token: null,
    user: null,
    isLoggedIn: false
  }),
  getters: {
    isAdmin: (state) => state.user?.role === 'admin',
    username: (state) => state.user?.username
  },
  actions: {
    // 登录
    async login(username, password) {
      try {
        const response = await api.auth.login({ username, password })
        this.token = response.data.access_token
        this.isLoggedIn = true
        await this.fetchUserInfo()
        return { success: true }
      } catch (error) {
        return { success: false, message: error.response?.data?.message || '登录失败' }
      }
    },

    // 注册
    async register(userData) {
      try {
        await api.auth.register(userData)
        return { success: true }
      } catch (error) {
        return { success: false, message: error.response?.data?.message || '注册失败' }
      }
    },

    // 获取用户信息
    async fetchUserInfo() {
      try {
        const response = await api.user.getProfile()
        this.user = response.data
        return { success: true }
      } catch (error) {
        return { success: false, message: error.response?.data?.message || '获取用户信息失败' }
      }
    },

    // 登出
    logout() {
      this.token = null
      this.user = null
      this.isLoggedIn = false
    }
  },
  persist: {
    key: 'user-store',
    storage: localStorage,
    paths: ['token', 'isLoggedIn', 'user']
  }
})