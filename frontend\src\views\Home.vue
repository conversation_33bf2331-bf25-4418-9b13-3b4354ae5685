<template>
  <PageContainer customClass="home-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">{{ siteInfo.company_name || '软件和设备销售平台' }}</h1>
        <p class="hero-description">{{ siteInfo.description || '提供高质量的软件和设备销售服务' }}</p>
        <div class="hero-actions">
          <AppButton type="primary" size="large" round @click="$router.push('/products')">
            浏览产品
            <el-icon class="ml-2"><ArrowRight /></el-icon>
          </AppButton>
          <AppButton plain size="large" round @click="scrollToSection('features')">
            了解更多
            <el-icon class="ml-2"><InfoFilled /></el-icon>
          </AppButton>
        </div>
      </div>
      <div class="hero-image">
        <img src="https://images.unsplash.com/photo-1581092918056-0c4c3acd3789?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80" alt="Hero Image" />
      </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features-section">
      <h2 class="section-title">我们的优势</h2>
      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">
            <el-icon><GoodsFilled /></el-icon>
          </div>
          <h3>优质产品</h3>
          <p>我们提供高品质的软件和硬件产品，满足您的各种需求。</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">
            <el-icon><Service /></el-icon>
          </div>
          <h3>专业服务</h3>
          <p>专业的技术团队为您提供全方位的技术支持和服务。</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">
            <el-icon><Money /></el-icon>
          </div>
          <h3>合理价格</h3>
          <p>我们提供具有竞争力的价格，确保您获得最佳的价值。</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">
            <el-icon><Lock /></el-icon>
          </div>
          <h3>安全保障</h3>
          <p>所有交易和数据都受到严格保护，确保您的信息安全。</p>
        </div>
      </div>
    </section>

    <!-- Products Section -->
    <section class="products-section">
      <div class="section-header">
        <h2 class="section-title">热门产品</h2>
        <AppButton type="text" @click="$router.push('/products')">
          查看全部
          <el-icon class="ml-1"><ArrowRight /></el-icon>
        </AppButton>
      </div>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>

      <div v-else-if="products.length === 0" class="empty-container">
        <el-empty description="暂无产品" />
      </div>

      <div v-else class="products-grid">
        <AppCard
          v-for="product in displayedProducts"
          :key="product.id"
          hover
          customClass="product-card"
          @click="goToProductDetail(product.id)"
        >
          <div class="product-image-container">
            <img
              :src="product.image_url || 'https://via.placeholder.com/300x200?text=' + product.name"
              class="product-image"
              :alt="product.name"
            />
          </div>
          <h3 class="product-title">{{ product.name }}</h3>
          <p class="product-description">{{ product.description }}</p>
          <div class="product-footer">
            <div class="product-price">¥{{ product.price }}</div>
            <AppButton type="primary" round @click.stop="goToProductDetail(product.id)">
              查看详情
            </AppButton>
          </div>
        </AppCard>
      </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials-section">
      <h2 class="section-title">客户评价</h2>
      <div class="testimonials-grid">
        <div class="testimonial-card">
          <div class="testimonial-content">
            <p>"这个平台提供的软件产品质量非常高，售后服务也很到位。"</p>
          </div>
          <div class="testimonial-author">
            <el-avatar :size="40">张</el-avatar>
            <div class="author-info">
              <h4>张先生</h4>
              <p>企业客户</p>
            </div>
          </div>
        </div>
        <div class="testimonial-card">
          <div class="testimonial-content">
            <p>"价格合理，产品种类丰富，购买流程简单便捷，非常满意。"</p>
          </div>
          <div class="testimonial-author">
            <el-avatar :size="40">李</el-avatar>
            <div class="author-info">
              <h4>李女士</h4>
              <p>个人用户</p>
            </div>
          </div>
        </div>
        <div class="testimonial-card">
          <div class="testimonial-content">
            <p>"技术支持响应迅速，解决问题专业高效，值得信赖的平台。"</p>
          </div>
          <div class="testimonial-author">
            <el-avatar :size="40">王</el-avatar>
            <div class="author-info">
              <h4>王先生</h4>
              <p>技术总监</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
      <div class="cta-content">
        <h2>准备好开始了吗？</h2>
        <p>浏览我们的产品目录，找到适合您需求的软件和设备。</p>
        <AppButton type="primary" size="large" round @click="$router.push('/products')">
          立即购买
        </AppButton>
      </div>
    </section>
  </PageContainer>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useProductStore } from '../store/product'
import { useSiteStore } from '../store'
import api from '../api'
import PageContainer from '../components/layout/PageContainer.vue'
import {
  ArrowRight,
  InfoFilled,
  GoodsFilled,
  Service,
  Money,
  Lock
} from '@element-plus/icons-vue'

const router = useRouter()
const productStore = useProductStore()
const siteStore = useSiteStore()

// 站点信息
const siteInfo = computed(() => siteStore.siteInfo)

// 产品列表
const products = ref([])
const loading = ref(true)

// 只显示前4个产品
const displayedProducts = computed(() => {
  return products.value.slice(0, 4)
})

// 获取产品列表
const fetchProducts = async () => {
  loading.value = true
  try {
    const result = await productStore.fetchProducts()
    if (result.success) {
      products.value = productStore.products
    }
  } catch (error) {
    console.error('获取产品列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 跳转到产品详情页
const goToProductDetail = (productId) => {
  router.push(`/products/${productId}`)
}

// 滚动到指定区域
const scrollToSection = (sectionId) => {
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchProducts()
})
</script>

<style scoped>
.home-page {
  max-width: 100% !important;
  padding: 0 !important;
}

/* Hero Section */
.hero-section {
  display: flex;
  align-items: center;
  min-height: 600px;
  padding: var(--spacing-10) var(--container-padding);
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  position: relative;
  overflow: hidden;
}

.hero-content {
  flex: 1;
  max-width: 600px;
  z-index: 1;
  padding: var(--spacing-8);
}

.hero-title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-4);
  color: var(--text-primary);
  line-height: 1.2;
}

.hero-description {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-6);
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-4);
  flex-wrap: wrap;
}

.hero-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
}

.hero-image img {
  max-width: 100%;
  max-height: 500px;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  transform: perspective(1000px) rotateY(-5deg);
  transition: transform var(--transition-normal) ease;
}

.hero-image img:hover {
  transform: perspective(1000px) rotateY(0deg);
}

/* Features Section */
.features-section {
  padding: var(--spacing-16) var(--container-padding);
  background-color: var(--bg-primary);
  text-align: center;
}

.section-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-10);
  color: var(--text-primary);
  position: relative;
  display: inline-block;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: var(--border-radius-full);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-6);
  max-width: var(--container-max-width);
  margin: 0 auto;
}

.feature-card {
  padding: var(--spacing-6);
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-normal) ease, box-shadow var(--transition-normal) ease;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.feature-icon {
  font-size: 48px;
  color: var(--primary-color);
  margin-bottom: var(--spacing-4);
}

.feature-card h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-3);
  color: var(--text-primary);
}

.feature-card p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Products Section */
.products-section {
  padding: var(--spacing-16) var(--container-padding);
  background-color: var(--bg-secondary);
  max-width: var(--container-max-width);
  margin: 0 auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-8);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-6);
}

.product-card {
  cursor: pointer;
  height: 100%;
}

.product-image-container {
  overflow: hidden;
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-4);
  aspect-ratio: 16 / 9;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal) ease;
}

.product-card:hover .product-image {
  transform: scale(1.05);
}

.product-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-2);
  color: var(--text-primary);
}

.product-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-4);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.product-price {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--accent-color);
}

.loading-container, .empty-container {
  padding: var(--spacing-10) 0;
  text-align: center;
}

/* Testimonials Section */
.testimonials-section {
  padding: var(--spacing-16) var(--container-padding);
  background-color: var(--bg-primary);
  text-align: center;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-6);
  max-width: var(--container-max-width);
  margin: 0 auto;
}

.testimonial-card {
  padding: var(--spacing-6);
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  text-align: left;
}

.testimonial-content {
  margin-bottom: var(--spacing-4);
}

.testimonial-content p {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  line-height: 1.6;
  font-style: italic;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.author-info h4 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  margin: 0;
  color: var(--text-primary);
}

.author-info p {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
}

/* CTA Section */
.cta-section {
  padding: var(--spacing-16) var(--container-padding);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  text-align: center;
  border-radius: var(--border-radius-lg);
  margin: var(--spacing-16) auto;
  max-width: calc(var(--container-max-width) - 2 * var(--container-padding));
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.cta-section h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-light);
  margin-bottom: var(--spacing-4);
}

.cta-section p {
  font-size: var(--font-size-lg);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--spacing-6);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .hero-section {
    flex-direction: column;
    text-align: center;
  }

  .hero-content {
    max-width: 100%;
    padding: var(--spacing-6) 0;
  }

  .hero-actions {
    justify-content: center;
  }

  .hero-image {
    margin-top: var(--spacing-6);
  }

  .hero-title {
    font-size: var(--font-size-4xl);
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: var(--font-size-3xl);
  }

  .hero-description {
    font-size: var(--font-size-lg);
  }

  .section-title {
    font-size: var(--font-size-2xl);
  }

  .features-grid, .products-grid, .testimonials-grid {
    grid-template-columns: 1fr;
  }

  .cta-section h2 {
    font-size: var(--font-size-2xl);
  }
}
</style>
