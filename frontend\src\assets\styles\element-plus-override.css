@import './variables.css';

/* Element Plus 组件样式覆盖 */

/* 按钮 */
.el-button {
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-fast) ease-in-out;
}

.el-button--primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.el-button--primary:hover,
.el-button--primary:focus {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.el-button--success {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.el-button--success:hover,
.el-button--success:focus {
  background-color: var(--secondary-dark);
  border-color: var(--secondary-dark);
}

.el-button--danger {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.el-button--danger:hover,
.el-button--danger:focus {
  background-color: var(--accent-dark);
  border-color: var(--accent-dark);
}

/* 卡片 */
.el-card {
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm) !important;
  transition: box-shadow var(--transition-fast) ease-in-out;
}

.el-card:hover {
  box-shadow: var(--shadow-md) !important;
}

.el-card__header {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  font-weight: var(--font-weight-medium);
}

.el-card__body {
  padding: var(--spacing-4);
}

/* 表单 */
.el-form-item__label {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.el-input__inner {
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
}

.el-input__inner:focus {
  border-color: var(--primary-color);
}

.el-input__inner:hover {
  border-color: var(--neutral-500);
}

.el-textarea__inner {
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
}

.el-textarea__inner:focus {
  border-color: var(--primary-color);
}

.el-textarea__inner:hover {
  border-color: var(--neutral-500);
}

/* 表格 */
.el-table {
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.el-table th {
  background-color: var(--bg-secondary);
  font-weight: var(--font-weight-semibold);
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: var(--bg-secondary);
}

.el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: var(--bg-tertiary);
}

/* 分页 */
.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: var(--primary-color);
}

.el-pagination.is-background .el-pager li:not(.disabled):hover {
  color: var(--primary-color);
}

/* 标签 */
.el-tag {
  border-radius: var(--border-radius-sm);
  font-weight: var(--font-weight-medium);
}

/* 对话框 */
.el-dialog {
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
}

.el-dialog__header {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
}

.el-dialog__title {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.el-dialog__body {
  padding: var(--spacing-4);
}

.el-dialog__footer {
  padding: var(--spacing-4);
  border-top: 1px solid var(--border-color);
}

/* 消息提示 */
.el-message {
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
}

.el-message--success {
  background-color: rgba(46, 204, 113, 0.1);
  border-color: var(--success-color);
}

.el-message--warning {
  background-color: rgba(243, 156, 18, 0.1);
  border-color: var(--warning-color);
}

.el-message--error {
  background-color: rgba(231, 76, 60, 0.1);
  border-color: var(--danger-color);
}

.el-message--info {
  background-color: rgba(52, 152, 219, 0.1);
  border-color: var(--info-color);
}

/* 下拉菜单 */
.el-dropdown-menu {
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
}

.el-dropdown-menu__item:hover {
  background-color: var(--bg-secondary);
  color: var(--primary-color);
}

/* 选择器 */
.el-select .el-input__inner {
  border-radius: var(--border-radius-md);
}

.el-select-dropdown {
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
}

.el-select-dropdown__item.selected {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

/* 日期选择器 */
.el-date-picker {
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
}

.el-date-table td.current:not(.disabled) span {
  background-color: var(--primary-color);
}

.el-date-table td.today span {
  color: var(--primary-color);
}

/* 开关 */
.el-switch.is-checked .el-switch__core {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* 加载中 */
.el-loading-spinner .path {
  stroke: var(--primary-color);
}

/* 步骤条 */
.el-step__head.is-process {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.el-step__title.is-process {
  color: var(--text-primary);
}

.el-step__head.is-finish {
  color: var(--success-color);
  border-color: var(--success-color);
}

.el-step__title.is-finish {
  color: var(--text-primary);
}

/* 标签页 */
.el-tabs__item.is-active {
  color: var(--primary-color);
}

.el-tabs__active-bar {
  background-color: var(--primary-color);
}
