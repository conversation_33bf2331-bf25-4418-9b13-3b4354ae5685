import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
})

// 存储API请求性能数据
const apiPerformanceData = []

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 添加请求开始时间戳
    config.metadata = { startTime: new Date().getTime() }

    // 添加请求ID
    config.requestId = generateRequestId()

    // 添加认证令牌
    const token = localStorage.getItem('user-store')
      ? JSON.parse(localStorage.getItem('user-store')).token
      : null

    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }

    // 开发环境下记录请求信息
    if (process.env.NODE_ENV === 'development') {
      console.log(`🚀 API请求: ${config.method.toUpperCase()} ${config.url}`, config)
    }

    return config
  },
  error => {
    console.error('❌ 请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    // 计算请求耗时
    const endTime = new Date().getTime()
    const startTime = response.config.metadata.startTime
    const duration = endTime - startTime

    // 记录API性能数据
    const performanceEntry = {
      requestId: response.config.requestId,
      url: response.config.url,
      method: response.config.method.toUpperCase(),
      status: response.status,
      duration: duration,
      timestamp: new Date().toISOString()
    }

    apiPerformanceData.push(performanceEntry)

    // 如果请求时间过长，记录警告
    if (duration > 1000) {
      console.warn(`⚠️ 慢请求: ${response.config.method.toUpperCase()} ${response.config.url} 耗时 ${duration}ms`)
    }

    // 开发环境下记录响应信息
    if (process.env.NODE_ENV === 'development') {
      console.log(`✅ API响应: ${response.config.method.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        duration: `${duration}ms`,
        data: response.data
      })
    }

    // 如果响应成功，直接返回响应对象
    return response
  },
  error => {
    // 计算请求耗时（即使请求失败）
    if (error.config && error.config.metadata) {
      const endTime = new Date().getTime()
      const startTime = error.config.metadata.startTime
      const duration = endTime - startTime

      // 记录API错误性能数据
      const performanceEntry = {
        requestId: error.config.requestId,
        url: error.config.url,
        method: error.config.method.toUpperCase(),
        status: error.response ? error.response.status : 0,
        duration: duration,
        error: error.message,
        timestamp: new Date().toISOString()
      }

      apiPerformanceData.push(performanceEntry)

      // 开发环境下记录错误信息
      if (process.env.NODE_ENV === 'development') {
        console.error(`❌ API错误: ${error.config.method.toUpperCase()} ${error.config.url}`, {
          status: error.response ? error.response.status : 'Network Error',
          duration: `${duration}ms`,
          error: error.message,
          response: error.response ? error.response.data : null
        })
      }
    }

    // 处理401错误（未授权）
    if (error.response && error.response.status === 401) {
      // 清除本地存储的token
      localStorage.removeItem('user-store')
      // 重定向到登录页
      window.location.href = '/login'
    }

    return Promise.reject(error)
  }
)

/**
 * 生成唯一请求ID
 * @returns {string} 请求ID
 */
function generateRequestId() {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`
}

/**
 * 获取API性能数据
 * @returns {Array} API性能数据列表
 */
export function getApiPerformanceData() {
  return apiPerformanceData
}

/**
 * 清除API性能数据
 */
export function clearApiPerformanceData() {
  apiPerformanceData.length = 0
}

// 认证相关API
const authApi = {
  // 用户登录
  login(data) {
    // 使用FormData格式发送登录请求
    const formData = new URLSearchParams();
    formData.append('username', data.username);
    formData.append('password', data.password);

    return api.post('/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
  },
  // 用户注册
  register(data) {
    return api.post('/users', data)
  }
}

// 用户相关API
const userApi = {
  // 获取当前用户信息
  getProfile() {
    return api.get('/users/me')
  },
  // 更新用户信息
  updateProfile(data) {
    return api.put('/users/me', data)
  },
  // 获取用户密钥列表
  getKeys() {
    return api.get('/users/me/license-keys')
  },
  // 重新生成密钥
  regenerateKey(productId) {
    return api.post(`/users/me/license-keys/${productId}/regenerate`)
  },
  // 修改密码
  changePassword(data) {
    return api.post('/users/me/change-password', data)
  }
}

// 产品相关API
const productApi = {
  // 获取产品列表
  getProducts(params) {
    return api.get('/products', { params })
  },
  // 获取产品详情
  getProductById(id) {
    return api.get(`/products/${id}`)
  },
  // 创建产品（管理员）
  createProduct(data) {
    return api.post('/products/admin', data)
  },
  // 更新产品（管理员）
  updateProduct(id, data) {
    return api.put(`/products/admin/${id}`, data)
  },
  // 切换产品状态（管理员）
  toggleProductStatus(id, isActive) {
    return api.patch(`/products/admin/${id}/status`, null, { params: { is_active: isActive } })
  },
  // 删除产品（管理员）
  deleteProduct(id) {
    return api.delete(`/products/admin/${id}`)
  }
}

// 订单相关API
const orderApi = {
  // 创建订单
  createOrder(data) {
    return api.post('/orders', data)
  },
  // 获取订单列表
  getOrders() {
    return api.get('/orders')
  },
  // 获取订单详情
  getOrderById(id) {
    return api.get(`/orders/${id}`)
  }
}

// 支付相关API
const paymentApi = {
  // 发起支付
  initiatePayment(orderId) {
    return api.post(`/orders/${orderId}/pay`)
  },
  // 查询支付状态
  checkPaymentStatus(orderId) {
    return api.get(`/orders/${orderId}/status`)
  },
  // 获取软件许可密钥列表
  getLicenseKeys() {
    return api.get('/license-keys')
  },
  // 模拟支付成功（仅用于测试）
  mockPaymentSuccess(orderId) {
    return api.post(`/orders/${orderId}/mock-payment-success`)
  }
}

// 管理员相关API
const adminApi = {
  // 获取用户列表
  getUsers(params) {
    return api.get('/admin/users', { params })
  },
  // 获取销售统计
  getStatistics() {
    return api.get('/admin/statistics')
  },
  // 获取所有订单
  getAllOrders(params) {
    return api.get('/admin/orders', { params })
  },
  // 获取订单详情
  getOrderById(id) {
    return api.get(`/admin/orders/${id}`)
  },
  // 更新订单状态
  updateOrderStatus(id, status) {
    return api.patch(`/admin/orders/${id}/status`, null, { params: { status } })
  },
  // 删除订单
  deleteOrder(id) {
    return api.delete(`/admin/orders/${id}`)
  },
  // 获取所有产品（包括已下架的）
  getAllProducts(params) {
    return api.get('/products/admin', { params })
  },
  // 创建产品
  createProduct(data) {
    return api.post('/products/admin', data)
  },
  // 更新产品
  updateProduct(id, data) {
    return api.put(`/products/admin/${id}`, data)
  },
  // 更新站点信息
  updateSiteInfo(data) {
    return api.put('/site-info', data)
  },
  // 切换用户状态（启用/禁用）
  toggleUserStatus(userId, isActive) {
    return api.patch(`/admin/users/${userId}/status`, null, { params: { is_active: isActive } })
  }
}

// 站点相关API
const siteApi = {
  // 获取站点信息
  getSiteInfo() {
    return api.get('/site-info')
  }
}

// 导出API
export default {
  auth: authApi,
  user: userApi,
  product: productApi,
  order: orderApi,
  payment: paymentApi,
  admin: adminApi,
  site: siteApi
}