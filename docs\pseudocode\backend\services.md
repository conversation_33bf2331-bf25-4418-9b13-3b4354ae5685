# 服务层伪代码

## 1. 用户服务 (services/user.py)

```python
# 用户服务
class UserService:
    # 创建新用户
    async def create_user(self, db: AsyncSession, user_create: UserCreate) -> User:
        # 检查用户名或邮箱是否已存在
        existing_user = await self.get_user_by_username(db, user_create.username)
        if existing_user:
            raise HTTPException(status_code=400, detail="用户名已存在")
        
        existing_email = await self.get_user_by_email(db, user_create.email)
        if existing_email:
            raise HTTPException(status_code=400, detail="邮箱已存在")
        
        # 创建新用户
        hashed_password = get_password_hash(user_create.password)
        db_user = User(
            username=user_create.username,
            email=user_create.email,
            password_hash=hashed_password,
            address=user_create.address
        )
        
        db.add(db_user)
        await db.commit()
        await db.refresh(db_user)
        return db_user
    
    # 通过ID获取用户
    async def get_user_by_id(self, db: AsyncSession, user_id: int) -> Optional[User]:
        result = await db.execute(select(User).where(User.id == user_id))
        return result.scalars().first()
    
    # 通过用户名获取用户
    async def get_user_by_username(self, db: AsyncSession, username: str) -> Optional[User]:
        result = await db.execute(select(User).where(User.username == username))
        return result.scalars().first()
    
    # 通过邮箱获取用户
    async def get_user_by_email(self, db: AsyncSession, email: str) -> Optional[User]:
        result = await db.execute(select(User).where(User.email == email))
        return result.scalars().first()
    
    # 更新用户信息
    async def update_user(self, db: AsyncSession, user_id: int, user_update: UserUpdate) -> Optional[User]:
        user = await self.get_user_by_id(db, user_id)
        if not user:
            return None
        
        # 更新用户信息
        update_data = user_update.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(user, key, value)
        
        await db.commit()
        await db.refresh(user)
        return user
    
    # 获取所有用户
    async def get_users(self, db: AsyncSession, skip: int = 0, limit: int = 100) -> List[User]:
        result = await db.execute(select(User).offset(skip).limit(limit))
        return result.scalars().all()
    
    # 获取用户总数
    async def get_user_count(self, db: AsyncSession) -> int:
        result = await db.execute(select(func.count()).select_from(User))
        return result.scalar()

# 创建用户服务实例
user_service = UserService()
```

## 2. 产品服务 (services/product.py)

```python
# 产品服务
class ProductService:
    # 创建新产品
    async def create_product(self, db: AsyncSession, product_create: ProductCreate) -> Product:
        db_product = Product(**product_create.dict())
        db.add(db_product)
        await db.commit()
        await db.refresh(db_product)
        return db_product
    
    # 通过ID获取产品
    async def get_product_by_id(self, db: AsyncSession, product_id: int) -> Optional[Product]:
        result = await db.execute(select(Product).where(Product.id == product_id))
        return result.scalars().first()
    
    # 获取所有产品
    async def get_products(self, db: AsyncSession, skip: int = 0, limit: int = 100, product_type: Optional[str] = None) -> List[Product]:
        query = select(Product).where(Product.is_active == True)
        
        if product_type:
            query = query.where(Product.type == product_type)
        
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()
    
    # 更新产品信息
    async def update_product(self, db: AsyncSession, product_id: int, product_update: ProductUpdate) -> Optional[Product]:
        product = await self.get_product_by_id(db, product_id)
        if not product:
            return None
        
        # 更新产品信息
        update_data = product_update.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(product, key, value)
        
        await db.commit()
        await db.refresh(product)
        return product
    
    # 获取产品销售数量
    async def get_product_sales_count(self, db: AsyncSession, product_type: Optional[str] = None) -> int:
        query = select(func.count()).select_from(Order).join(Product).where(Order.status == "paid")
        
        if product_type:
            query = query.where(Product.type == product_type)
        
        result = await db.execute(query)
        return result.scalar()

# 创建产品服务实例
product_service = ProductService()
```
