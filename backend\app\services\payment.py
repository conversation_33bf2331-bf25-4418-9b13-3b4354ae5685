import random
import string
import time
from datetime import datetime
from typing import List, Optional, Tu<PERSON>

from fastapi import HTTPException, status
from sqlalchemy import select, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from app.models.order import LicenseKey, Order, OrderItem, Payment
from app.models.product import Product
from app.schemas.order import (
    LicenseKeyCreate,
    OrderCreate,
    OrderUpdate,
    PaymentCreate,
    PaymentUpdate,
)
from app.services.product import product_service
from app.core.config import settings

# 尝试导入支付宝SDK
try:
    from alipay import AliPay
    from alipay.utils import AliPayConfig
    ALIPAY_SDK_AVAILABLE = True
except ImportError:
    ALIPAY_SDK_AVAILABLE = False
    print("警告: 支付宝SDK未安装，将使用模拟支付")


class OrderService:
    """
    订单服务
    """

    async def create_order(
        self, db: AsyncSession, user_id: int, order_create: OrderCreate
    ) -> <PERSON><PERSON>[Order, List[OrderItem]]:
        """
        创建订单

        Args:
            db: 数据库会话
            user_id: 用户ID
            order_create: 订单创建模型

        Returns:
            Tuple[Order, List[OrderItem]]: 订单和订单项列表

        Raises:
            HTTPException: 产品不存在或已下架时抛出
        """
        # 检查产品是否存在
        order_items = []
        total_amount = 0.0

        # 生成订单号
        order_number = f"ORD-{int(time.time())}-{user_id}-{random.randint(1000, 9999)}"

        # 创建订单
        db_order = Order(
            order_number=order_number,
            user_id=user_id,
            total_amount=total_amount,  # 临时值，后面会更新
            status="pending",
        )

        db.add(db_order)
        await db.flush()  # 获取订单ID

        # 创建订单项
        for item in order_create.items:
            # 检查产品是否存在
            product = await product_service.get_product_by_id(db, item.product_id)
            if not product or not product.is_active:
                # 回滚事务
                await db.rollback()
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"产品 {item.product_id} 不存在或已下架",
                )

            # 创建订单项
            db_order_item = OrderItem(
                order_id=db_order.id,
                product_id=item.product_id,
                quantity=item.quantity,
                price=product.price,
            )

            db.add(db_order_item)
            order_items.append(db_order_item)
            total_amount += product.price * item.quantity

        # 更新订单总金额
        db_order.total_amount = total_amount

        await db.commit()
        await db.refresh(db_order)

        return db_order, order_items

    async def get_order_by_id(
        self, db: AsyncSession, order_id: int, include_items: bool = False
    ) -> Optional[Order]:
        """
        通过ID获取订单

        Args:
            db: 数据库会话
            order_id: 订单ID
            include_items: 是否包含订单项

        Returns:
            Optional[Order]: 订单
        """
        query = select(Order).where(Order.id == order_id)

        if include_items:
            query = query.options(
                joinedload(Order.order_items).joinedload(OrderItem.product)
            )

        result = await db.execute(query)
        return result.scalars().first()

    async def get_order_by_order_number(
        self, db: AsyncSession, order_number: str, include_items: bool = False
    ) -> Optional[Order]:
        """
        通过订单号获取订单

        Args:
            db: 数据库会话
            order_number: 订单号
            include_items: 是否包含订单项

        Returns:
            Optional[Order]: 订单
        """
        query = select(Order).where(Order.order_number == order_number)

        if include_items:
            query = query.options(
                joinedload(Order.order_items).joinedload(OrderItem.product)
            )

        result = await db.execute(query)
        return result.scalars().first()

    async def get_user_orders(
        self, db: AsyncSession, user_id: int, include_items: bool = False
    ) -> List[Order]:
        """
        获取用户订单列表

        Args:
            db: 数据库会话
            user_id: 用户ID
            include_items: 是否包含订单项

        Returns:
            List[Order]: 订单列表
        """
        query = select(Order).where(Order.user_id == user_id).order_by(Order.created_at.desc())

        if include_items:
            query = query.options(
                joinedload(Order.order_items).joinedload(OrderItem.product)
            )

        result = await db.execute(query)
        return list(result.unique().scalars().all())

    async def update_order(
        self, db: AsyncSession, order_id: int, order_update: OrderUpdate
    ) -> Optional[Order]:
        """
        更新订单

        Args:
            db: 数据库会话
            order_id: 订单ID
            order_update: 订单更新模型

        Returns:
            Optional[Order]: 更新后的订单
        """
        # 获取订单
        db_order = await self.get_order_by_id(db, order_id)
        if not db_order:
            return None

        # 更新订单
        update_data = order_update.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_order, key, value)

        await db.commit()
        await db.refresh(db_order)
        return db_order

    async def create_payment(
        self, db: AsyncSession, payment_create: PaymentCreate
    ) -> Payment:
        """
        创建支付记录

        Args:
            db: 数据库会话
            payment_create: 支付记录创建模型

        Returns:
            Payment: 支付记录
        """
        # 创建支付记录
        db_payment = Payment(**payment_create.dict())
        db.add(db_payment)
        await db.commit()
        await db.refresh(db_payment)
        return db_payment

    async def update_payment(
        self, db: AsyncSession, payment_id: int, payment_update: PaymentUpdate
    ) -> Optional[Payment]:
        """
        更新支付记录

        Args:
            db: 数据库会话
            payment_id: 支付记录ID
            payment_update: 支付记录更新模型

        Returns:
            Optional[Payment]: 更新后的支付记录
        """
        # 获取支付记录
        result = await db.execute(select(Payment).where(Payment.id == payment_id))
        db_payment = result.scalars().first()
        if not db_payment:
            return None

        # 更新支付记录
        update_data = payment_update.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_payment, key, value)

        await db.commit()
        await db.refresh(db_payment)
        return db_payment

    async def get_payment_by_id(self, db: AsyncSession, payment_id: int) -> Optional[Payment]:
        """
        通过ID获取支付记录

        Args:
            db: 数据库会话
            payment_id: 支付记录ID

        Returns:
            Optional[Payment]: 支付记录
        """
        result = await db.execute(select(Payment).where(Payment.id == payment_id))
        return result.scalars().first()

    async def get_payment_by_payment_id(
        self, db: AsyncSession, payment_id: str
    ) -> Optional[Payment]:
        """
        通过支付平台交易号获取支付记录

        Args:
            db: 数据库会话
            payment_id: 支付平台交易号

        Returns:
            Optional[Payment]: 支付记录
        """
        result = await db.execute(select(Payment).where(Payment.payment_id == payment_id))
        return result.scalars().first()

    async def get_order_payments(self, db: AsyncSession, order_id: int) -> List[Payment]:
        """
        获取订单支付记录列表

        Args:
            db: 数据库会话
            order_id: 订单ID

        Returns:
            List[Payment]: 支付记录列表
        """
        result = await db.execute(
            select(Payment).where(Payment.order_id == order_id).order_by(Payment.created_at.desc())
        )
        return list(result.scalars().all())

    async def create_license_key(
        self, db: AsyncSession, license_key_create: LicenseKeyCreate
    ) -> LicenseKey:
        """
        创建软件许可密钥

        Args:
            db: 数据库会话
            license_key_create: 软件许可密钥创建模型

        Returns:
            LicenseKey: 软件许可密钥
        """
        # 创建软件许可密钥
        db_license_key = LicenseKey(**license_key_create.dict())
        db.add(db_license_key)
        await db.commit()
        await db.refresh(db_license_key)
        return db_license_key

    async def get_user_license_keys(
        self, db: AsyncSession, user_id: int
    ) -> List[LicenseKey]:
        """
        获取用户软件许可密钥列表

        Args:
            db: 数据库会话
            user_id: 用户ID

        Returns:
            List[LicenseKey]: 软件许可密钥列表
        """
        result = await db.execute(
            select(LicenseKey)
            .options(joinedload(LicenseKey.product))
            .where(LicenseKey.user_id == user_id)
            .order_by(LicenseKey.created_at.desc())
        )
        return list(result.scalars().all())

    def generate_license_key(self) -> str:
        """
        生成软件许可密钥

        Returns:
            str: 软件许可密钥
        """
        # 生成随机密钥
        key_parts = []
        for _ in range(5):
            part = "".join(random.choices(string.ascii_uppercase + string.digits, k=5))
            key_parts.append(part)

        return "-".join(key_parts)


# 创建订单服务实例
order_service = OrderService()


class PaymentService:
    """
    支付服务
    """

    def __init__(self):
        """初始化支付服务"""
        self.alipay_enabled = False
        self.alipay_client = None

        # 尝试初始化支付宝客户端
        if ALIPAY_SDK_AVAILABLE:
            try:
                # 检查必要的配置是否存在
                if (hasattr(settings, 'ALIPAY_APP_ID') and
                    hasattr(settings, 'ALIPAY_PRIVATE_KEY_PATH') and
                    hasattr(settings, 'ALIPAY_PUBLIC_KEY_PATH')):

                    # 检查密钥文件是否存在
                    try:
                        with open(settings.ALIPAY_PRIVATE_KEY_PATH, 'r') as f:
                            private_key = f.read()
                        with open(settings.ALIPAY_PUBLIC_KEY_PATH, 'r') as f:
                            public_key = f.read()

                        # 初始化支付宝客户端
                        self.alipay_client = AliPay(
                            appid=settings.ALIPAY_APP_ID,
                            app_notify_url=settings.ALIPAY_NOTIFY_URL,
                            app_private_key_string=private_key,
                            alipay_public_key_string=public_key,
                            sign_type="RSA2",
                            debug=True  # 沙箱环境设置为True
                        )
                        self.alipay_config = AliPayConfig(timeout=15)  # 设置超时时间
                        self.alipay_enabled = True
                        print("支付宝客户端初始化成功")
                    except FileNotFoundError as e:
                        print(f"支付宝密钥文件不存在: {str(e)}")
                    except Exception as e:
                        print(f"支付宝客户端初始化失败: {str(e)}")
                else:
                    print("支付宝配置不完整，将使用模拟支付")
            except Exception as e:
                print(f"支付宝客户端初始化异常: {str(e)}")

    async def generate_payment_url(self, db: AsyncSession, order_id: int) -> str:
        """
        生成支付链接

        Args:
            db: 数据库会话
            order_id: 订单ID

        Returns:
            str: 支付链接

        Raises:
            HTTPException: 订单不存在或状态不正确时抛出
        """
        # 获取订单信息
        order = await order_service.get_order_by_id(db, order_id, include_items=True)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在",
            )

        if order.status != "pending":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="订单状态不正确",
            )

        # 如果支付宝客户端可用，使用支付宝支付
        if self.alipay_enabled and self.alipay_client:
            try:
                # 获取订单商品名称
                product_names = []
                for item in order.order_items:
                    product = await product_service.get_product_by_id(db, item.product_id)
                    if product:
                        product_names.append(product.name)

                # 商品名称
                subject = "购买 " + ", ".join(product_names) if product_names else f"订单 {order.order_number}"

                # 打印调试信息
                print(f"生成支付宝订单参数: 订单号={order.order_number}, 金额={order.total_amount}, 商品={subject}")

                try:
                    # 生成支付宝订单
                    order_string = self.alipay_client.api_alipay_trade_page_pay(
                        out_trade_no=order.order_number,
                        total_amount=str(order.total_amount),
                        subject=subject,
                        return_url=settings.ALIPAY_RETURN_URL,
                        notify_url=settings.ALIPAY_NOTIFY_URL
                    )

                    # 打印生成的订单字符串（不含敏感信息）
                    print(f"生成的支付宝订单字符串长度: {len(order_string)}")

                    # 返回支付链接（沙箱环境）
                    payment_url = f"https://openapi-sandbox.dl.alipaydev.com/gateway.do?{order_string}"
                    print(f"生成的支付链接: {payment_url[:100]}...")
                    return payment_url
                except Exception as e:
                    print(f"生成支付宝订单字符串时出错: {str(e)}")
                    # 记录详细的异常信息
                    import traceback
                    traceback.print_exc()
                    raise
            except Exception as e:
                print(f"生成支付宝支付链接失败: {str(e)}")
                # 如果生成支付宝链接失败，回退到模拟支付

        # 尝试使用简单的方式生成支付宝链接（不使用SDK）
        try:
            # 检查是否有必要的配置
            if hasattr(settings, 'ALIPAY_APP_ID'):
                # 构建简单的支付宝请求参数
                params = {
                    'app_id': settings.ALIPAY_APP_ID,
                    'method': 'alipay.trade.page.pay',
                    'charset': 'utf-8',
                    'sign_type': 'RSA2',
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'version': '1.0',
                    'return_url': settings.ALIPAY_RETURN_URL,
                    'notify_url': settings.ALIPAY_NOTIFY_URL,
                    'biz_content': f'{{"out_trade_no":"{order.order_number}","total_amount":"{order.total_amount}","subject":"订单 {order.order_number}","product_code":"FAST_INSTANT_TRADE_PAY"}}',
                    'sign': 'test_sign_value'  # 这里使用测试签名，实际上支付宝会验证失败，但可以测试网关是否可访问
                }

                # 构建URL
                import urllib.parse
                query_string = '&'.join([f"{k}={urllib.parse.quote(str(v))}" for k, v in params.items()])
                backup_url = f"https://openapi-sandbox.dl.alipaydev.com/gateway.do?{query_string}"

                # 提供备用选择
                print("支付宝SDK不可用，使用模拟支付:")
                print(f"模拟支付链接: http://localhost:5173/mock-payment?order_id={order.id}&order_number={order.order_number}&amount={order.total_amount}")

                # 返回模拟支付链接，但在日志中提供备用选项
                return f"http://localhost:5173/mock-payment?order_id={order.id}&order_number={order.order_number}&amount={order.total_amount}"
            else:
                print("缺少支付宝配置，使用模拟支付")
        except Exception as e:
            print(f"生成备用支付链接失败: {str(e)}")

        # 使用模拟支付链接作为最后的备选方案
        return f"http://localhost:5173/mock-payment?order_id={order.id}&order_number={order.order_number}&amount={order.total_amount}"

    async def handle_payment_notification(self, db: AsyncSession, data: dict) -> bool:
        """
        处理支付回调

        Args:
            db: 数据库会话
            data: 支付回调数据

        Returns:
            bool: 处理结果
        """
        # 如果支付宝客户端可用，验证支付宝回调
        if self.alipay_enabled and self.alipay_client:
            try:
                # 验证签名
                signature = data.pop("sign", None)
                if not self.alipay_client.verify(data, signature):
                    print("支付宝回调签名验证失败")
                    return False

                # 获取订单信息
                out_trade_no = data.get("out_trade_no")  # 商户订单号
                trade_no = data.get("trade_no")  # 支付宝交易号
                trade_status = data.get("trade_status")  # 交易状态

                print(f"支付宝回调: 订单号={out_trade_no}, 交易号={trade_no}, 状态={trade_status}")

                # 只处理交易成功或交易完成的通知
                if trade_status not in ("TRADE_SUCCESS", "TRADE_FINISHED"):
                    print(f"支付宝回调: 交易状态不是成功或完成: {trade_status}")
                    return True  # 返回成功，避免支付宝重复通知

                # 获取订单
                order = await order_service.get_order_by_order_number(db, out_trade_no)
                if not order:
                    print(f"支付宝回调: 未找到订单: {out_trade_no}")
                    return False

                # 检查订单状态
                if order.status != "pending":
                    print(f"支付宝回调: 订单状态不是待支付: {order.status}")
                    return True  # 订单已处理，直接返回成功

                # 创建支付记录
                payment_create = PaymentCreate(
                    order_id=order.id,
                    payment_id=trade_no,
                    amount=order.total_amount,
                    status="success",
                    payment_method="alipay",
                )
                await order_service.create_payment(db, payment_create)

                # 更新订单状态
                order_update = OrderUpdate(status="paid", payment_method="alipay")
                await order_service.update_order(db, order.id, order_update)

                # 如果是软件产品，生成许可密钥（检查是否已存在）
                for item in order.order_items:
                    product = await product_service.get_product_by_id(db, item.product_id)
                    if product and product.product_type == "software":
                        # 检查是否已经为此订单和产品生成了许可密钥
                        existing_key = await db.execute(
                            select(LicenseKey).where(
                                LicenseKey.order_id == order.id,
                                LicenseKey.product_id == product.id
                            )
                        )
                        if existing_key.scalar_one_or_none() is None:
                            # 生成许可密钥
                            license_key = order_service.generate_license_key()
                            license_key_create = LicenseKeyCreate(
                                user_id=order.user_id,
                                product_id=product.id,
                                order_id=order.id,
                                license_key=license_key,
                            )
                            await order_service.create_license_key(db, license_key_create)
                        else:
                            print(f"许可密钥已存在，跳过生成: 订单={order.id}, 产品={product.id}")

                return True
            except Exception as e:
                print(f"处理支付宝回调失败: {str(e)}")
                # 如果处理支付宝回调失败，继续使用模拟支付回调处理

        # 模拟支付回调处理
        order_number = data.get("out_trade_no")
        trade_status = data.get("trade_status")
        trade_no = data.get("trade_no")

        # 模拟支付宝回调处理
        if not order_number or not trade_status or not trade_no:
            return False

        # 获取订单
        order = await order_service.get_order_by_order_number(db, order_number)
        if not order:
            return False

        # 检查订单状态
        if order.status != "pending":
            return True  # 订单已处理，直接返回成功

        # 创建支付记录
        payment_create = PaymentCreate(
            order_id=order.id,
            payment_id=trade_no,
            amount=order.total_amount,
            status="success" if trade_status == "TRADE_SUCCESS" else "failed",
            payment_method="alipay",
        )
        await order_service.create_payment(db, payment_create)

        # 更新订单状态
        if trade_status == "TRADE_SUCCESS":
            # 更新订单状态为已支付
            order_update = OrderUpdate(status="paid", payment_method="alipay")
            await order_service.update_order(db, order.id, order_update)

            # 如果是软件产品，生成许可密钥（检查是否已存在）
            for item in order.order_items:
                product = await product_service.get_product_by_id(db, item.product_id)
                if product and product.product_type == "software":
                    # 检查是否已经为此订单和产品生成了许可密钥
                    existing_key = await db.execute(
                        select(LicenseKey).where(
                            LicenseKey.order_id == order.id,
                            LicenseKey.product_id == product.id
                        )
                    )
                    if existing_key.scalar_one_or_none() is None:
                        # 生成许可密钥
                        license_key = order_service.generate_license_key()
                        license_key_create = LicenseKeyCreate(
                            user_id=order.user_id,
                            product_id=product.id,
                            order_id=order.id,
                            license_key=license_key,
                        )
                        await order_service.create_license_key(db, license_key_create)
                    else:
                        print(f"许可密钥已存在，跳过生成: 订单={order.id}, 产品={product.id}")

            return True
        else:
            # 更新订单状态为已取消
            order_update = OrderUpdate(status="cancelled")
            await order_service.update_order(db, order.id, order_update)
            return False

    async def check_payment_status(self, db: AsyncSession, order_id: int) -> dict:
        """
        查询支付状态

        Args:
            db: 数据库会话
            order_id: 订单ID

        Returns:
            dict: 支付状态信息

        Raises:
            HTTPException: 订单不存在时抛出
        """
        # 获取订单信息
        order = await order_service.get_order_by_id(db, order_id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在",
            )

        # 如果支付宝客户端可用，查询支付宝订单状态
        if self.alipay_enabled and self.alipay_client and order.status == "pending":
            try:
                # 查询支付宝订单状态
                result = self.alipay_client.api_alipay_trade_query(out_trade_no=order.order_number)

                # 检查查询结果
                if result.get("code") == "10000":  # 接口调用成功
                    trade_status = result.get("trade_status")
                    print(f"支付宝查询结果: 订单号={order.order_number}, 状态={trade_status}")

                    # 如果支付成功，更新订单状态
                    if trade_status in ("TRADE_SUCCESS", "TRADE_FINISHED") and order.status == "pending":
                        # 创建支付记录
                        payment_create = PaymentCreate(
                            order_id=order.id,
                            payment_id=result.get("trade_no"),
                            amount=order.total_amount,
                            status="success",
                            payment_method="alipay",
                        )
                        await order_service.create_payment(db, payment_create)

                        # 更新订单状态
                        order_update = OrderUpdate(status="paid", payment_method="alipay")
                        await order_service.update_order(db, order.id, order_update)

                        # 如果是软件产品，生成许可密钥
                        order_items = await db.execute(
                            select(OrderItem).where(OrderItem.order_id == order.id)
                        )
                        order_items = order_items.scalars().all()

                        for item in order_items:
                            product = await product_service.get_product_by_id(db, item.product_id)
                            if product and product.product_type == "software":
                                # 检查是否已经为此订单和产品生成了许可密钥
                                existing_key = await db.execute(
                                    select(LicenseKey).where(
                                        LicenseKey.order_id == order.id,
                                        LicenseKey.product_id == product.id
                                    )
                                )
                                if existing_key.scalar_one_or_none() is None:
                                    # 生成许可密钥
                                    license_key = order_service.generate_license_key()
                                    license_key_create = LicenseKeyCreate(
                                        user_id=order.user_id,
                                        product_id=product.id,
                                        order_id=order.id,
                                        license_key=license_key,
                                    )
                                    await order_service.create_license_key(db, license_key_create)
                                else:
                                    print(f"许可密钥已存在，跳过生成: 订单={order.id}, 产品={product.id}")

                        # 重新获取更新后的订单
                        order = await order_service.get_order_by_id(db, order_id)
            except Exception as e:
                print(f"查询支付宝订单状态失败: {str(e)}")

        # 获取支付记录
        payments = await order_service.get_order_payments(db, order_id)
        latest_payment = payments[0] if payments else None

        # 返回支付状态信息
        return {
            "order_id": order.id,
            "order_number": order.order_number,
            "status": order.status,
            "payment_id": latest_payment.payment_id if latest_payment else None,
            "payment_status": latest_payment.status if latest_payment else None,
            "payment_time": latest_payment.payment_time if latest_payment else None,
        }

    async def mock_payment_success(self, db: AsyncSession, order_id: int) -> bool:
        """
        模拟支付成功

        Args:
            db: 数据库会话
            order_id: 订单ID

        Returns:
            bool: 处理结果

        Raises:
            HTTPException: 订单不存在或状态不正确时抛出
        """
        # 获取订单信息
        order = await order_service.get_order_by_id(db, order_id, include_items=True)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在",
            )

        if order.status != "pending":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="订单状态不正确",
            )

        # 生成模拟支付宝交易号
        trade_no = f"MOCK{int(time.time())}{random.randint(1000, 9999)}"

        # 创建支付记录
        payment_create = PaymentCreate(
            order_id=order.id,
            payment_id=trade_no,
            amount=order.total_amount,
            status="success",
            payment_method="alipay",
        )
        payment = await order_service.create_payment(db, payment_create)

        # 更新支付时间
        payment_update = PaymentUpdate(payment_time=datetime.now())
        await order_service.update_payment(db, payment.id, payment_update)

        # 更新订单状态
        order_update = OrderUpdate(status="paid", payment_method="alipay")
        await order_service.update_order(db, order.id, order_update)

        # 如果是软件产品，生成许可密钥（检查是否已存在）
        for item in order.order_items:
            product = await product_service.get_product_by_id(db, item.product_id)
            if product and product.product_type == "software":
                # 检查是否已经为此订单和产品生成了许可密钥
                existing_key = await db.execute(
                    select(LicenseKey).where(
                        LicenseKey.order_id == order.id,
                        LicenseKey.product_id == product.id
                    )
                )
                if existing_key.scalar_one_or_none() is None:
                    # 生成许可密钥
                    license_key = order_service.generate_license_key()
                    license_key_create = LicenseKeyCreate(
                        user_id=order.user_id,
                        product_id=product.id,
                        order_id=order.id,
                        license_key=license_key,
                    )
                    # 使用修改后的create_license_key方法创建许可密钥
                    await order_service.create_license_key(db, license_key_create)
                else:
                    print(f"许可密钥已存在，跳过生成: 订单={order.id}, 产品={product.id}")

        return True


# 创建支付服务实例
payment_service = PaymentService()