# 后端伪代码结构

## 1. 核心模块 (core)

### 1.1 配置管理 (config.py)
```python
# 定义应用配置类
class Settings:
    # 基础配置
    PROJECT_NAME: str = "软件和设备销售平台"
    API_V1_PREFIX: str = "/api/v1"

    # 数据库配置
    DATABASE_URL: str = "**********************************/app"
    TEST_DATABASE_URL: str = "sqlite:///./test.db"

    # 安全配置
    SECRET_KEY: str = "your-secret-key"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24  # 1天

    # 支付宝配置
    ALIPAY_APP_ID: str = "2021000148685433"  # 沙箱APP_ID
    ALIPAY_PRIVATE_KEY_PATH: str = "/app/keys/app_private_key.pem"
    ALIPAY_PUBLIC_KEY_PATH: str = "/app/keys/alipay_public_key.pem"
    ALIPAY_NOTIFY_URL: str = "https://your-domain.com/api/v1/payments/notify"
    ALIPAY_RETURN_URL: str = "https://your-domain.com/payment-result"

# 创建全局设置对象
settings = Settings()
```

### 1.2 数据库连接 (database.py)
```python
# 创建异步数据库引擎和会话
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession

# 创建数据库引擎
engine = create_async_engine(settings.DATABASE_URL)

# 创建会话工厂
async def get_db():
    async with AsyncSession(engine) as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
```

### 1.3 安全模块 (security.py)
```python
# 密码哈希和JWT令牌处理
from passlib.context import CryptContext
from jose import jwt

# 密码哈希上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# 创建访问令牌
def create_access_token(data: dict, expires_delta: timedelta):
    to_encode = data.copy()
    expire = datetime.utcnow() + expires_delta
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm="HS256")
    return encoded_jwt

# 验证密码
def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

# 获取密码哈希
def get_password_hash(password):
    return pwd_context.hash(password)
```

### 1.4 依赖注入 (dependencies.py)
```python
# API依赖项
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer

# OAuth2密码承载依赖项
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_PREFIX}/auth/login")

# 获取当前用户
async def get_current_user(token: str = Depends(oauth2_scheme), db: AsyncSession = Depends(get_db)):
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
        user_id: str = payload.get("sub")
        if user_id is None:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
    except JWTError:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)

    user = await user_service.get_user_by_id(db, user_id)
    if user is None:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
    return user

# 获取当前活跃用户
async def get_current_active_user(current_user = Depends(get_current_user)):
    if not current_user.is_active:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST)
    return current_user

# 获取当前管理员用户
async def get_current_admin_user(current_user = Depends(get_current_active_user)):
    if current_user.role != "admin":
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN)
    return current_user
```

## 2. 数据模型 (models)

### 2.1 用户模型 (user.py)
```python
# 用户数据模型
from sqlalchemy import Column, Integer, String, Boolean, DateTime
from sqlalchemy.sql import func

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    password_hash = Column(String)
    is_active = Column(Boolean, default=True)
    role = Column(String, default="user")  # user 或 admin
    address = Column(String, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
```

### 2.2 产品模型 (product.py)
```python
# 产品数据模型
class Product(Base):
    __tablename__ = "products"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    description = Column(String)
    price = Column(Float)
    type = Column(String)  # software 或 device
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
```

### 2.3 订单模型 (order.py)
```python
# 订单数据模型
class Order(Base):
    __tablename__ = "orders"

    id = Column(Integer, primary_key=True, index=True)
    order_number = Column(String, unique=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    product_id = Column(Integer, ForeignKey("products.id"))
    status = Column(String)  # pending, paid, cancelled
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    license_key = Column(String, nullable=True)

    # 关系
    user = relationship("User", back_populates="orders")
    product = relationship("Product")
```

### 2.4 站点信息模型 (site.py)
```python
# 站点信息数据模型
class SiteInfo(Base):
    __tablename__ = "site_info"

    id = Column(Integer, primary_key=True, index=True)
    company_name = Column(String)
    description = Column(String)
    contact_info = Column(String)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
```
