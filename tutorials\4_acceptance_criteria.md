# 4 验收标准定义与 Gherkin 语法

## 什么是验收标准？

验收标准（Acceptance Criteria）是用户故事或功能需求的详细说明，用于定义何时该用户故事或功能被认为是“完成”且符合预期。它们是一组可测试的条件，明确了在什么情况下，用户故事所描述的功能才能被接受。

验收标准是连接业务需求和开发实现的桥梁，确保团队对需求的理解一致，并为测试提供明确的依据。

## 为什么验收标准很重要？

定义清晰的验收标准有以下几个重要原因：

1.  **明确需求：** 消除歧义，确保所有相关方（产品经理、开发人员、测试人员等）对用户故事的理解一致。
2.  **指导开发：** 为开发人员提供明确的目标，知道需要实现哪些功能才能满足用户故事。
3.  **指导测试：** 为测试人员提供测试用例的基础，确保测试覆盖了所有关键场景。
4.  **定义“完成”：** 明确用户故事何时可以被标记为“完成”，避免不必要的返工。
5.  **促进沟通：** 作为团队内部以及团队与业务方之间沟通的工具。

## Gherkin 语法介绍

Gherkin 是一种业务可读的领域特定语言，用于描述软件的行为。它通常与行为驱动开发（BDD）结合使用，用于编写验收标准。Gherkin 的主要关键字包括：

*   `Feature`: 描述要实现的功能。
*   `Scenario`: 描述一个具体的场景或示例。
*   `Given`: 描述系统的初始状态或上下文。
*   `When`: 描述用户执行的操作或发生的事件。
*   `Then`: 描述操作或事件发生后系统的预期结果。
*   `And`, `But`: 用于连接多个 Given, When, Then 语句，使描述更流畅。

## 使用 Gherkin 编写验收标准示例

假设我们有一个用户故事：“作为一名注册用户，我希望能够登录到网站，以便访问我的个人资料。”

以下是使用 Gherkin 语法编写的验收标准示例：

```gherkin
Feature: 用户登录

  为了访问我的个人资料
  作为一名注册用户
  我希望能够登录到网站

  Scenario: 成功登录
    Given 我是已注册用户
    And 我在登录页面
    When 我输入正确的用户名和密码
    And 我点击“登录”按钮
    Then 我应该被重定向到我的个人资料页面
    And 我应该看到我的用户名显示在页面上

  Scenario: 登录失败 - 密码错误
    Given 我是已注册用户
    And 我在登录页面
    When 我输入正确的用户名和错误的密码
    And 我点击“登录”按钮
    Then 我应该停留在登录页面
    And 我应该看到错误消息“用户名或密码错误”

  Scenario: 登录失败 - 用户不存在
    Given 我在登录页面
    When 我输入未注册的用户名和任意密码
    And 我点击“登录”按钮
    Then 我应该停留在登录页面
    And 我应该看到错误消息“用户名或密码错误”
```

在这个示例中，我们定义了一个 `Feature`（用户登录），然后为不同的登录场景定义了 `Scenario`。每个场景都使用 `Given-When-Then` 结构清晰地描述了操作步骤和预期结果。

## 总结

验收标准是确保用户故事被正确理解和实现的关键。通过使用 Gherkin 等结构化语言，我们可以编写清晰、可测试的验收标准，从而提高开发效率和产品质量。

接下来，我们将学习如何识别和文档化需求的约束（Constraints）。