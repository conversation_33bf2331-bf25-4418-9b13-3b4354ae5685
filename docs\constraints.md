
# 项目约束文档

本文档列出了本项目在设计、开发和实施过程中需要遵守的关键约束。

## 1. 技术约束

### 1.1 平台和环境
- **约束**: 系统必须部署在基于 Linux 的服务器环境中，并使用 Docker Compose 进行容器编排。
  - **类型**: 技术
  - **来源**: 现有基础设施标准和部署策略。
  - **影响**: 所有技术选型和部署流程必须与 Linux 环境和 Docker Compose 兼容。
- **约束**: 开发环境使用 Windows 11 操作系统，Docker Desktop 和 PowerShell 终端。
  - **类型**: 技术
  - **来源**: 开发团队工作环境。
  - **影响**: 开发工具和脚本需要在 Windows 环境中正常工作，同时确保与 Linux 生产环境的兼容性。
- **约束**: 前端应用必须兼容主流现代浏览器（Chrome, Firefox, Edge, Safari 的最新两个版本）。
  - **类型**: 技术
  - **来源**: 用户需求。
  - **影响**: 需要进行跨浏览器兼容性测试。
- **约束**: 系统必须部署在阿里云2核心2GB内存40GB存储的云服务器上。
  - **类型**: 技术
  - **来源**: 预算限制和资源分配。
  - **影响**: 系统设计和性能优化必须考虑这些硬件限制，避免资源密集型操作。

### 1.2 技术栈和工具
- **约束**: 后端服务必须使用 Python 3.12 和 FastAPI (>=0.11) 框架开发，保持异步特性。
  - **类型**: 技术
  - **来源**: 团队技术栈熟练度和项目需求。
  - **影响**: 限制了后端开发的技术选择。
- **约束**: 数据库必须使用 PostgreSQL 16（生产环境）和 SQLite（开发环境）。
  - **类型**: 技术
  - **来源**: 组织数据库标准化策略和开发便捷性考虑。
  - **影响**: 数据模型设计需同时兼容两种数据库系统，确保开发到生产的平滑过渡。
- **约束**: 前端应用必须使用 Vue 3、Pinia 状态管理和 Element Plus 组件库开发。
  - **类型**: 技术
  - **来源**: 团队技术栈熟练度和项目需求。
  - **影响**: 限制了前端开发的技术选择，但提供了成熟的状态管理和UI组件方案。
- **约束**: 所有服务必须容器化并使用 Docker Compose 进行管理，以确保开发环境与生产环境的一致性。
  - **类型**: 技术
  - **来源**: 开发和部署策略。
  - **影响**: 需要为所有服务编写 Dockerfile 和配置 docker-compose.yml 文件，确保在 Windows 开发环境和 Linux 生产环境中均可正常运行。

### 1.3 集成约束
- **约束**: 支付功能必须集成支付宝作为唯一的第三方支付服务。
  - **类型**: 技术/业务
  - **来源**: 业务决策和用户支付习惯。
  - **影响**: 需要实现支付宝API集成，包括支付发起、回调处理和订单状态同步。
  - **MVP阶段实施**: 在MVP阶段，将实现基本的支付流程，包括支付订单创建、支付宝支付页面跳转、支付结果异步通知处理和支付状态查询。
  - **优先级**: 高

### 1.4 架构约束
- **约束**: 系统必须严格限制为三个微服务：后端服务、前端服务和数据库服务。
  - **类型**: 技术/架构
  - **来源**: 项目规模和资源限制。
  - **影响**: 限制了系统的复杂性，要求在有限的服务数量内实现所有功能。
  - **MVP阶段实施**: 
    - 后端微服务：单一FastAPI应用，包含所有API端点和业务逻辑
    - 前端微服务：单一Vue应用，包含所有用户界面
    - 数据库微服务：单一PostgreSQL实例，包含所有数据表
  - **优先级**: 高
  - **注意事项**: 不允许增加额外的微服务，必须在这三个服务内实现所有功能。任何需要的功能扩展都应该在现有服务内进行模块化设计，而非创建新服务。

## 2. 业务约束

### 2.1 预算约束
- **约束**: 项目总预算不得超过 20,000 人民币。
  - **类型**: 业务
  - **来源**: 业务部门批准。
  - **影响**: 影响技术选型、第三方服务使用和团队规模。
  - **明细**:
    - 服务器费用: 5,000 人民币/年（阿里云2核心2GB内存40GB存储）
    - 人力成本: 15,000 人民币（一个开发人员一个月的工资）

### 2.2 时间表约束
- **约束**: 系统必须在 15 天之内上线。
  - **类型**: 业务
  - **来源**: 项目计划。
  - **影响**: 影响开发优先级、功能范围和资源分配。极短的时间表要求严格聚焦于核心MVP功能。

### 2.3 运营约束
- **约束**: 系统必须支持每天 24 小时、每周 7 天不间断运行。
  - **类型**: 业务
  - **来源**: 业务需求。
  - **影响**: 需要考虑高可用性和容错设计。
  - **MVP阶段实施**: 实现基本的监控告警系统和明确的手动恢复流程，而非完全自动化的高可用架构。

### 2.4 范围约束
- **约束**: 项目必须遵循 MVP（最小可行产品）原则，仅包含核心功能以实现快速上线和验证。
  - **类型**: 业务
  - **来源**: 项目策略。
  - **影响**: 影响需求优先级排序和功能范围定义，非核心功能将在后续迭代中考虑。
  - **优先级**: 高

### 2.5 性能约束 (MVP阶段)
- **约束**: 在MVP阶段，系统需支持至少20个并发用户，平均响应时间不超过3秒。
  - **类型**: 业务/技术
  - **来源**: MVP目标和服务器资源限制。
  - **影响**: 影响架构设计、技术选型和优化工作。
  - **优先级**: 中

### 2.6 产品范围约束
- **约束**: 由于产品数量有限（一个软件，两个设备），网站设计应尽量简单且响应式。
  - **类型**: 业务/设计
  - **来源**: 产品范围限制
  - **影响**: 影响网站设计和前端开发，需优先考虑简洁的用户界面和跨设备兼容性。
  - **优先级**: 高

## 3. 法规约束

### 3.1 数据隐私
- **约束**: 必须遵守 GDPR 数据隐私法规。
  - **类型**: 法规
  - **来源**: 法律要求。
  - **影响**: 影响用户数据收集、存储和处理方式。
  - **MVP阶段实施**: 实现用户数据收集的明确同意机制、基本的数据访问和删除功能、隐私政策文档、数据加密存储。

### 3.2 可访问性
- **约束**: 前端界面必须符合 WCAG 2.1 AA 级别标准。
  - **类型**: 法规
  - **来源**: 公司政策和潜在用户需求。
  - **影响**: 影响 UI/UX 设计和前端实现。
  - **MVP阶段实施**: 优先确保核心用户流程符合WCAG 2.1 AA标准，次要功能可在后续迭代中完善。

## 4. 安全约束

### 4.1 认证和授权
- **约束**: 在 MVP 阶段，双因素认证可以简化或推迟到后续版本。
  - **类型**: 安全
  - **来源**: MVP 范围限制。
  - **影响**: MVP 版本将不包含强制双因素认证，后续版本需要实现。
  - **MVP阶段最低安全要求**: 实现强密码策略、基本的账户锁定机制、安全的密码存储、基本的CSRF和XSS防护。
  - **优先级**: MVP 阶段：低，后续阶段：高

- **约束**: 在 MVP 阶段，基于角色的访问控制 (RBAC) 可以简化或推迟到后续版本。
  - **类型**: 安全
  - **来源**: MVP 范围限制。
  - **影响**: MVP 版本将使用简化的权限模型（例如，仅区分管理员和普通用户）。
  - **优先级**: MVP 阶段：低，后续阶段：高

### 4.2 数据安全
- **约束**: 敏感数据在传输和存储过程中必须加密。
  - **类型**: 安全
  - **来源**: 安全策略和法规要求。
  - **影响**: 需要实施 TLS/SSL 和数据库加密。

### 4.3 审计和监控
- **约束**: 必须记录所有关键用户操作和系统事件。
  - **类型**: 安全
  - **来源**: 安全和合规性要求。
  - **影响**: 需要设计和实现日志记录和监控系统。
  - **MVP阶段实施**: 优先记录关键错误、安全事件和核心用户操作日志。

## 5. MVP 原则下的综合影响与建议

本项目面临的约束共同塑造了 MVP 的范围和实施策略：

- **时间表 (15天)** 和 **预算 (20,000人民币)** 是核心约束，要求高度聚焦核心功能，追求效率和成本效益。
- **技术栈** 提供了明确的技术基础，应避免引入新的、不熟悉的技术。
- **平台环境** 要求所有部署流程必须容器化，确保环境一致性。
- **运营约束 (24/7)** 在 MVP 阶段应优先确保核心功能的稳定性。
- **法规约束** 必须在 MVP 设计和实现中得到满足，不能因为追求速度而忽视合规性。
- **安全约束** 在 MVP 阶段允许简化某些功能，但必须确保基本的用户认证和数据安全。

**总结建议**:

1. **严格定义 MVP 范围**: 严格限定核心功能集，排除所有非必要特性。
2. **技术实现聚焦**: 优先使用团队熟悉的技术栈，避免技术选型上的不确定性。
3. **风险评估与权衡**: 评估在 MVP 阶段简化带来的风险，确认可接受的风险水平。
4. **持续沟通与验证**: 保持与业务方和相关团队的密切沟通，确保对约束的理解一致。
5. **开发与生产环境差异管理**: 使用Docker容器确保环境一致性，定期在类似生产环境的条件下进行测试。

## 6. 文档更新历史

| 版本 | 日期       | 作者   | 描述         |
|------|------------|--------|--------------|
| 1.0  | 2023-10-27 | [zl] | 初稿         |
| 1.1  | 2024-04-30 | AI   | 评审并根据MVP原则添加建议 |
| 1.2  | 2024-05-02 | AI   | 更新技术栈约束，统一格式，清理重复内容 |
