"""
为license_keys表添加order_id列的迁移脚本
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine

from app.core.config import settings


async def add_order_id_to_license_keys():
    """为license_keys表添加order_id列"""
    # 创建异步引擎
    if settings.DATABASE_URL.startswith("sqlite"):
        # SQLite连接
        sqlalchemy_url = settings.DATABASE_URL.replace(
            "sqlite:///", "sqlite+aiosqlite:///"
        )
    else:
        # PostgreSQL连接
        sqlalchemy_url = settings.DATABASE_URL.replace(
            "postgresql://", "postgresql+asyncpg://"
        )

    engine = create_async_engine(sqlalchemy_url)

    # 检查列是否已存在
    async with engine.connect() as conn:
        # 开启事务
        async with conn.begin():
            # 检查列是否已存在
            check_query = """
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'license_keys' AND column_name = 'order_id'
            """
            result = await conn.execute(text(check_query))
            column_exists = result.fetchone() is not None

            if column_exists:
                print("order_id列已存在，无需添加")
                return

            # 添加order_id列
            print("正在添加order_id列...")

            # 创建临时表
            await conn.execute(text("""
            CREATE TABLE license_keys_temp (
                id SERIAL PRIMARY KEY,
                user_id INTEGER NOT NULL REFERENCES users(id),
                product_id INTEGER NOT NULL REFERENCES products(id),
                order_id INTEGER NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
                license_key VARCHAR(100) UNIQUE NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
            """))

            # 查询所有许可密钥
            result = await conn.execute(text("SELECT * FROM license_keys"))
            license_keys = result.fetchall()

            # 查询所有订单项，用于关联订单ID
            result = await conn.execute(text("""
            SELECT oi.product_id, o.id as order_id, o.user_id
            FROM order_items oi
            JOIN orders o ON oi.order_id = o.id
            WHERE o.status = 'paid'
            """))
            order_items = result.fetchall()

            # 为每个许可密钥找到对应的订单ID
            for key in license_keys:
                # 尝试找到匹配的订单项
                matching_order = None
                for item in order_items:
                    if (item.product_id == key.product_id and
                        item.user_id == key.user_id):
                        matching_order = item
                        break

                # 如果找到匹配的订单，使用其ID；否则使用默认值1
                order_id = matching_order.order_id if matching_order else 1

                # 插入到临时表
                await conn.execute(text("""
                INSERT INTO license_keys_temp
                (id, user_id, product_id, order_id, license_key, is_active, created_at, updated_at)
                VALUES (:id, :user_id, :product_id, :order_id, :license_key, :is_active, :created_at, :updated_at)
                """), {
                    "id": key.id,
                    "user_id": key.user_id,
                    "product_id": key.product_id,
                    "order_id": order_id,
                    "license_key": key.license_key,
                    "is_active": key.is_active,
                    "created_at": key.created_at,
                    "updated_at": key.updated_at
                })

            # 删除原表
            await conn.execute(text("DROP TABLE license_keys"))

            # 重命名临时表
            await conn.execute(text("ALTER TABLE license_keys_temp RENAME TO license_keys"))

            # 重新创建索引
            await conn.execute(text("CREATE INDEX IF NOT EXISTS ix_license_keys_id ON license_keys (id)"))

            print("order_id列添加成功")


if __name__ == "__main__":
    try:
        asyncio.run(add_order_id_to_license_keys())
    except Exception as e:
        print(f"执行脚本时发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
