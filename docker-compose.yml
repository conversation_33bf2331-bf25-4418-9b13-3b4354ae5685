# 文件路径: docker-compose.yml

version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: sales_platform_backend
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./backend/app/static:/app/app/static
    depends_on:
      - database
    environment:
      DATABASE_URL: ****************************************/sales_platform
      PYTHONPATH: /app
      SECRET_KEY: "2088721067958003"
      ACCESS_TOKEN_EXPIRE_MINUTES: "1440"
      ENVIRONMENT: "development"
      ALIPAY_APP_ID: "2021000148685433"
      ALIPAY_PRIVATE_KEY_PATH: "/app/keys/app_private_key.pem"
      ALIPAY_PUBLIC_KEY_PATH: "/app/keys/alipay_public_key.pem"
      ALIPAY_NOTIFY_URL: "http://localhost:8000/api/v1/payments/notify"
      ALIPAY_RETURN_URL: "http://localhost:5173/payment-result"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: sales_platform_frontend
    ports:
      - "5173:5173" # 映射开发服务器端口
    volumes:
      - ./frontend:/src # 挂载本地代码目录
      - /src/node_modules # 匿名卷，防止本地 node_modules 覆盖容器内的依赖
    depends_on:
      - backend
    environment:
      VITE_API_BASE_URL: "http://localhost:8000/api/v1"
    restart: unless-stopped

  database:
    image: postgres:16-alpine
    container_name: sales_platform_database
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: sales_platform
    volumes:
      - db_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d sales_platform"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  backend_tests:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: sales_platform_backend_tests
    command: pytest /app/tests -v
    volumes:
      - ./backend:/app
    depends_on:
      - database
    environment:
      DATABASE_URL: ****************************************/sales_platform_test
      PYTHONPATH: /app
      TESTING: "true"
      SECRET_KEY: "2088721067958003"
      ENVIRONMENT: "testing"
      ALIPAY_APP_ID: "2021000148685433"
      ALIPAY_PRIVATE_KEY_PATH: "/app/keys/app_private_key.pem"
      ALIPAY_PUBLIC_KEY_PATH: "/app/keys/alipay_public_key.pem"
      ALIPAY_NOTIFY_URL: "http://localhost:8000/api/v1/payments/notify"
      ALIPAY_RETURN_URL: "http://localhost:5173/payment-result"

  frontend_tests:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: sales_platform_frontend_tests
    command: /bin/sh -c "npm run test:unit"
    volumes:
      - ./frontend:/src
      - /src/node_modules
    environment:
      VITE_API_BASE_URL: "http://backend:8000/api/v1"

volumes:
  db_data:
    name: sales_platform_data

networks:
  default:
    name: sales_platform_network