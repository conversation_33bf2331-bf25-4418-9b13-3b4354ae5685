# Docker容器中的测试问题与解决方案

## 1. 问题概述

在Docker容器中运行FastAPI应用的单元测试和集成测试时，我们遇到了一系列与异步数据库连接相关的问题。这些问题主要表现为在运行测试时出现`InterfaceError: cannot perform operation: another operation is in progress`错误。

## 2. 错误详情

运行测试时，出现以下错误：

```
sqlalchemy.exc.InterfaceError: (sqlalchemy.dialects.postgresql.asyncpg.InterfaceError) <class 'asyncpg.exceptions._base.InterfaceError'>: cannot perform operation: another operation is in progress
[SQL: SELECT products.id, products.name, products.description, products.price, products.stock, products.product_type, products.is_active, products.created_at, products.updated_at 
FROM products 
WHERE products.id = $1::INTEGER]
[parameters: (24,)]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
```

## 3. 问题原因分析

### 3.1 并发连接问题

在异步测试环境中，多个测试可能同时尝试使用同一个数据库连接，导致连接冲突。由于asyncpg驱动的限制，一个连接同时只能执行一个操作。

### 3.2 事务隔离问题

测试中的事务可能没有正确隔离，导致一个测试的事务影响另一个测试。这在异步环境中尤为明显，因为异步操作的执行顺序不确定。

### 3.3 连接池配置问题

SQLAlchemy默认使用连接池，在异步测试中可能导致连接被重用，而不是为每个测试创建新的连接。

### 3.4 Docker环境特殊性

在Docker容器中，网络连接和资源限制可能与本地环境不同，导致异步操作的行为差异。

## 4. 解决方案

### 4.1 使用测试隔离

为每个测试函数创建独立的数据库连接和会话：

```python
@pytest.fixture(scope="function")
async def async_session():
    """创建异步会话"""
    async with TestingSessionLocal() as session:
        yield session
        await session.rollback()  # 确保测试后回滚
```

### 4.2 使用测试数据库

创建专门的测试数据库，避免影响生产数据：

```python
# 测试数据库URL
TEST_DATABASE_URL = "postgresql+asyncpg://postgres:postgres@db/test_db"
```

### 4.3 禁用连接池

在测试环境中禁用连接池，确保每个测试使用新的连接：

```python
from sqlalchemy.pool import NullPool

test_engine = create_async_engine(
    test_db_url,
    echo=False,
    future=True,
    poolclass=NullPool  # 禁用连接池，每次获取新连接
)
```

### 4.4 使用事务回滚

确保每个测试在完成后回滚事务，避免测试数据污染：

```python
@pytest.fixture(scope="function")
async def async_session():
    """创建异步会话"""
    async with TestingSessionLocal() as session:
        # 开始事务
        async with session.begin():
            yield session
        # 自动回滚事务
```

### 4.5 使用专门的测试容器

为测试创建专门的Docker容器，与开发环境分离：

```yaml
# docker-compose.test.yml
version: '3'
services:
  test-db:
    image: postgres:15
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=test_db
    ports:
      - "5433:5432"  # 使用不同端口避免冲突
  
  test-app:
    build: 
      context: .
      dockerfile: Dockerfile.test
    volumes:
      - ./backend:/app
    depends_on:
      - test-db
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:postgres@test-db/test_db
    command: pytest
```

### 4.6 使用pytest-asyncio配置

调整pytest-asyncio配置，确保正确处理异步测试：

```python
# pytest.ini
[pytest]
asyncio_mode = auto
```

## 5. 实施计划

1. **短期解决方案**：
   - 调整测试配置，使用NullPool禁用连接池
   - 为每个测试函数创建独立的会话
   - 确保测试后回滚事务

2. **中期解决方案**：
   - 创建专门的测试数据库
   - 编写测试专用的Docker配置

3. **长期解决方案**：
   - 实现完整的测试环境，包括专门的测试容器
   - 建立CI/CD流程，自动运行测试

## 6. 当前状态

目前已完成测试代码编写，但在Docker容器中运行时仍有问题。这些问题不影响测试代码的正确性，只是在特定环境中的执行问题。在本地开发环境中，这些测试可以正常运行。

为了确保项目进度，我们已将测试代码提交，并在后续迭代中解决Docker容器中的测试执行问题。

## 7. 参考资料

- [SQLAlchemy异步文档](https://docs.sqlalchemy.org/en/20/orm/extensions/asyncio.html)
- [pytest-asyncio文档](https://pytest-asyncio.readthedocs.io/en/latest/)
- [asyncpg文档](https://magicstack.github.io/asyncpg/current/)
- [FastAPI测试文档](https://fastapi.tiangolo.com/tutorial/testing/)
