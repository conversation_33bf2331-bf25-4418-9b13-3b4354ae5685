from typing import Any, Dict, Generic, List, Optional, TypeVar, Union

from pydantic import BaseModel, Field
from pydantic.generics import GenericModel


# 定义泛型类型变量
T = TypeVar("T")
DictT = TypeVar("DictT", bound=Dict[str, Any])


class ResponseModel(GenericModel, Generic[T]):
    """
    通用响应模型

    Attributes:
        success: 是否成功
        message: 消息
        data: 数据
    """
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="消息")
    data: Optional[T] = Field(None, description="数据")


class PaginatedResponseModel(ResponseModel[List[T]]):
    """
    分页响应模型

    Attributes:
        success: 是否成功
        message: 消息
        data: 数据列表
        total: 总记录数
        page: 当前页码
        page_size: 每页记录数
    """
    total: int = Field(..., description="总记录数")
    page: int = Field(1, description="当前页码")
    page_size: int = Field(10, description="每页记录数")


class DictResponseModel(ResponseModel[DictT]):
    """
    字典响应模型

    Attributes:
        success: 是否成功
        message: 消息
        data: 字典数据
    """
    pass


class DictListResponseModel(ResponseModel[List[DictT]]):
    """
    字典列表分页响应模型

    Attributes:
        success: 是否成功
        message: 消息
        data: 字典数据列表
        total: 总记录数
        page: 当前页码
        page_size: 每页记录数
    """
    total: int = Field(..., description="总记录数")
    page: int = Field(1, description="当前页码")
    page_size: int = Field(10, description="每页记录数")
