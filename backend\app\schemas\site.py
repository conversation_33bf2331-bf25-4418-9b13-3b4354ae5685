from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


# 站点信息基础模型
class SiteInfoBase(BaseModel):
    """
    站点信息基础模型

    Attributes:
        company_name: 公司名称
        description: 站点描述
        contact_info: 联系信息
        logo_url: Logo URL
    """
    company_name: str = Field(..., min_length=1, max_length=100, description="公司名称")
    description: Optional[str] = Field(None, description="站点描述")
    contact_info: Optional[str] = Field(None, description="联系信息")
    logo_url: Optional[str] = Field(None, description="Logo URL")


# 站点信息创建模型
class SiteInfoCreate(SiteInfoBase):
    """
    站点信息创建模型
    """
    pass


# 站点信息更新模型
class SiteInfoUpdate(BaseModel):
    """
    站点信息更新模型

    Attributes:
        company_name: 公司名称
        description: 站点描述
        contact_info: 联系信息
        logo_url: Logo URL
    """
    company_name: Optional[str] = Field(None, min_length=1, max_length=100, description="公司名称")
    description: Optional[str] = Field(None, description="站点描述")
    contact_info: Optional[str] = Field(None, description="联系信息")
    logo_url: Optional[str] = Field(None, description="Logo URL")


# 站点信息响应模型
class SiteInfo(SiteInfoBase):
    """
    站点信息响应模型

    Attributes:
        id: 站点信息ID
        created_at: 创建时间
        updated_at: 更新时间
    """
    id: int = Field(..., description="站点信息ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True