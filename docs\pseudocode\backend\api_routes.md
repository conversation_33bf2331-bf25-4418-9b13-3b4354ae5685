# API路由伪代码

## 1. 用户API (api/user.py)

```python
# 用户API路由
router = APIRouter(prefix="/users", tags=["users"])

# 注册新用户
@router.post("/", response_model=ResponseModel)
async def register_user(user_create: UserCreate, db: AsyncSession = Depends(get_db)):
    try:
        user = await user_service.create_user(db, user_create)
        return ResponseModel(
            success=True,
            message="用户注册成功",
            data=User.from_orm(user)
        )
    except HTTPException as e:
        return ResponseModel(success=False, message=e.detail)

# 获取当前用户信息
@router.get("/me", response_model=ResponseModel)
async def get_user_me(current_user: User = Depends(get_current_active_user)):
    return ResponseModel(
        success=True,
        message="获取用户信息成功",
        data=current_user
    )

# 更新当前用户信息
@router.put("/me", response_model=ResponseModel)
async def update_user_me(
    user_update: UserUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    updated_user = await user_service.update_user(db, current_user.id, user_update)
    return ResponseModel(
        success=True,
        message="用户信息更新成功",
        data=User.from_orm(updated_user)
    )

# 获取用户订单
@router.get("/me/orders", response_model=ResponseModel)
async def get_user_orders(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    orders = await payment_service.get_user_orders(db, current_user.id)
    return ResponseModel(
        success=True,
        message="获取订单成功",
        data=[Order.from_orm(order) for order in orders]
    )
```

## 2. 认证API (api/auth.py)

```python
# 认证API路由
router = APIRouter(prefix="/auth", tags=["auth"])

# 用户登录
@router.post("/login", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db)
):
    # 验证用户
    user = await user_service.get_user_by_username(db, form_data.username)
    if not user or not verify_password(form_data.password, user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码不正确"
        )
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id)},
        expires_delta=access_token_expires
    )
    
    return {"access_token": access_token, "token_type": "bearer"}
```

## 3. 产品API (api/product.py)

```python
# 产品API路由
router = APIRouter(prefix="/products", tags=["products"])

# 获取所有产品
@router.get("/", response_model=ResponseModel)
async def get_products(
    skip: int = 0,
    limit: int = 10,
    product_type: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    products = await product_service.get_products(db, skip, limit, product_type)
    return ResponseModel(
        success=True,
        message="获取产品列表成功",
        data=[Product.from_orm(product) for product in products]
    )

# 获取单个产品
@router.get("/{product_id}", response_model=ResponseModel)
async def get_product(product_id: int, db: AsyncSession = Depends(get_db)):
    product = await product_service.get_product_by_id(db, product_id)
    if not product:
        return ResponseModel(success=False, message="产品不存在")
    
    return ResponseModel(
        success=True,
        message="获取产品成功",
        data=Product.from_orm(product)
    )
```

## 4. 支付API (api/payment.py)

```python
# 支付API路由
router = APIRouter(prefix="/payments", tags=["payments"])

# 创建订单
@router.post("/orders", response_model=ResponseModel)
async def create_order(
    order_create: OrderCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    order = await payment_service.create_order(db, current_user.id, order_create)
    return ResponseModel(
        success=True,
        message="订单创建成功",
        data=Order.from_orm(order)
    )

# 获取支付链接
@router.get("/orders/{order_id}/pay", response_model=ResponseModel)
async def get_payment_url(
    order_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    # 检查订单是否属于当前用户
    order = await payment_service.get_order_by_id(db, order_id)
    if not order or order.user_id != current_user.id:
        return ResponseModel(success=False, message="订单不存在")
    
    payment_url = await payment_service.generate_payment_url(db, order_id)
    return ResponseModel(
        success=True,
        message="获取支付链接成功",
        data={"payment_url": payment_url}
    )

# 支付宝回调
@router.post("/notify", response_model=str)
async def alipay_notify(request: Request, db: AsyncSession = Depends(get_db)):
    data = await request.form()
    data_dict = dict(data)
    
    # 处理支付回调
    success = await payment_service.handle_payment_notification(db, data_dict)
    
    if success:
        return "success"
    else:
        return "fail"
```
