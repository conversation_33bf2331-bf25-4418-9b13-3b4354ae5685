from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.core.config import settings

# 创建FastAPI应用
app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_PREFIX}/openapi.json",
    docs_url=f"{settings.API_V1_PREFIX}/docs",
    redoc_url=f"{settings.API_V1_PREFIX}/redoc",
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制为前端域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 健康检查端点
@app.get("/health")
async def health_check():
    return {"status": "ok"}


# 应用启动事件
@app.on_event("startup")
async def startup_event():
    # 初始化数据库连接
    pass


# 应用关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    # 关闭数据库连接
    pass


# 导入API路由
from app.api.auth import router as auth_router
from app.api.user import router as user_router
from app.api.product import router as product_router
from app.api.site import router as site_router
from app.api.order import router as order_router
from app.api.payment import router as payment_router
from app.api.admin import router as admin_router

# 包含API路由
app.include_router(auth_router, prefix=settings.API_V1_PREFIX)
app.include_router(user_router, prefix=settings.API_V1_PREFIX)
app.include_router(product_router, prefix=settings.API_V1_PREFIX)
app.include_router(site_router, prefix=settings.API_V1_PREFIX)
app.include_router(order_router, prefix=f"{settings.API_V1_PREFIX}")
app.include_router(payment_router, prefix=f"{settings.API_V1_PREFIX}/payments")
app.include_router(admin_router, prefix=settings.API_V1_PREFIX)