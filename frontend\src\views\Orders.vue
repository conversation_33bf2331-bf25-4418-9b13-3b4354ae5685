<template>
  <div class="orders-container">
    <h1 class="page-title">我的订单</h1>
    
    <el-alert
      v-if="orderStore.error"
      :title="orderStore.error"
      type="error"
      show-icon
      @close="orderStore.clearError"
    />
    
    <div class="order-tabs">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="全部订单" name="all">
          <order-list :orders="orderStore.orders" @refresh="fetchOrders" />
        </el-tab-pane>
        <el-tab-pane label="待支付" name="pending">
          <order-list :orders="orderStore.pendingOrders" @refresh="fetchOrders" />
        </el-tab-pane>
        <el-tab-pane label="已支付" name="paid">
          <order-list :orders="orderStore.paidOrders" @refresh="fetchOrders" />
        </el-tab-pane>
        <el-tab-pane label="已取消" name="cancelled">
          <order-list :orders="orderStore.cancelledOrders" @refresh="fetchOrders" />
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <div class="license-keys-section" v-if="orderStore.paidOrders.length > 0">
      <h2>我的软件许可密钥</h2>
      <el-button type="primary" @click="fetchLicenseKeys" :loading="orderStore.loading">
        刷新许可密钥
      </el-button>
      
      <div class="license-keys-list" v-if="orderStore.licenseKeys.length > 0">
        <el-table :data="orderStore.licenseKeys" style="width: 100%">
          <el-table-column prop="product_id" label="产品ID" width="100" />
          <el-table-column label="产品名称" width="200">
            <template #default="scope">
              {{ getProductName(scope.row.product_id) }}
            </template>
          </el-table-column>
          <el-table-column prop="license_key" label="许可密钥" />
          <el-table-column label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
                {{ scope.row.is_active ? '有效' : '无效' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button 
                type="primary" 
                size="small" 
                @click="copyLicenseKey(scope.row.license_key)"
              >
                复制密钥
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-empty v-else description="暂无许可密钥" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useOrderStore } from '../store'
import { ElMessage } from 'element-plus'
import OrderList from '../components/OrderList.vue'

// 状态管理
const orderStore = useOrderStore()
const activeTab = ref('all')

// 产品名称缓存
const productNames = ref({})

// 获取订单列表
const fetchOrders = async () => {
  const result = await orderStore.fetchOrders()
  if (!result.success) {
    ElMessage.error(result.message)
  }
}

// 获取软件许可密钥列表
const fetchLicenseKeys = async () => {
  const result = await orderStore.fetchLicenseKeys()
  if (!result.success) {
    ElMessage.error(result.message)
  } else {
    ElMessage.success('许可密钥刷新成功')
  }
}

// 获取产品名称
const getProductName = (productId) => {
  return productNames.value[productId] || `产品 ${productId}`
}

// 复制许可密钥
const copyLicenseKey = (licenseKey) => {
  navigator.clipboard.writeText(licenseKey).then(() => {
    ElMessage.success('许可密钥已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败，请手动复制')
  })
}

// 组件挂载时获取订单列表和许可密钥
onMounted(async () => {
  await fetchOrders()
  if (orderStore.paidOrders.length > 0) {
    await fetchLicenseKeys()
  }
})
</script>

<style scoped>
.orders-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
  color: #303133;
}

.order-tabs {
  margin-bottom: 30px;
}

.license-keys-section {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.license-keys-section h2 {
  margin-bottom: 20px;
  color: #303133;
}

.license-keys-list {
  margin-top: 20px;
}
</style>
