import { defineStore } from 'pinia'
import api from '../api'

// 订单状态
export const useOrderStore = defineStore('order', {
  state: () => ({
    orders: [],
    currentOrder: null,
    licenseKeys: [],
    loading: false,
    error: null
  }),
  getters: {
    // 获取待支付订单
    pendingOrders: (state) => state.orders.filter(order => order.status === 'pending'),

    // 获取已支付订单
    paidOrders: (state) => state.orders.filter(order => order.status === 'paid'),

    // 获取已取消订单
    cancelledOrders: (state) => state.orders.filter(order => order.status === 'cancelled'),

    // 获取订单总数
    totalOrders: (state) => state.orders.length,

    // 获取订单总金额
    totalAmount: (state) => state.orders.reduce((total, order) => total + order.total_amount, 0)
  },
  actions: {
    // 创建订单
    async createOrder(orderData) {
      this.loading = true
      this.error = null
      try {
        const response = await api.order.createOrder(orderData)
        this.currentOrder = response.data
        // 更新订单列表
        await this.fetchOrders()
        return { success: true, order: response.data }
      } catch (error) {
        this.error = error.response?.data?.detail || '创建订单失败'
        return { success: false, message: this.error }
      } finally {
        this.loading = false
      }
    },

    // 获取订单列表
    async fetchOrders() {
      this.loading = true
      this.error = null
      try {
        const response = await api.order.getOrders()
        this.orders = response.data
        return { success: true }
      } catch (error) {
        this.error = error.response?.data?.detail || '获取订单列表失败'
        return { success: false, message: this.error }
      } finally {
        this.loading = false
      }
    },

    // 获取订单详情
    async fetchOrderById(orderId, forceRefresh = false) {
      this.loading = true
      this.error = null
      try {
        console.log('获取订单详情，订单ID:', orderId, '强制刷新:', forceRefresh)

        // 如果已经有当前订单且不需要强制刷新，直接返回
        if (this.currentOrder && this.currentOrder.id === orderId && !forceRefresh) {
          console.log('使用缓存的订单详情:', this.currentOrder)
          return { success: true, order: this.currentOrder }
        }

        const response = await api.order.getOrderById(orderId)
        console.log('订单详情API响应:', response.data)

        // 确保订单项中的产品信息正确
        if (response.data && response.data.items) {
          // 检查订单项是否有产品信息
          const hasProductInfo = response.data.items.some(item => item.product && item.product.name)
          console.log('订单项是否有产品信息:', hasProductInfo)

          if (!hasProductInfo) {
            console.warn('订单项缺少产品信息，尝试补充产品信息')
            // 如果订单项缺少产品信息，尝试补充
            for (const item of response.data.items) {
              if (!item.product || !item.product.name) {
                try {
                  // 获取产品详情
                  const productResponse = await api.product.getProductById(item.product_id)
                  console.log('获取产品详情:', productResponse.data)

                  // 检查响应结构
                  if (productResponse.data) {
                    // 根据API响应结构调整
                    if (productResponse.data.data) {
                      // 如果响应格式是 { data: { ... } }
                      item.product = productResponse.data.data
                    } else if (productResponse.data.id) {
                      // 如果响应直接是产品对象
                      item.product = productResponse.data
                    }

                    console.log('更新后的产品信息:', item.product)
                  }
                } catch (productError) {
                  console.error('获取产品详情失败:', productError)
                }
              }
            }
          }
        }

        this.currentOrder = response.data
        return { success: true, order: response.data }
      } catch (error) {
        console.error('获取订单详情失败:', error)
        this.error = error.response?.data?.detail || '获取订单详情失败'
        return { success: false, message: this.error }
      } finally {
        this.loading = false
      }
    },

    // 发起支付
    async initiatePayment(orderId) {
      this.loading = true
      this.error = null
      try {
        const response = await api.payment.initiatePayment(orderId)
        return { success: true, paymentUrl: response.data.payment_url }
      } catch (error) {
        this.error = error.response?.data?.detail || '发起支付失败'
        return { success: false, message: this.error }
      } finally {
        this.loading = false
      }
    },

    // 查询支付状态
    async checkPaymentStatus(orderId) {
      this.loading = true
      this.error = null
      try {
        console.log('查询支付状态，订单ID:', orderId)
        const response = await api.payment.checkPaymentStatus(orderId)
        console.log('支付状态查询响应:', response.data)

        // 如果订单状态已更新，更新当前订单和订单列表
        if (this.currentOrder && this.currentOrder.id === orderId) {
          console.log('更新当前订单状态:', response.data.status)
          this.currentOrder.status = response.data.status
        }

        // 更新订单列表中的订单状态
        const orderIndex = this.orders.findIndex(order => order.id === orderId)
        if (orderIndex !== -1) {
          console.log('更新订单列表中的订单状态:', response.data.status)
          this.orders[orderIndex].status = response.data.status
        }

        // 如果状态为已支付，刷新订单列表
        if (response.data.status === 'paid') {
          console.log('订单已支付，刷新订单列表')
          await this.fetchOrders()
        }

        return { success: true, status: response.data }
      } catch (error) {
        console.error('查询支付状态失败:', error)
        this.error = error.response?.data?.detail || '查询支付状态失败'
        return { success: false, message: this.error }
      } finally {
        this.loading = false
      }
    },

    // 获取软件许可密钥列表
    async fetchLicenseKeys() {
      this.loading = true
      this.error = null
      try {
        const response = await api.payment.getLicenseKeys()
        this.licenseKeys = response.data
        return { success: true }
      } catch (error) {
        this.error = error.response?.data?.detail || '获取软件许可密钥列表失败'
        return { success: false, message: this.error }
      } finally {
        this.loading = false
      }
    },

    // 模拟支付成功（仅用于测试）
    async mockPaymentSuccess(orderId) {
      this.loading = true
      this.error = null
      try {
        console.log('调用模拟支付成功接口，订单ID:', orderId)
        const response = await api.payment.mockPaymentSuccess(orderId)
        console.log('模拟支付成功接口响应:', response.data)

        // 等待一段时间，确保后端处理完成
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 强制刷新订单状态
        const orderResult = await this.fetchOrderById(orderId, true)
        console.log('更新订单状态结果:', orderResult)

        if (orderResult.success) {
          // 检查订单状态是否已更新为已支付
          if (this.currentOrder.status !== 'paid') {
            console.warn('订单状态未更新为已支付，当前状态:', this.currentOrder.status)
            // 再次尝试获取最新状态
            await new Promise(resolve => setTimeout(resolve, 1000))
            await this.fetchOrderById(orderId, true)
          }

          // 更新订单列表
          await this.fetchOrders()

          // 检查支付状态
          const paymentStatus = await this.checkPaymentStatus(orderId)
          console.log('支付状态检查结果:', paymentStatus)

          return {
            success: true,
            result: response.data,
            orderStatus: this.currentOrder.status,
            paymentStatus: paymentStatus.success ? paymentStatus.status : null
          }
        } else {
          return { success: false, message: orderResult.message || '获取订单详情失败' }
        }
      } catch (error) {
        console.error('模拟支付失败:', error)
        this.error = error.response?.data?.detail || '模拟支付失败'
        return { success: false, message: this.error }
      } finally {
        this.loading = false
      }
    },

    // 清除当前订单
    clearCurrentOrder() {
      this.currentOrder = null
    },

    // 清除错误
    clearError() {
      this.error = null
    }
  },
  persist: {
    key: 'order-store',
    storage: localStorage,
    paths: ['orders', 'currentOrder', 'licenseKeys']
  }
})
