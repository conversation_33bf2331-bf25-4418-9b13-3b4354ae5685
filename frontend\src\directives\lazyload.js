/**
 * 图片懒加载指令
 * 使用方法：v-lazy="图片地址"
 */
export default {
  install(app) {
    // 默认加载中的图片
    const defaultLoadingImg = 'data:image/svg+xml;base64,PHN2ZyB0PSIxNjkwMjgyNzU2NTI5IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjI1NzgiIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIj48cGF0aCBkPSJNODUzLjMzMyA4NTMuMzMzSDIxMy4zMzNhODUuMzMzIDg1LjMzMyAwIDAgMS04NS4zMzMtODUuMzMzVjI5OC42NjdhODUuMzMzIDg1LjMzMyAwIDAgMSA4NS4zMzMtODUuMzM0aDY0MGE4NS4zMzMgODUuMzMzIDAgMCAxIDg1LjMzNCA4NS4zMzR2NDY5LjMzM2E4NS4zMzMgODUuMzMzIDAgMCAxLTg1LjMzNCA4NS4zMzN6TTIxMy4zMzMgMjU2YTQyLjY2NyA0Mi42NjcgMCAwIDAtNDIuNjY2IDQyLjY2N3Y0NjkuMzMzYTQyLjY2NyA0Mi42NjcgMCAwIDAgNDIuNjY2IDQyLjY2N2g2NDBhNDIuNjY3IDQyLjY2NyAwIDAgMCA0Mi42NjctNDIuNjY3VjI5OC42NjdhNDIuNjY3IDQyLjY2NyAwIDAgMC00Mi42NjctNDIuNjY3SDIxMy4zMzN6IiBmaWxsPSIjZTZlNmU2IiBwLWlkPSIyNTc5Ij48L3BhdGg+PHBhdGggZD0iTTM0MS4zMzMgNDI2LjY2N2E4NS4zMzMgODUuMzMzIDAgMSAwIDAtMTcwLjY2NyA4NS4zMzMgODUuMzMzIDAgMCAwIDAgMTcwLjY2N3pNMzQxLjMzMyAyOTguNjY3YTQyLjY2NyA0Mi42NjcgMCAxIDEgMCA4NS4zMzMgNDIuNjY3IDQyLjY2NyAwIDAgMSAwLTg1LjMzM3pNNzY4IDY4Mi42NjdMNjgyLjY2NyA1MTJsLTEyOCAxNzAuNjY3LTg1LjMzNC0xMjgtMTI4IDIxMy4zMzNoNTEyeiIgZmlsbD0iI2U2ZTZlNiIgcC1pZD0iMjU4MCI+PC9wYXRoPjwvc3ZnPg=='
    // 默认加载失败的图片
    const defaultErrorImg = 'data:image/svg+xml;base64,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'

    // 创建观察器实例
    const createObserver = (el) => {
      const options = {
        root: null, // 使用视口作为根元素
        rootMargin: '0px', // 视口边距
        threshold: 0.1 // 目标元素10%可见时触发回调
      }

      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const imgElement = entry.target
            const imgSrc = imgElement.getAttribute('data-src')
            
            // 创建新图片对象预加载
            const img = new Image()
            img.src = imgSrc
            
            // 图片加载成功
            img.onload = () => {
              imgElement.src = imgSrc
              imgElement.classList.add('lazy-loaded')
            }
            
            // 图片加载失败
            img.onerror = () => {
              imgElement.src = imgElement.getAttribute('data-error') || defaultErrorImg
              imgElement.classList.add('lazy-error')
            }
            
            // 停止观察已加载的图片
            observer.unobserve(imgElement)
          }
        })
      }, options)
      
      return observer
    }

    // 注册指令
    app.directive('lazy', {
      mounted(el, binding) {
        // 设置初始加载中的图片
        el.src = el.getAttribute('data-loading') || defaultLoadingImg
        // 保存真实图片地址
        el.setAttribute('data-src', binding.value)
        // 创建并启动观察器
        const observer = createObserver(el)
        observer.observe(el)
        // 将观察器实例保存到元素上，以便在更新和卸载时使用
        el._lazy_observer = observer
      },
      updated(el, binding) {
        // 如果图片地址变化，更新data-src属性
        if (binding.value !== binding.oldValue) {
          el.setAttribute('data-src', binding.value)
          // 如果已经加载过，重置为加载中状态
          if (el.classList.contains('lazy-loaded') || el.classList.contains('lazy-error')) {
            el.classList.remove('lazy-loaded', 'lazy-error')
            el.src = el.getAttribute('data-loading') || defaultLoadingImg
            // 重新观察元素
            if (el._lazy_observer) {
              el._lazy_observer.observe(el)
            }
          }
        }
      },
      unmounted(el) {
        // 清理观察器
        if (el._lazy_observer) {
          el._lazy_observer.disconnect()
          delete el._lazy_observer
        }
      }
    })
  }
}
