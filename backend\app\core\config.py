from typing import Any, Dict, List, Optional, Union
from pydantic import AnyHttpUrl, EmailStr, PostgresDsn, validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """应用配置类"""

    # 基础配置
    PROJECT_NAME: str = "软件和设备销售平台"
    API_V1_PREFIX: str = "/api/v1"
    ENVIRONMENT: str = "development"

    # 安全配置
    SECRET_KEY: str
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24  # 1天

    # CORS配置
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []

    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # 数据库配置
    DATABASE_URL: str

    @validator("DATABASE_URL", pre=True)
    def validate_database_url(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if values.get("ENVIRONMENT") == "testing":
            # 测试环境使用SQLite内存数据库
            return "sqlite:///./test.db"
        return v

    # 支付宝配置
    ALIPAY_APP_ID: str
    ALIPAY_PRIVATE_KEY_PATH: Optional[str] = None
    ALIPAY_PUBLIC_KEY_PATH: Optional[str] = None
    ALIPAY_NOTIFY_URL: str
    ALIPAY_RETURN_URL: str

    # 管理员配置
    FIRST_SUPERUSER_USERNAME: str = "admin"
    FIRST_SUPERUSER_PASSWORD: str = "admin123"
    FIRST_SUPERUSER_EMAIL: EmailStr = "<EMAIL>"

    model_config = SettingsConfigDict(env_file=".env", case_sensitive=True)


settings = Settings()