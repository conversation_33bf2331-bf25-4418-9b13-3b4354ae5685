# 伪代码练习：停用用户账户

## 需求
作为管理员，我希望能够停用特定的用户账户，以便阻止他们访问系统。

## 伪代码模板

```pseudocode
// TODO: 编写伪代码来实现停用用户账户的功能
// 考虑以下步骤：
// 1. 接收要停用的用户账户标识符（例如，用户ID或用户名）。
// 2. 验证当前用户是否具有管理员权限。
// 3. 在数据库中查找用户账户。
// 4. 如果找到用户账户：
//    a. 检查账户是否已经被停用。
//    b. 如果未被停用，则更新账户状态为停用。
//    c. 记录停用操作的日志。
//    d. 返回成功响应。
// 5. 如果未找到用户账户或当前用户没有管理员权限，返回相应的错误响应。

FUNCTION DeactivateUserAccount(userId):
    // TODO: 实现伪代码逻辑
    // ...

END FUNCTION
```

请尝试在上面的伪代码模板中填写具体的伪代码逻辑。