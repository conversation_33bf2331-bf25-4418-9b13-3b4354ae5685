import pytest
from unittest.mock import patch, MagicMock

from app.services.admin import AdminService
from app.models.user import User
from app.models.order import Order
from app.models.product import Product


@pytest.fixture
def admin_service():
    return AdminService()


@pytest.mark.asyncio
async def test_get_sales_statistics(admin_service, async_session):
    # 创建模拟数据
    user = User(id=1, username="test_user", email="<EMAIL>", password_hash="hash", is_active=True)
    product1 = Product(id=1, name="Software Product", description="Test", price=100, type="software", is_active=True)
    product2 = Product(id=2, name="Hardware Product", description="Test", price=200, type="hardware", is_active=True)
    order1 = Order(id=1, order_number="ORD-001", user_id=1, product_id=1, status="paid")
    order2 = Order(id=2, order_number="ORD-002", user_id=1, product_id=2, status="paid")
    order3 = Order(id=3, order_number="ORD-003", user_id=1, product_id=1, status="pending")
    
    # 添加到数据库
    async_session.add_all([user, product1, product2, order1, order2, order3])
    await async_session.commit()
    
    # 调用测试方法
    stats = await admin_service.get_sales_statistics(async_session)
    
    # 验证结果
    assert stats["user_count"] == 1
    assert stats["order_count"] == 3
    assert stats["paid_order_count"] == 2
    assert stats["software_sales"] == 1
    assert stats["hardware_sales"] == 1
    assert stats["product_count"] == 2
    assert stats["active_product_count"] == 2


@pytest.mark.asyncio
async def test_get_all_orders(admin_service, async_session):
    # 创建模拟数据
    user = User(id=1, username="test_user", email="<EMAIL>", password_hash="hash", is_active=True)
    product = Product(id=1, name="Test Product", description="Test", price=100, type="software", is_active=True)
    order1 = Order(id=1, order_number="ORD-001", user_id=1, product_id=1, status="paid")
    order2 = Order(id=2, order_number="ORD-002", user_id=1, product_id=1, status="pending")
    
    # 添加到数据库
    async_session.add_all([user, product, order1, order2])
    await async_session.commit()
    
    # 调用测试方法 - 获取所有订单
    orders = await admin_service.get_all_orders(async_session)
    assert len(orders) == 2
    
    # 调用测试方法 - 按状态筛选
    paid_orders = await admin_service.get_all_orders(async_session, status="paid")
    assert len(paid_orders) == 1
    assert paid_orders[0].status == "paid"
    
    pending_orders = await admin_service.get_all_orders(async_session, status="pending")
    assert len(pending_orders) == 1
    assert pending_orders[0].status == "pending"


@pytest.mark.asyncio
async def test_get_orders_count(admin_service, async_session):
    # 创建模拟数据
    user = User(id=1, username="test_user", email="<EMAIL>", password_hash="hash", is_active=True)
    product = Product(id=1, name="Test Product", description="Test", price=100, type="software", is_active=True)
    order1 = Order(id=1, order_number="ORD-001", user_id=1, product_id=1, status="paid")
    order2 = Order(id=2, order_number="ORD-002", user_id=1, product_id=1, status="pending")
    
    # 添加到数据库
    async_session.add_all([user, product, order1, order2])
    await async_session.commit()
    
    # 调用测试方法 - 获取所有订单数量
    count = await admin_service.get_orders_count(async_session)
    assert count == 2
    
    # 调用测试方法 - 按状态筛选
    paid_count = await admin_service.get_orders_count(async_session, status="paid")
    assert paid_count == 1
    
    pending_count = await admin_service.get_orders_count(async_session, status="pending")
    assert pending_count == 1


@pytest.mark.asyncio
async def test_toggle_user_status(admin_service, async_session):
    # 创建模拟数据
    user = User(id=1, username="test_user", email="<EMAIL>", password_hash="hash", is_active=True)
    
    # 添加到数据库
    async_session.add(user)
    await async_session.commit()
    
    # 调用测试方法 - 禁用用户
    updated_user = await admin_service.toggle_user_status(async_session, user.id, False)
    assert updated_user is not None
    assert updated_user.is_active is False
    
    # 调用测试方法 - 启用用户
    updated_user = await admin_service.toggle_user_status(async_session, user.id, True)
    assert updated_user is not None
    assert updated_user.is_active is True
    
    # 调用测试方法 - 不存在的用户
    non_existent_user = await admin_service.toggle_user_status(async_session, 999, False)
    assert non_existent_user is None
