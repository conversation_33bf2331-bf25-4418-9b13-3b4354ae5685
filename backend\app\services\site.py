from typing import Optional

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.site import SiteInfo
from app.schemas.site import SiteInfoCreate, SiteInfoUpdate


class SiteService:
    """
    站点信息服务类
    """

    async def get_site_info(self, db: AsyncSession) -> Optional[SiteInfo]:
        """
        获取站点信息

        Args:
            db: 数据库会话

        Returns:
            Optional[SiteInfo]: 站点信息对象，如果不存在则返回None
        """
        result = await db.execute(select(SiteInfo).limit(1))
        return result.scalars().first()

    async def create_site_info(self, db: AsyncSession, site_info_create: SiteInfoCreate) -> SiteInfo:
        """
        创建站点信息

        Args:
            db: 数据库会话
            site_info_create: 站点信息创建模型

        Returns:
            SiteInfo: 创建的站点信息对象
        """
        db_site_info = SiteInfo(**site_info_create.model_dump())
        db.add(db_site_info)
        await db.commit()
        await db.refresh(db_site_info)
        return db_site_info

    async def update_site_info(self, db: AsyncSession, site_info_update: SiteInfoUpdate) -> Optional[SiteInfo]:
        """
        更新站点信息

        Args:
            db: 数据库会话
            site_info_update: 站点信息更新模型

        Returns:
            Optional[SiteInfo]: 更新后的站点信息对象，如果不存在则创建新的
        """
        # 获取现有站点信息
        db_site_info = await self.get_site_info(db)

        # 如果不存在，则创建新的
        if not db_site_info:
            # 创建默认站点信息
            default_site_info = SiteInfoCreate(
                company_name="软件和设备销售平台",
                description="提供高质量的软件和设备销售服务",
                contact_info="电话：123-456-7890 邮箱：<EMAIL>",
                logo_url=""
            )
            # 更新默认值
            update_data = site_info_update.model_dump(exclude_unset=True)
            for key, value in update_data.items():
                if value is not None:
                    setattr(default_site_info, key, value)
            # 创建新的站点信息
            return await self.create_site_info(db, default_site_info)
        else:
            # 更新现有站点信息
            update_data = site_info_update.model_dump(exclude_unset=True)
            for key, value in update_data.items():
                if value is not None:
                    setattr(db_site_info, key, value)
            await db.commit()
            await db.refresh(db_site_info)
            return db_site_info


# 创建站点信息服务实例
site_service = SiteService()
