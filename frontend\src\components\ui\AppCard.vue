<template>
  <el-card
    :class="['app-card', customClass, { 'app-card-hover': hover }]"
    :shadow="shadow"
    :body-style="bodyStyle"
  >
    <template v-if="$slots.header || title" #header>
      <div class="app-card-header">
        <slot name="header">
          <h3 class="app-card-title">{{ title }}</h3>
        </slot>
      </div>
    </template>
    <div class="app-card-body">
      <slot></slot>
    </div>
    <template v-if="$slots.footer" #footer>
      <div class="app-card-footer">
        <slot name="footer"></slot>
      </div>
    </template>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // 卡片标题
  title: {
    type: String,
    default: ''
  },
  // 卡片阴影
  shadow: {
    type: String,
    default: 'always',
    validator: (value) => {
      return ['always', 'hover', 'never'].includes(value)
    }
  },
  // 是否在悬停时添加额外效果
  hover: {
    type: Boolean,
    default: false
  },
  // 自定义类名
  customClass: {
    type: String,
    default: ''
  },
  // 卡片内容样式
  bodyStyle: {
    type: Object,
    default: () => ({})
  }
})
</script>

<style scoped>
.app-card {
  transition: transform var(--transition-normal) ease, box-shadow var(--transition-normal) ease;
}

.app-card-hover:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg) !important;
}

.app-card-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.app-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.app-card-body {
  height: 100%;
}

.app-card-footer {
  padding-top: var(--spacing-4);
  margin-top: auto;
}
</style>
