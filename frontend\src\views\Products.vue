<template>
  <PageContainer customClass="products-page">
    <template #header>
      <div class="products-header">
        <div class="header-content">
          <h1 class="page-title">产品列表</h1>
          <p class="page-subtitle">浏览我们的软件和硬件产品</p>
        </div>
        <div class="filters">
          <el-select
            v-model="productType"
            placeholder="产品类型"
            @change="handleFilterChange"
            class="filter-select"
          >
            <el-option label="全部产品" value=""></el-option>
            <el-option label="软件产品" value="software"></el-option>
            <el-option label="硬件产品" value="hardware"></el-option>
          </el-select>
          <el-input
            v-model="searchQuery"
            placeholder="搜索产品"
            clearable
            @input="handleSearchInput"
            class="search-input"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>
    </template>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>

    <!-- 产品列表 -->
    <div v-else class="products-content">
      <!-- 无产品时显示空状态 -->
      <el-empty
        v-if="products.length === 0"
        description="暂无产品"
        class="empty-container"
      >
        <template #image>
          <el-icon class="empty-icon"><Goods /></el-icon>
        </template>
        <template #description>
          <p>暂无符合条件的产品</p>
        </template>
        <el-button @click="resetFilters">重置筛选条件</el-button>
      </el-empty>

      <!-- 产品网格 -->
      <div v-else class="products-grid">
        <AppCard
          v-for="product in products"
          :key="product.id"
          hover
          customClass="product-card"
          @click="goToProductDetail(product.id)"
        >
          <div class="product-image-container">
            <img
              :src="getProductImage(product.product_type)"
              class="product-image"
              :alt="product.name"
            />
            <div class="product-tag">
              <el-tag
                :type="product.product_type === 'software' ? 'success' : 'warning'"
                size="small"
                effect="dark"
              >
                {{ product.product_type === 'software' ? '软件' : '硬件' }}
              </el-tag>
            </div>
          </div>
          <h3 class="product-title">{{ product.name }}</h3>
          <p class="product-description">{{ product.description }}</p>
          <div class="product-footer">
            <div class="product-price">¥{{ product.price.toFixed(2) }}</div>
            <AppButton type="primary" round @click.stop="goToProductDetail(product.id)">
              查看详情
            </AppButton>
          </div>
        </AppCard>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-if="total > pageSize"
          background
          layout="prev, pager, next, jumper"
          :total="total"
          :page-size="pageSize"
          :current-page="currentPage"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          hide-on-single-page
        />
      </div>
    </div>
  </PageContainer>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useProductStore } from '../store/product'
import PageContainer from '../components/layout/PageContainer.vue'
import { Search, Goods } from '@element-plus/icons-vue'
import { debounce } from 'lodash-es'

const router = useRouter()
const productStore = useProductStore()

// 状态
const currentPage = ref(1)
const pageSize = ref(8)
const productType = ref('')
const searchQuery = ref('')
const searchTimeout = ref(null)

// 计算属性
const products = computed(() => productStore.products)
const loading = computed(() => productStore.loading)
const total = computed(() => productStore.total)

// 获取产品列表
const fetchProducts = async () => {
  const params = {
    skip: (currentPage.value - 1) * pageSize.value,
    limit: pageSize.value,
    product_type: productType.value || undefined,
    page: currentPage.value,
    pageSize: pageSize.value,
    search: searchQuery.value || undefined
  }

  const result = await productStore.fetchProducts(params)

  if (!result.success) {
    ElMessage.error(result.message || '获取产品列表失败')
  }
}

// 处理筛选条件变化
const handleFilterChange = () => {
  currentPage.value = 1
  fetchProducts()
}

// 处理搜索输入
const handleSearchInput = debounce(() => {
  currentPage.value = 1
  fetchProducts()
}, 500)

// 重置筛选条件
const resetFilters = () => {
  productType.value = ''
  searchQuery.value = ''
  currentPage.value = 1
  fetchProducts()
}

// 跳转到产品详情页
const goToProductDetail = (productId) => {
  router.push(`/products/${productId}`)
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchProducts()
}

// 处理每页数量变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchProducts()
}

// 获取产品图片
const getProductImage = (type) => {
  if (type === 'software') {
    return 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80'
  } else {
    return 'https://images.unsplash.com/photo-1518770660439-4636190af475?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80'
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchProducts()
})
</script>

<style scoped>
.products-page {
  max-width: var(--container-max-width);
  margin: 0 auto;
}

.products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.header-content {
  flex: 1;
  min-width: 250px;
}

.page-title {
  margin: 0;
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.page-subtitle {
  margin: var(--spacing-2) 0 0;
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
}

.filters {
  display: flex;
  gap: var(--spacing-3);
  flex-wrap: wrap;
}

.filter-select {
  width: 150px;
}

.search-input {
  width: 200px;
}

.loading-container {
  padding: var(--spacing-10) 0;
}

.empty-container {
  padding: var(--spacing-16) 0;
}

.empty-icon {
  font-size: 60px;
  color: var(--neutral-400);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

.product-card {
  height: 100%;
  cursor: pointer;
}

.product-image-container {
  position: relative;
  overflow: hidden;
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-4);
  aspect-ratio: 16 / 9;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal) ease;
}

.product-card:hover .product-image {
  transform: scale(1.05);
}

.product-tag {
  position: absolute;
  top: var(--spacing-3);
  right: var(--spacing-3);
  z-index: 1;
}

.product-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-2);
  color: var(--text-primary);
}

.product-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-4);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.product-price {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--accent-color);
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-8);
}

@media (max-width: 768px) {
  .products-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .filters {
    width: 100%;
  }

  .filter-select,
  .search-input {
    width: 100%;
  }
}
</style>
