import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    // 注释掉需要额外依赖的插件
    // 如需使用这些插件，请先安装相关依赖：
    // npm install -D rollup-plugin-visualizer vite-plugin-compression vite-plugin-imagemin

    // // 生成gzip压缩文件
    // viteCompression({
    //   verbose: true,
    //   disable: false,
    //   threshold: 10240, // 大于10kb的文件才会被压缩
    //   algorithm: 'gzip',
    //   ext: '.gz',
    // }),

    // // 图片压缩
    // viteImagemin({
    //   gifsicle: {
    //     optimizationLevel: 7,
    //     interlaced: false,
    //   },
    //   optipng: {
    //     optimizationLevel: 7,
    //   },
    //   mozjpeg: {
    //     quality: 80,
    //   },
    //   pngquant: {
    //     quality: [0.8, 0.9],
    //     speed: 4,
    //   },
    //   svgo: {
    //     plugins: [
    //       {
    //         name: 'removeViewBox',
    //       },
    //       {
    //         name: 'removeEmptyAttrs',
    //         active: false,
    //       },
    //     ],
    //   },
    // }),

    // // 打包分析
    // visualizer({
    //   open: false, // 自动打开分析报告
    //   gzipSize: true, // 显示gzip大小
    //   brotliSize: true, // 显示brotli大小
    //   filename: 'dist/stats.html', // 分析报告文件名
    // }),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  // 构建配置
  build: {
    // 启用CSS代码分割
    cssCodeSplit: true,
    // 启用源码映射
    sourcemap: false,
    // 设置块大小警告限制
    chunkSizeWarningLimit: 1000,
    // 自定义rollup配置
    rollupOptions: {
      // 输出配置
      output: {
        // 静态资源分类打包
        chunkFileNames: 'static/js/[name]-[hash].js',
        entryFileNames: 'static/js/[name]-[hash].js',
        assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
        // 代码分割策略
        manualChunks: {
          // 将Vue相关库打包到一起
          vue: ['vue', 'vue-router', 'pinia'],
          // 将Element Plus打包到一起
          elementPlus: ['element-plus', '@element-plus/icons-vue'],
          // 将其他第三方库打包到一起
          vendor: ['axios', 'pinia-plugin-persistedstate'],
        },
      },
    },
  },
  // 开发服务器配置
  server: {
    port: 5173,
    open: true,
    cors: true,
    // 代理配置
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
    },
  },
  // 预览服务器配置
  preview: {
    port: 5174,
    open: true,
  },
})
