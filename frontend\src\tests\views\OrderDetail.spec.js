import { mount } from '@vue/test-utils'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import OrderDetail from '../../views/OrderDetail.vue'
import { useOrderStore, useUserStore } from '../../store'
import { ElMessage, ElMessageBox } from 'element-plus'

// 模拟Element Plus组件
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
  },
  ElMessageBox: {
    confirm: vi.fn(),
  },
}))

describe('OrderDetail.vue', () => {
  let wrapper
  let orderStore
  let userStore
  let router
  
  // 模拟订单数据
  const mockOrder = {
    id: 1,
    order_number: 'ORD-**********',
    status: 'pending',
    total_amount: 100,
    created_at: '2023-11-21T12:34:56',
    updated_at: '2023-11-21T12:34:56',
    payment_method: null,
    items: [
      {
        id: 1,
        product_id: 1,
        product: { name: 'Test Product 1' },
        price: 100,
        quantity: 1
      }
    ]
  }
  
  // 模拟支付状态数据
  const mockPaymentStatus = {
    status: 'pending',
    payment_id: null,
    payment_status: null,
    payment_time: null
  }
  
  // 模拟许可密钥数据
  const mockLicenseKeys = [
    {
      id: 1,
      user_id: 1,
      product_id: 1,
      product: { name: 'Test Product 1' },
      order_id: 1,
      license_key: 'LICENSE-**********',
      is_active: true
    }
  ]
  
  beforeEach(() => {
    // 创建一个新的Pinia实例
    setActivePinia(createPinia())
    
    // 创建路由实例
    router = createRouter({
      history: createWebHistory(),
      routes: [
        {
          path: '/orders/:id',
          name: 'OrderDetail',
          component: OrderDetail
        }
      ]
    })
    
    // 设置当前路由
    router.push('/orders/1')
    
    // 获取store实例
    orderStore = useOrderStore()
    userStore = useUserStore()
    
    // 模拟store方法和属性
    orderStore.fetchOrderById = vi.fn().mockResolvedValue({ success: true, order: mockOrder })
    orderStore.checkPaymentStatus = vi.fn().mockResolvedValue({ success: true, status: mockPaymentStatus })
    orderStore.fetchLicenseKeys = vi.fn().mockResolvedValue({ success: true })
    orderStore.initiatePayment = vi.fn().mockResolvedValue({ success: true, paymentUrl: 'https://example.com/pay' })
    orderStore.mockPaymentSuccess = vi.fn().mockResolvedValue({ success: true })
    
    vi.spyOn(orderStore, 'currentOrder', 'get').mockReturnValue(mockOrder)
    vi.spyOn(userStore, 'isAdmin', 'get').mockReturnValue(false)
    
    // 模拟window.location
    delete window.location
    window.location = { href: '' }
    
    // 模拟navigator.clipboard
    Object.defineProperty(navigator, 'clipboard', {
      value: {
        writeText: vi.fn().mockResolvedValue(undefined)
      }
    })
    
    // 挂载组件
    wrapper = mount(OrderDetail, {
      global: {
        plugins: [router],
        stubs: ['el-button', 'el-tag', 'el-card', 'el-table', 'el-table-column', 'el-page-header'],
        provide: {
          orderStore,
          userStore
        }
      }
    })
  })
  
  it('fetches order details on mount', () => {
    // 验证fetchOrderById方法是否被调用
    expect(orderStore.fetchOrderById).toHaveBeenCalledWith(1)
    
    // 验证checkPaymentStatus方法是否被调用
    expect(orderStore.checkPaymentStatus).toHaveBeenCalledWith(1)
  })
  
  it('renders order details correctly', () => {
    // 验证订单号是否正确显示
    expect(wrapper.html()).toContain('ORD-**********')
    
    // 验证订单状态是否正确显示
    expect(wrapper.html()).toContain('待支付')
    
    // 验证订单总金额是否正确显示
    expect(wrapper.html()).toContain('¥100.00')
    
    // 验证产品信息是否正确显示
    expect(wrapper.html()).toContain('Test Product 1')
  })
  
  it('shows pay button for pending orders', () => {
    // 验证支付按钮是否显示
    expect(wrapper.html()).toContain('去支付')
  })
  
  it('initiates payment when pay button is clicked', async () => {
    // 找到支付按钮
    const payButton = wrapper.find('.order-actions button')
    
    // 模拟点击支付按钮
    await payButton.trigger('click')
    
    // 验证支付方法是否被调用
    expect(orderStore.initiatePayment).toHaveBeenCalledWith(1)
    
    // 验证页面是否跳转到支付链接
    expect(window.location.href).toBe('https://example.com/pay')
  })
  
  it('shows mock payment button for admin users', async () => {
    // 模拟管理员用户
    vi.spyOn(userStore, 'isAdmin', 'get').mockReturnValue(true)
    
    // 重新挂载组件
    wrapper = mount(OrderDetail, {
      global: {
        plugins: [router],
        stubs: ['el-button', 'el-tag', 'el-card', 'el-table', 'el-table-column', 'el-page-header'],
        provide: {
          orderStore,
          userStore
        }
      }
    })
    
    // 验证模拟支付按钮是否显示
    expect(wrapper.html()).toContain('模拟支付')
    
    // 模拟确认对话框返回Promise.resolve
    ElMessageBox.confirm.mockResolvedValue()
    
    // 找到模拟支付按钮
    const mockPaymentButton = wrapper.find('.order-actions button:nth-child(2)')
    
    // 模拟点击模拟支付按钮
    await mockPaymentButton.trigger('click')
    
    // 验证确认对话框是否被调用
    expect(ElMessageBox.confirm).toHaveBeenCalled()
    
    // 验证模拟支付方法是否被调用
    expect(orderStore.mockPaymentSuccess).toHaveBeenCalledWith(1)
    
    // 验证成功消息是否被显示
    expect(ElMessage.success).toHaveBeenCalledWith('模拟支付成功')
  })
  
  it('fetches license keys for paid orders', async () => {
    // 模拟已支付订单
    const paidOrder = { ...mockOrder, status: 'paid' }
    vi.spyOn(orderStore, 'currentOrder', 'get').mockReturnValue(paidOrder)
    
    // 重新挂载组件
    wrapper = mount(OrderDetail, {
      global: {
        plugins: [router],
        stubs: ['el-button', 'el-tag', 'el-card', 'el-table', 'el-table-column', 'el-page-header'],
        provide: {
          orderStore,
          userStore
        }
      }
    })
    
    // 验证fetchLicenseKeys方法是否被调用
    expect(orderStore.fetchLicenseKeys).toHaveBeenCalled()
  })
  
  it('formats date correctly', () => {
    // 获取组件实例
    const vm = wrapper.vm
    
    // 测试日期格式化方法
    const formattedDate = vm.formatDate('2023-11-21T12:34:56')
    
    // 验证格式化后的日期是否符合预期
    expect(formattedDate).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/)
  })
})
