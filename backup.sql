--
-- PostgreSQL database dump
--

-- Dumped from database version 16.9
-- Dumped by pg_dump version 16.9

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: license_keys; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.license_keys (
    id integer NOT NULL,
    user_id integer NOT NULL,
    product_id integer NOT NULL,
    license_key character varying(100) NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.license_keys OWNER TO "user";

--
-- Name: license_keys_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.license_keys_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.license_keys_id_seq OWNER TO "user";

--
-- Name: license_keys_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.license_keys_id_seq OWNED BY public.license_keys.id;


--
-- Name: order_items; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.order_items (
    id integer NOT NULL,
    order_id integer NOT NULL,
    product_id integer NOT NULL,
    quantity integer NOT NULL,
    price numeric(10,2) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.order_items OWNER TO "user";

--
-- Name: order_items_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.order_items_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.order_items_id_seq OWNER TO "user";

--
-- Name: order_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.order_items_id_seq OWNED BY public.order_items.id;


--
-- Name: orders; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.orders (
    id integer NOT NULL,
    order_number character varying(50) NOT NULL,
    user_id integer NOT NULL,
    total_amount numeric(10,2) NOT NULL,
    status character varying(20) DEFAULT 'pending'::character varying,
    payment_method character varying(20),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.orders OWNER TO "user";

--
-- Name: orders_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.orders_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.orders_id_seq OWNER TO "user";

--
-- Name: orders_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.orders_id_seq OWNED BY public.orders.id;


--
-- Name: payments; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.payments (
    id integer NOT NULL,
    order_id integer NOT NULL,
    payment_id character varying(100) NOT NULL,
    amount numeric(10,2) NOT NULL,
    status character varying(20) NOT NULL,
    payment_method character varying(20) NOT NULL,
    payment_time timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.payments OWNER TO "user";

--
-- Name: payments_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.payments_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.payments_id_seq OWNER TO "user";

--
-- Name: payments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.payments_id_seq OWNED BY public.payments.id;


--
-- Name: products; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.products (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    description text,
    price numeric(10,2) NOT NULL,
    stock integer DEFAULT 0,
    product_type character varying(20) NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.products OWNER TO "user";

--
-- Name: products_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.products_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.products_id_seq OWNER TO "user";

--
-- Name: products_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.products_id_seq OWNED BY public.products.id;


--
-- Name: site_info; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.site_info (
    id integer NOT NULL,
    company_name character varying(100) NOT NULL,
    description text,
    contact_info text,
    logo_url character varying(255),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.site_info OWNER TO "user";

--
-- Name: site_info_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.site_info_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.site_info_id_seq OWNER TO "user";

--
-- Name: site_info_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.site_info_id_seq OWNED BY public.site_info.id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.users (
    id integer NOT NULL,
    username character varying,
    email character varying,
    password_hash character varying,
    is_active boolean,
    role character varying,
    address character varying,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.users OWNER TO "user";

--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.users_id_seq OWNER TO "user";

--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: license_keys id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.license_keys ALTER COLUMN id SET DEFAULT nextval('public.license_keys_id_seq'::regclass);


--
-- Name: order_items id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.order_items ALTER COLUMN id SET DEFAULT nextval('public.order_items_id_seq'::regclass);


--
-- Name: orders id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.orders ALTER COLUMN id SET DEFAULT nextval('public.orders_id_seq'::regclass);


--
-- Name: payments id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.payments ALTER COLUMN id SET DEFAULT nextval('public.payments_id_seq'::regclass);


--
-- Name: products id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.products ALTER COLUMN id SET DEFAULT nextval('public.products_id_seq'::regclass);


--
-- Name: site_info id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.site_info ALTER COLUMN id SET DEFAULT nextval('public.site_info_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Data for Name: license_keys; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.license_keys (id, user_id, product_id, license_key, is_active, created_at, updated_at) FROM stdin;
1	1	2	BV3KW-RI7BR-CGP9A-91YU7-EP13O	t	2025-05-11 22:35:45.007222+00	2025-05-11 22:35:45.007222+00
2	3	2	OXVW5-T3D1Y-CRWVK-P2F72-ARFJ0	t	2025-05-11 23:44:40.361789+00	2025-05-11 23:44:40.361789+00
\.


--
-- Data for Name: order_items; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.order_items (id, order_id, product_id, quantity, price, created_at) FROM stdin;
1	1	2	1	499.00	2025-05-11 22:33:06.084057+00
2	2	2	1	499.00	2025-05-11 23:12:05.851534+00
3	3	6	1	3999.00	2025-05-11 23:14:36.180754+00
4	4	2	1	499.00	2025-05-11 23:39:06.878217+00
5	5	5	1	5999.00	2025-05-11 23:39:49.46993+00
6	6	2	1	499.00	2025-05-12 18:13:55.548047+00
7	7	3	1	199.00	2025-05-12 18:15:55.010413+00
8	8	3	1	199.00	2025-05-13 17:37:00.143842+00
9	9	3	1	199.00	2025-05-13 18:00:49.912279+00
10	10	3	1	199.00	2025-05-13 18:10:28.665907+00
11	11	3	1	199.00	2025-05-13 18:13:44.78787+00
12	12	3	1	199.00	2025-05-13 18:15:11.216056+00
13	13	3	1	199.00	2025-05-13 18:17:07.486535+00
\.


--
-- Data for Name: orders; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.orders (id, order_number, user_id, total_amount, status, payment_method, created_at, updated_at) FROM stdin;
1	ORD-1747002786-1-4545	1	499.00	paid	alipay	2025-05-11 22:33:06.084057+00	2025-05-11 22:35:44.98417+00
4	ORD-1747006746-3-6727	3	499.00	pending	\N	2025-05-11 23:39:06.878217+00	2025-05-11 23:39:06.878217+00
5	ORD-1747006789-3-2265	3	5999.00	pending	\N	2025-05-11 23:39:49.46993+00	2025-05-11 23:39:49.46993+00
2	ORD-1747005125-3-5189	3	499.00	paid	alipay	2025-05-11 23:12:05.851534+00	2025-05-11 23:44:40.301531+00
3	ORD-1747005276-3-9510	3	3999.00	paid	\N	2025-05-11 23:14:36.180754+00	2025-05-11 23:14:36.180754+00
6	ORD-1747073635-3-1906	3	499.00	pending	\N	2025-05-12 18:13:55.548047+00	2025-05-12 18:13:55.548047+00
7	ORD-1747073755-3-1138	3	199.00	pending	\N	2025-05-12 18:15:55.010413+00	2025-05-12 18:15:55.010413+00
8	ORD-1747157820-3-8233	3	199.00	pending	\N	2025-05-13 17:37:00.143842+00	2025-05-13 17:37:00.143842+00
9	ORD-1747159249-3-1638	3	199.00	pending	\N	2025-05-13 18:00:49.912279+00	2025-05-13 18:00:49.912279+00
10	ORD-1747159828-3-8229	3	199.00	pending	\N	2025-05-13 18:10:28.665907+00	2025-05-13 18:10:28.665907+00
11	ORD-1747160024-3-4658	3	199.00	pending	\N	2025-05-13 18:13:44.78787+00	2025-05-13 18:13:44.78787+00
12	ORD-1747160111-3-8353	3	199.00	pending	\N	2025-05-13 18:15:11.216056+00	2025-05-13 18:15:11.216056+00
13	ORD-1747160227-3-4434	3	199.00	pending	\N	2025-05-13 18:17:07.486535+00	2025-05-13 18:17:07.486535+00
\.


--
-- Data for Name: payments; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.payments (id, order_id, payment_id, amount, status, payment_method, payment_time, created_at, updated_at) FROM stdin;
1	1	MOCK17470029446552	499.00	success	alipay	2025-05-11 22:35:44.968881+00	2025-05-11 22:35:44.928908+00	2025-05-11 22:35:44.964991+00
2	2	MOCK17470070804881	499.00	success	alipay	2025-05-11 23:44:40.259341+00	2025-05-11 23:44:40.206558+00	2025-05-11 23:44:40.250994+00
\.


--
-- Data for Name: products; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.products (id, name, description, price, stock, product_type, is_active, created_at, updated_at) FROM stdin;
6	智能办公设备套装	包含多种智能办公设备，提升办公效率和体验。	3999.00	80	hardware	t	2025-05-11 20:30:46.431285+00	2025-05-11 20:30:46.431285+00
13	硬件产品1	这是一个硬件产品	999.99	50	hardware	t	2025-05-11 21:29:05.839681+00	2025-05-11 21:29:05.839681+00
2	标准版软件许可证	适合中小型企业的软件版本，包含核心功能和基本支持。	499.00	200	software	t	2025-05-11 20:30:46.389956+00	2025-05-11 21:45:25.0355+00
3	入门版软件许可证	适合个人用户和小型团队的基础版本，包含必要功能。	199.00	300	software	t	2025-05-11 20:30:46.401983+00	2025-05-11 21:45:38.175921+00
4	高性能服务器	企业级高性能服务器，适合大型应用和数据处理。	9999.00	50	hardware	t	2025-05-11 20:30:46.411389+00	2025-05-11 21:45:55.330807+00
5	网络安全设备	专业级网络安全防护设备，提供全方位的网络安全保障。	5999.00	30	hardware	t	2025-05-11 20:30:46.420583+00	2025-05-11 23:40:59.190096+00
29	测试产品	这是一个测试产品描述	299.99	100	software	t	2025-05-13 16:43:21.80013+00	2025-05-13 16:43:21.80013+00
\.


--
-- Data for Name: site_info; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.site_info (id, company_name, description, contact_info, logo_url, created_at, updated_at) FROM stdin;
1	xbai.fens 软件和设备销售平台	提供高质量的软件和硬件设备销售服务，满足您的各种需求	电话：135-456-7890 邮箱：<EMAIL>	\N	2025-05-11 18:42:12.173361+00	2025-05-13 17:44:59.958978+00
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.users (id, username, email, password_hash, is_active, role, address, created_at) FROM stdin;
4	testuser123	<EMAIL>	$2b$12$7pJS2KnvpCUqH.MDex9SpOETt6GjDXgKj5nm1K3I5tChq8JmL7f.e	t	user	123 Test St	2025-05-11 19:52:55.095396+00
1	admin	<EMAIL>	$2b$12$20x33.ddDjtOjn2qG6YQ3.qW.ShyIN8G14tD/O/EWNtucNJjDw4J6	t	admin	\N	2025-05-11 18:31:46.697804+00
3	testuser	<EMAIL>	$2b$12$Zj3.I6DyQetltpRCEB7gUO8Gm/A.d7uWypk/zEYFAsDgj/Sh3JBpC	t	user	123 Test St	2025-05-11 19:06:26.234237+00
\.


--
-- Name: license_keys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.license_keys_id_seq', 2, true);


--
-- Name: order_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.order_items_id_seq', 13, true);


--
-- Name: orders_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.orders_id_seq', 13, true);


--
-- Name: payments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.payments_id_seq', 2, true);


--
-- Name: products_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.products_id_seq', 30, true);


--
-- Name: site_info_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.site_info_id_seq', 1, true);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.users_id_seq', 4, true);


--
-- Name: license_keys license_keys_license_key_key; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.license_keys
    ADD CONSTRAINT license_keys_license_key_key UNIQUE (license_key);


--
-- Name: license_keys license_keys_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.license_keys
    ADD CONSTRAINT license_keys_pkey PRIMARY KEY (id);


--
-- Name: order_items order_items_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.order_items
    ADD CONSTRAINT order_items_pkey PRIMARY KEY (id);


--
-- Name: orders orders_order_number_key; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_order_number_key UNIQUE (order_number);


--
-- Name: orders orders_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_pkey PRIMARY KEY (id);


--
-- Name: payments payments_payment_id_key; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT payments_payment_id_key UNIQUE (payment_id);


--
-- Name: payments payments_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT payments_pkey PRIMARY KEY (id);


--
-- Name: products products_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.products
    ADD CONSTRAINT products_pkey PRIMARY KEY (id);


--
-- Name: site_info site_info_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.site_info
    ADD CONSTRAINT site_info_pkey PRIMARY KEY (id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: ix_users_email; Type: INDEX; Schema: public; Owner: user
--

CREATE UNIQUE INDEX ix_users_email ON public.users USING btree (email);


--
-- Name: ix_users_id; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX ix_users_id ON public.users USING btree (id);


--
-- Name: ix_users_username; Type: INDEX; Schema: public; Owner: user
--

CREATE UNIQUE INDEX ix_users_username ON public.users USING btree (username);


--
-- Name: license_keys license_keys_product_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.license_keys
    ADD CONSTRAINT license_keys_product_id_fkey FOREIGN KEY (product_id) REFERENCES public.products(id);


--
-- Name: license_keys license_keys_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.license_keys
    ADD CONSTRAINT license_keys_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: order_items order_items_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.order_items
    ADD CONSTRAINT order_items_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id);


--
-- Name: order_items order_items_product_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.order_items
    ADD CONSTRAINT order_items_product_id_fkey FOREIGN KEY (product_id) REFERENCES public.products(id);


--
-- Name: orders orders_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: payments payments_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT payments_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id);


--
-- PostgreSQL database dump complete
--

