# 管理员和站点API路由伪代码

## 1. 管理员API (api/admin.py)

```python
# 管理员API路由
router = APIRouter(prefix="/admin", tags=["admin"])

# 获取用户列表
@router.get("/users", response_model=ResponseModel)
async def get_users(
    skip: int = 0,
    limit: int = 10,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    users = await user_service.get_users(db, skip, limit)
    user_count = await user_service.get_user_count(db)
    
    return ResponseModel(
        success=True,
        message="获取用户列表成功",
        data={
            "users": [User.from_orm(user) for user in users],
            "total": user_count
        }
    )

# 获取销售统计
@router.get("/statistics", response_model=ResponseModel)
async def get_statistics(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    statistics = await admin_service.get_sales_statistics(db)
    return ResponseModel(
        success=True,
        message="获取销售统计成功",
        data=statistics
    )

# 获取所有订单
@router.get("/orders", response_model=ResponseModel)
async def get_all_orders(
    skip: int = 0,
    limit: int = 10,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    orders = await admin_service.get_all_orders(db, skip, limit)
    return ResponseModel(
        success=True,
        message="获取订单列表成功",
        data=[Order.from_orm(order) for order in orders]
    )

# 添加新产品
@router.post("/products", response_model=ResponseModel)
async def create_product(
    product_create: ProductCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    product = await product_service.create_product(db, product_create)
    return ResponseModel(
        success=True,
        message="产品创建成功",
        data=Product.from_orm(product)
    )

# 更新产品信息
@router.put("/products/{product_id}", response_model=ResponseModel)
async def update_product(
    product_id: int,
    product_update: ProductUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    product = await product_service.update_product(db, product_id, product_update)
    if not product:
        return ResponseModel(success=False, message="产品不存在")
    
    return ResponseModel(
        success=True,
        message="产品更新成功",
        data=Product.from_orm(product)
    )
```

## 2. 站点信息API (api/site.py)

```python
# 站点信息API路由
router = APIRouter(prefix="/site", tags=["site"])

# 获取站点信息
@router.get("/info", response_model=ResponseModel)
async def get_site_info(db: AsyncSession = Depends(get_db)):
    site_info = await admin_service.get_site_info(db)
    if not site_info:
        return ResponseModel(success=False, message="站点信息不存在")
    
    return ResponseModel(
        success=True,
        message="获取站点信息成功",
        data=SiteInfo.from_orm(site_info)
    )

# 更新站点信息（仅管理员）
@router.put("/info", response_model=ResponseModel)
async def update_site_info(
    site_info_update: SiteInfoUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    site_info = await admin_service.update_site_info(db, site_info_update)
    return ResponseModel(
        success=True,
        message="站点信息更新成功",
        data=SiteInfo.from_orm(site_info)
    )
```

## 3. 主应用入口 (main.py)

```python
# 主应用入口
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# 创建FastAPI应用
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="软件和设备销售平台API",
    version="0.1.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制为前端域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含API路由
app.include_router(auth_router, prefix=settings.API_V1_PREFIX)
app.include_router(user_router, prefix=settings.API_V1_PREFIX)
app.include_router(product_router, prefix=settings.API_V1_PREFIX)
app.include_router(payment_router, prefix=settings.API_V1_PREFIX)
app.include_router(admin_router, prefix=settings.API_V1_PREFIX)
app.include_router(site_router, prefix=settings.API_V1_PREFIX)

# 健康检查端点
@app.get("/health")
async def health_check():
    return {"status": "ok"}

# 应用启动事件
@app.on_event("startup")
async def startup_event():
    # 初始化数据库连接
    pass

# 应用关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    # 关闭数据库连接
    pass
```
