from typing import Annotated, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status

from app.core.database import get_db
from app.core.dependencies import get_current_admin_user
from app.core.schemas import PaginatedResponseModel, ResponseModel
from app.models.user import User
from app.schemas.product import Product as ProductSchema
from app.schemas.product import ProductCreate, ProductUpdate
from app.services.product import product_service

# 定义路由
router = APIRouter(prefix="/products", tags=["products"])

# 定义数据库依赖
DB = Annotated[get_db, Depends()]


@router.get("/", response_model=PaginatedResponseModel[List[ProductSchema]])
async def get_products(
    db: DB,
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(10, ge=1, le=100, description="限制记录数"),
    product_type: Optional[str] = Query(None, description="产品类型筛选"),
    is_active: Optional[bool] = Query(None, description="是否激活筛选"),
    admin: Optional[bool] = Query(False, description="是否是管理员请求"),
):
    """
    获取产品列表

    Args:
        db: 数据库会话
        skip: 跳过记录数
        limit: 限制记录数
        product_type: 产品类型筛选
        is_active: 是否激活筛选
        admin: 是否是管理员请求

    Returns:
        PaginatedResponseModel: 分页产品列表
    """
    # 如果不是管理员请求，且没有指定is_active参数，则只返回已激活的产品
    if not admin and is_active is None:
        is_active = True

    # 获取产品列表
    products = await product_service.get_products(
        db, skip=skip, limit=limit, product_type=product_type, is_active=is_active
    )
    # 获取产品总数
    total = await product_service.get_products_count(
        db, product_type=product_type, is_active=is_active
    )

    return PaginatedResponseModel(
        success=True,
        message="获取产品列表成功",
        data=products,
        total=total,
        page=skip // limit + 1,
        page_size=limit,
    )


@router.get("/{product_id}", response_model=ResponseModel[ProductSchema])
async def get_product(product_id: int, db: DB):
    """
    获取产品详情

    Args:
        product_id: 产品ID
        db: 数据库会话

    Returns:
        ResponseModel: 产品详情

    Raises:
        HTTPException: 产品不存在时抛出
    """
    # 获取产品
    product = await product_service.get_product_by_id(db, product_id)
    if not product or not product.is_active:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="产品不存在",
        )

    return ResponseModel(
        success=True,
        message="获取产品成功",
        data=product,
    )


# 管理员API
@router.get("/admin", response_model=PaginatedResponseModel[List[ProductSchema]])
async def get_all_products(
    db: DB,
    current_user: Annotated[User, Depends(get_current_admin_user)],
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(10, ge=1, le=100, description="限制记录数"),
    product_type: Optional[str] = Query(None, description="产品类型筛选"),
    is_active: Optional[bool] = Query(None, description="是否激活筛选"),
):
    """
    获取所有产品（仅管理员）

    Args:
        db: 数据库会话
        current_user: 当前管理员用户
        skip: 跳过记录数
        limit: 限制记录数
        product_type: 产品类型筛选
        is_active: 是否激活筛选

    Returns:
        PaginatedResponseModel: 分页产品列表
    """
    # 获取产品列表
    products = await product_service.get_products(
        db, skip=skip, limit=limit, product_type=product_type, is_active=is_active
    )
    # 获取产品总数
    total = await product_service.get_products_count(
        db, product_type=product_type, is_active=is_active
    )

    return PaginatedResponseModel(
        success=True,
        message="获取产品列表成功",
        data=products,
        total=total,
        page=skip // limit + 1,
        page_size=limit,
    )


@router.post("/admin", response_model=ResponseModel[ProductSchema])
async def create_product(
    product_create: ProductCreate,
    db: DB,
    current_user: Annotated[User, Depends(get_current_admin_user)],
):
    """
    创建产品（仅管理员）

    Args:
        product_create: 产品创建模型
        db: 数据库会话
        current_user: 当前管理员用户

    Returns:
        ResponseModel: 创建的产品
    """
    # 创建产品
    product = await product_service.create_product(db, product_create)

    return ResponseModel(
        success=True,
        message="创建产品成功",
        data=product,
    )


@router.put("/admin/{product_id}", response_model=ResponseModel[ProductSchema])
async def update_product(
    product_id: int,
    product_update: ProductUpdate,
    db: DB,
    current_user: Annotated[User, Depends(get_current_admin_user)],
):
    """
    更新产品（仅管理员）

    Args:
        product_id: 产品ID
        product_update: 产品更新模型
        db: 数据库会话
        current_user: 当前管理员用户

    Returns:
        ResponseModel: 更新后的产品

    Raises:
        HTTPException: 产品不存在时抛出
    """
    # 更新产品
    product = await product_service.update_product(db, product_id, product_update)
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="产品不存在",
        )

    return ResponseModel(
        success=True,
        message="更新产品成功",
        data=product,
    )


@router.patch("/admin/{product_id}/status", response_model=ResponseModel[ProductSchema])
async def toggle_product_status(
    product_id: int,
    is_active: bool,
    db: DB,
    current_user: Annotated[User, Depends(get_current_admin_user)],
):
    """
    切换产品状态（仅管理员）

    Args:
        product_id: 产品ID
        is_active: 是否激活
        db: 数据库会话
        current_user: 当前管理员用户

    Returns:
        ResponseModel: 更新后的产品

    Raises:
        HTTPException: 产品不存在时抛出
    """
    # 切换产品状态
    product = await product_service.toggle_product_status(db, product_id, is_active)
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="产品不存在",
        )

    return ResponseModel(
        success=True,
        message=f"产品已{'激活' if is_active else '禁用'}",
        data=product,
    )


@router.delete("/admin/{product_id}", response_model=ResponseModel)
async def delete_product(
    product_id: int,
    db: DB,
    current_user: Annotated[User, Depends(get_current_admin_user)],
):
    """
    删除产品（仅管理员）

    Args:
        product_id: 产品ID
        db: 数据库会话
        current_user: 当前管理员用户

    Returns:
        ResponseModel: 删除结果

    Raises:
        HTTPException: 产品不存在时抛出
    """
    # 删除产品
    result = await product_service.delete_product(db, product_id)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="产品不存在",
        )

    return ResponseModel(
        success=True,
        message="删除产品成功",
    )