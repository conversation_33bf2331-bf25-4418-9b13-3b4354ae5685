# 常用测试开发指南

## 1. 测试方法

我们的项目使用两种主要的测试方法：

### 1.1 London School TDD（也称为"Mockist TDD"或"Outside-In TDD"）与FastAPI结合

**核心原则**：
1. **外部驱动设计**：从外部接口开始测试，逐步向内部实现
2. **适度使用模拟对象**：使用mock/stub隔离被测单元与其依赖，但避免过度模拟
3. **验证交互而非状态**：关注组件之间的交互和消息传递，而非仅验证最终状态
4. **测试隔离性**：每个测试应该只测试一个单元，依赖应被模拟
5. **行为验证**：验证被测单元是否正确调用了其依赖（使用mock.assert_called_with等）

**FastAPI特性考虑**：
1. **异步支持**：使用`pytest-anyio`测试异步函数（推荐替代`pytest-asyncio`），使用`AsyncMock`模拟异步依赖
2. **依赖注入**：利用FastAPI的依赖注入系统进行测试，使用`app.dependency_overrides`和`Annotated`类型
3. **Pydantic模型**：利用Pydantic v2的验证功能减少手动验证代码，使用`pydantic_settings`管理配置
4. **测试客户端**：使用FastAPI的`TestClient`测试API端点，使用`AsyncClient`测试异步API
5. **WebSocket测试**：使用`TestClient.websocket_connect`测试WebSocket端点

**特点**：
- 使用模拟对象替代依赖，但避免过度模拟
- 验证函数与依赖的交互
- 关注函数的内部行为
- 测试单一职责
- 利用框架特性简化测试

### 1.2 集成测试

**特点**：
- 使用真实依赖（或接近真实的依赖）
- 验证多个组件协同工作的结果
- 关注功能的外部行为
- 测试端到端流程
- 测试事件处理器和生命周期钩子

## 2. 测试工具与命令

### 2.1 测试框架与插件

我们使用以下测试工具：

- **pytest**：主要测试框架
- **pytest-mock**：用于创建和管理模拟对象
- **pytest-anyio**：用于测试异步函数（推荐替代pytest-asyncio）
- **httpx**：用于测试HTTP客户端，包含`AsyncClient`用于异步测试
- **FastAPI TestClient**：用于测试API端点和WebSocket
- **pytest-docker**：用于在测试中启动临时Docker容器（如PostgreSQL）
- **factory-boy**：用于创建测试数据
- **pytest-cov**：用于测试覆盖率报告

### 2.2 测试命令

所有测试都通过 Docker 容器运行，而不是直接在命令行执行：

```bash
# 运行所有测试
docker-compose run --rm backend_tests pytest

# 运行所有单元测试
docker-compose run --rm backend_tests pytest tests/unit/ -v

# 运行所有集成测试
docker-compose run --rm backend_tests pytest tests/integration/ -v

# 运行特定单元测试文件
docker-compose run --rm backend_tests pytest tests/unit/user/test_login.py -v

# 运行特定集成测试文件
docker-compose run --rm backend_tests pytest tests/integration/user/test_user_integration.py -v

# 运行特定测试类
docker-compose run --rm backend_tests pytest tests/unit/user/test_login.py::TestLogin -v

# 运行特定测试方法
docker-compose run --rm backend_tests pytest tests/unit/user/test_login.py::TestLogin::test_login_success -v

# 运行异步测试（使用pytest-anyio标记）
docker-compose run --rm backend_tests pytest tests/unit/user/test_async_login.py -v

# 生成测试覆盖率报告
docker-compose run --rm backend_tests pytest --cov=app --cov-report=html tests/

# 运行WebSocket测试
docker-compose run --rm backend_tests pytest tests/integration/websocket/ -v

# 运行标记为"slow"的测试
docker-compose run --rm backend_tests pytest -m slow

# 排除标记为"slow"的测试
docker-compose run --rm backend_tests pytest -m "not slow"
```

## 3. 测试目录结构

我们的测试代码按照测试类型和功能模块进行组织：

```
backend/
└── tests/
    ├── unit/           # 单元测试
    ├── integration/    # 集成测试
    ├── fixtures/       # 共享测试夹具
    ├── performance/    # 性能测试
```

### 3.1 测试夹具组织

为了支持异步测试和SQLite数据库测试，我们在`fixtures`目录中组织了以下夹具：

#### 数据库测试夹具 (fixtures/db.py)
```python
import pytest
import os
import uuid
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from app.core.database import Base, get_db

# SQLite测试夹具
@pytest.fixture(scope="function")
async def test_db_engine():
    """创建测试数据库引擎"""
    # 使用SQLite内存数据库
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        connect_args={"check_same_thread": False}
    )

    # 创建表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    yield engine

    # 清理
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)

@pytest.fixture(scope="function")
async def test_db_session(test_db_engine):
    """创建测试数据库会话"""
    async_session = sessionmaker(
        test_db_engine,
        class_=AsyncSession,
        expire_on_commit=False
    )

    async with async_session() as session:
        # 开始事务
        async with session.begin():
            # 提供会话
            yield session
            # 事务会自动回滚

@pytest.fixture(scope="function")
async def override_get_db(test_db_session):
    """覆盖依赖注入的数据库会话"""
    async def _override_get_db():
        yield test_db_session

    return _override_get_db

# PostgreSQL测试夹具
@pytest.fixture(scope="session")
def postgres_container():
    """启动PostgreSQL容器用于测试"""
    import docker
    import time

    client = docker.from_env()
    container = client.containers.run(
        "postgres:16",
        environment={
            "POSTGRES_PASSWORD": "postgres",
            "POSTGRES_USER": "postgres",
            "POSTGRES_DB": "test_db"
        },
        ports={"5432/tcp": 5433},  # 映射到不同端口避免冲突
        detach=True,
    )

    # 等待数据库启动
    time.sleep(3)

    yield container

    # 清理
    container.stop()
    container.remove()

@pytest.fixture(scope="function")
async def pg_test_db_engine(postgres_container):
    """创建PostgreSQL测试数据库引擎"""
    engine = create_async_engine(
        "postgresql+asyncpg://postgres:postgres@localhost:5433/test_db",
    )

    # 创建表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    yield engine

    # 清理表（但保留数据库）
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)

@pytest.fixture(scope="function")
async def pg_test_db_session(pg_test_db_engine):
    """创建PostgreSQL测试数据库会话"""
    async_session = sessionmaker(
        pg_test_db_engine,
        class_=AsyncSession,
        expire_on_commit=False
    )

    async with async_session() as session:
        # 开始事务
        async with session.begin():
            # 提供会话
            yield session
            # 事务会自动回滚

# 集成测试夹具
@pytest.fixture(scope="module")
async def integration_db_engine():
    """创建集成测试数据库引擎（文件型SQLite）"""
    # 创建唯一的测试数据库文件
    test_db_id = uuid.uuid4().hex
    db_path = f"./test_db_{test_db_id}.sqlite"

    # 创建引擎
    engine = create_async_engine(
        f"sqlite+aiosqlite:///{db_path}",
        connect_args={"check_same_thread": False}
    )

    # 创建表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    yield engine

    # 清理
    if os.path.exists(db_path):
        os.remove(db_path)

@pytest.fixture(scope="function")
async def integration_db_session(integration_db_engine):
    """创建集成测试数据库会话"""
    async_session = sessionmaker(
        integration_db_engine,
        class_=AsyncSession,
        expire_on_commit=False
    )

    async with async_session() as session:
        # 开始事务
        async with session.begin():
            # 提供会话
            yield session
            # 事务会自动回滚
```

#### 认证测试夹具 (fixtures/auth.py)
```python
import pytest
from fastapi import Depends
from app.user.models import User
from app.core.security import create_access_token
from app.core.dependencies import get_current_user

@pytest.fixture
async def test_user(test_db_session):
    """创建测试用户"""
    from app.user.models import User
    import uuid

    # 创建测试用户
    user = User(
        id=uuid.uuid4(),
        username="testuser",
        email="<EMAIL>",
        password_hash="$2b$12$IKEQb00u5eHhkplO0/xR0.LZ8tBO4LmqGwV32uKQ.WW8.ZGilGKXS",  # 'password123'
        is_active=True
    )
    test_db_session.add(user)
    await test_db_session.flush()

    return user

@pytest.fixture
def test_token(test_user):
    """创建测试用户的访问令牌"""
    return create_access_token(data={"sub": str(test_user.id)})

@pytest.fixture
def override_get_current_user(test_user):
    """覆盖当前用户依赖"""
    async def _override_get_current_user():
        return test_user

    return _override_get_current_user
```

#### API测试夹具 (fixtures/api.py)
```python
import pytest
from fastapi.testclient import TestClient
from httpx import AsyncClient
from app.main import app

@pytest.fixture
def client(override_get_db):
    """创建测试客户端"""
    # 覆盖数据库依赖
    app.dependency_overrides[get_db] = override_get_db

    with TestClient(app) as client:
        yield client

    # 清理
    app.dependency_overrides.clear()

@pytest.fixture
def auth_client(client, test_token):
    """创建已认证的测试客户端"""
    def _auth_client():
        return client, {"Authorization": f"Bearer {test_token}"}

    return _auth_client

@pytest.fixture
async def async_client(override_get_db):
    """创建异步测试客户端"""
    # 覆盖数据库依赖
    app.dependency_overrides[get_db] = override_get_db

    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac

    # 清理
    app.dependency_overrides.clear()
```

## 4. 容器目录与本地目录对应关系

在 Docker 容器中，目录结构与本地有所不同：

| 本地目录 | 容器目录 |
|---------|---------|
| `backend/app` | `/app/app` |
| `backend/tests` | `/app/tests` |

**重要提示**：
- 在运行测试时，不要在路径中包含 'backend'
- 例如，要测试 `backend/tests/unit/user/test_login.py`，命令应该是：
  ```bash
  docker-compose run --rm backend_tests pytest tests/unit/user/test_login.py -v
  ```

## 5. 代码开发流程

我们遵循结合FastAPI特性的London School TDD开发流程：

### 5.1 编写单元测试

1. **创建测试文件**：在 `backend/tests/unit` 目录下创建单元测试文件
2. **编写测试用例**：定义测试用例，适度使用模拟对象替代依赖
3. **验证交互**：验证被测函数与依赖的交互

#### 异步函数测试（推荐）

FastAPI是一个异步框架，我们应该优先使用异步函数和异步测试：

```python
import pytest
from unittest.mock import AsyncMock

# 使用pytest-anyio进行异步测试（推荐）
@pytest.mark.anyio
async def test_async_logout_user_success(mocker):
    # 准备测试数据
    token = "valid_session_token"

    # 模拟异步依赖
    mock_repo = AsyncMock()
    mock_repo.invalidate_session.return_value = True

    mock_logger = mocker.patch('app.user.auth_service.log_event')

    # 调用异步服务层函数
    result = await logout_user(token, user_repo=mock_repo)

    # 验证结果
    assert result["success"] is True

    # 验证交互
    mock_repo.invalidate_session.assert_called_once_with(token)
    mock_logger.assert_called_once()
```

#### 使用Annotated类型的依赖注入

```python
from typing import Annotated
from fastapi import Depends, FastAPI

# 定义依赖类型
UserRepoDep = Annotated[UserRepository, Depends(get_user_repository)]

# 在服务函数中使用依赖类型
async def logout_user(
    token: str,
    user_repo: UserRepoDep,
):
    # 实现逻辑...
    pass
```

#### 同步函数测试（仅用于特殊情况）

在某些特殊情况下（如CPU绑定操作或第三方库集成），可能需要测试同步函数：

```python
def test_sync_function(mocker):
    # 准备测试数据
    input_data = "test_input"

    # 模拟依赖
    mock_dependency = mocker.patch('app.module.some_dependency')
    mock_dependency.process.return_value = "processed_result"

    # 调用同步函数
    result = process_data(input_data)

    # 验证结果
    assert result == "processed_result"

    # 验证交互
    mock_dependency.process.assert_called_once_with(input_data)
```

> **注意**：尽量将同步函数转换为异步函数，以充分利用FastAPI的异步特性。

### 5.2 实现代码

1. **创建实现文件**：在 `backend/app` 目录下创建实现文件
2. **实现函数**：根据测试用例的预期实现函数，利用FastAPI特性
3. **运行测试**：确保测试通过

#### 异步函数实现（推荐）

在FastAPI中，我们应该优先使用异步函数来实现业务逻辑：

```python
async def logout_user(
    session_token: Optional[str] = None,
    user_id: Optional[str] = None,
    user_repo=None,
    logger=None
) -> Dict[str, Any]:
    """
    用户登出服务

    Args:
        session_token: 会话令牌（可选，与user_id二选一）
        user_id: 用户ID（可选，与session_token二选一）
        user_repo: 用户仓库（可选，用于依赖注入）
        logger: 日志服务（可选，用于依赖注入）

    Returns:
        包含登出结果的字典
    """
    # 设置默认依赖
    user_repo = user_repo or UserRepository()
    logger = logger or log_event

    # 验证输入
    if not session_token and not user_id:
        raise ValidationException(message="会话令牌和用户ID不能同时为空")

    try:
        # 根据不同的输入参数执行不同的登出逻辑
        if session_token:
            # 使单个会话失效
            result = await user_repo.invalidate_session(session_token)

            if result:
                # 提取用户ID
                extracted_user_id = session_token.replace("token_for_", "") if session_token.startswith("token_for_") else "unknown"

                # 记录登出成功
                logger("用户登出成功", user_id=extracted_user_id)

                # 返回成功结果
                return {"success": True, "message": "登出成功"}
            else:
                # 记录登出失败
                logger("用户登出失败", token=session_token, reason="无效的会话")

                # 返回失败结果
                return {"success": False, "message": "无效的会话"}
        else:
            # 使用户的所有会话失效
            sessions_terminated = await user_repo.invalidate_all_user_sessions(user_id)

            # 记录登出成功
            logger("用户所有会话已登出", user_id=user_id, sessions_terminated=sessions_terminated)

            # 返回成功结果
            return {
                "success": True,
                "message": "所有会话已登出",
                "sessions_terminated": sessions_terminated
            }
    except Exception as e:
        # 记录异常
        logger("用户登出异常", error=str(e), token=session_token, user_id=user_id)
        # 重新抛出异常
        raise
```

#### 同步函数实现（仅用于特殊情况）

在某些特殊情况下，如CPU密集型操作或与同步第三方库集成时，可能需要使用同步函数：

```python
def process_data(input_data: str, processor=None):
    """
    处理数据（同步版本，用于CPU密集型操作）

    Args:
        input_data: 输入数据
        processor: 数据处理器（可选，用于依赖注入）

    Returns:
        处理后的数据
    """
    # 设置默认依赖
    processor = processor or default_processor

    # 验证输入
    if not input_data:
        raise ValueError("输入数据不能为空")

    # 处理数据（CPU密集型操作）
    try:
        result = processor.process(input_data)
        return result
    except Exception as e:
        logger.error(f"数据处理失败: {str(e)}")
        raise
```

> **最佳实践**：即使对于CPU密集型操作，也可以考虑使用异步函数包装，并使用`run_in_threadpool`在线程池中执行：
>
> ```python
> from fastapi.concurrency import run_in_threadpool
>
> async def process_data_async(input_data: str, processor=None):
>     """异步包装的CPU密集型操作"""
>     return await run_in_threadpool(process_data, input_data, processor)
> ```

### 5.3 实现API端点

利用FastAPI的依赖注入系统实现API端点：

```python
from typing import Annotated
from fastapi import APIRouter, Depends
from fastapi.security import OAuth2PasswordBearer

router = APIRouter(prefix="/user", tags=["User"])

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/user/login")

# 使用Annotated类型定义依赖
TokenDep = Annotated[str, Depends(oauth2_scheme)]
DbSessionDep = Annotated[AsyncSession, Depends(get_db)]

@router.post("/logout", response_model=SuccessResponse)
async def logout(
    token: TokenDep,
    db: DbSessionDep
):
    """
    用户登出API

    使当前会话失效，防止未授权访问
    """
    try:
        # 创建仓库
        user_repo = UserRepository(db_session=db)

        # 调用服务层函数
        result = await logout_user(session_token=token, user_repo=user_repo)

        if result["success"]:
            return success_response(message=result["message"])
        else:
            raise ValidationException(message=result["message"])
    except ValidationException as e:
        raise ValidationException(message=str(e))
    except Exception as e:
        raise AppException(message=f"登出服务内部错误: {str(e)}")
```

### 5.4 编写API测试

```python
from fastapi.testclient import TestClient
from app.main import app
import pytest

# 覆盖依赖
app.dependency_overrides[get_db] = override_get_db
app.dependency_overrides[get_current_user] = override_get_current_user

def test_logout_api_success():
    """测试登出API成功"""
    with TestClient(app) as client:
        # 先登录获取token
        login_response = client.post(
            "/user/login",
            data={"username": "testuser", "password": "password123"}
        )
        token = login_response.json()["data"]["access_token"]

        # 使用token调用登出API
        response = client.post(
            "/user/logout",
            headers={"Authorization": f"Bearer {token}"}
        )

        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "登出成功" in data["message"]
```

#### 使用AsyncClient测试异步API

```python
import pytest
from httpx import AsyncClient

@pytest.mark.anyio
async def test_async_logout_api():
    """使用AsyncClient测试异步API端点"""
    # 覆盖依赖
    app.dependency_overrides[get_db] = override_get_db

    # 创建测试token
    token = create_access_token(data={"sub": "testuser"})

    # 使用AsyncClient测试
    async with AsyncClient(app=app, base_url="http://test") as client:
        response = await client.post(
            "/user/logout",
            headers={"Authorization": f"Bearer {token}"}
        )

        # 验证响应
        assert response.status_code == 200
        assert response.json()["success"] is True
```

### 5.5 编写集成测试

使用真实依赖或接近真实的依赖进行集成测试：

```python
import pytest

@pytest.mark.asyncio
class TestLogoutIntegration:
    async def test_logout_success(self, test_db_session):
        """测试成功登出"""
        # 准备测试数据
        from app.user.models import User, Session
        import uuid

        # 创建测试用户
        user = User(
            id=uuid.uuid4(),
            username="testuser",
            email="<EMAIL>",
            password_hash="hashed_password"
        )
        test_db_session.add(user)
        await test_db_session.commit()

        # 创建测试会话
        token = f"token_for_{user.username}"
        session = Session(
            id=uuid.uuid4(),
            token=token,
            user_id=user.id,
            expires_at=datetime.now() + timedelta(hours=1),
            is_active=True
        )
        test_db_session.add(session)
        await test_db_session.commit()

        # 创建仓库
        user_repo = UserRepository(db_session=test_db_session)

        # 调用服务函数
        result = await logout_user(token, user_repo=user_repo)

        # 验证结果
        assert result["success"] is True

        # 验证会话已失效
        db_session = await test_db_session.execute(
            select(Session).where(Session.token == token)
        )
        db_session = db_session.scalar_one_or_none()
        assert db_session is not None
        assert db_session.is_active is False
```

## 6. API规范与命名约定

### 6.1 OpenAPI规范

我们的API遵循OpenAPI规范，并采用以下命名约定：

1. **命名风格统一**：
   - 所有参数和字段使用一致的命名风格（snake_case）
   - 例如：`user_id`, `software_id`, `product_id`, `order_id`
   - 避免使用驼峰命名法（camelCase）如 `userId`, `softwareId`

2. **路径格式统一**：
   - 使用斜杠分隔的路径格式：`/resource/action`
   - 例如：`/purchase/initiate`, `/purchase/callback`, `/purchase/result/{order_id}`
   - 避免使用短横线格式：`/purchase-initiate`, `/purchase-callback`

3. **路由组织原则**：
   - 路由应按功能模块组织，保持关注点分离
   - 用户相关路由应在 `user_router.py` 中
   - 订单相关路由应在 `order_router.py` 中
   - 管理员相关路由应在 `admin_router.py` 中

4. **响应格式统一**：
   - 成功响应应包含统一的结构
   - 错误响应应包含统一的错误码和消息格式

遵循这些规范可以确保API的一致性和可维护性，同时提高开发效率和代码质量。

## 7. FastAPI与数据库异步测试最佳实践

### 7.1 数据库异步测试策略

在FastAPI异步应用中，我们可以使用SQLite或PostgreSQL作为测试数据库：

#### 数据库配置

```python
# app/core/database.py
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, declarative_base
import os

# 创建基类
Base = declarative_base()

# 数据库URL配置
DATABASE_URL = os.environ.get(
    "DATABASE_URL",
    "postgresql+asyncpg://postgres:postgres@localhost:5432/app_db"
)

# 测试模式使用SQLite
if os.environ.get("TESTING", "false").lower() == "true":
    DATABASE_URL = "sqlite+aiosqlite:///:memory:"

# 创建异步引擎
engine = create_async_engine(
    DATABASE_URL,
    connect_args={"check_same_thread": False} if DATABASE_URL.startswith("sqlite") else {}
)

# 创建异步会话工厂
async_session = sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# 依赖注入函数
async def get_db():
    """获取数据库会话（用于依赖注入）"""
    async with async_session() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
```

#### 测试数据库夹具

```python
# tests/fixtures/db.py
import pytest
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from app.core.database import Base, get_db

@pytest.fixture(scope="function")
async def test_db_engine():
    """创建测试数据库引擎"""
    # 使用SQLite内存数据库
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        connect_args={"check_same_thread": False}
    )

    # 创建表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    yield engine

    # 清理
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)

@pytest.fixture(scope="function")
async def test_db_session(test_db_engine):
    """创建测试数据库会话"""
    async_session = sessionmaker(
        test_db_engine,
        class_=AsyncSession,
        expire_on_commit=False
    )

    async with async_session() as session:
        # 开始事务
        async with session.begin():
            # 提供会话
            yield session
            # 事务会自动回滚

@pytest.fixture(scope="function")
async def override_get_db(test_db_session):
    """覆盖依赖注入的数据库会话"""
    async def _override_get_db():
        yield test_db_session

    return _override_get_db
```

### 7.2 FastAPI特性利用

在测试中充分利用FastAPI的特性：

#### 依赖注入测试

```python
# 覆盖依赖
app.dependency_overrides[get_db] = override_get_db
app.dependency_overrides[get_current_user] = override_get_current_user

# 测试需要认证的端点
def test_protected_endpoint():
    with TestClient(app) as client:
        response = client.get("/protected-route")
        assert response.status_code == 200
```

#### Pydantic模型测试

```python
# 测试Pydantic模型验证
def test_user_model_validation():
    # 有效数据
    valid_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "Password123!"
    }
    user = UserCreate(**valid_data)
    assert user.username == "testuser"

    # 无效数据
    invalid_data = {
        "username": "t",  # 太短
        "email": "invalid-email",
        "password": "short"
    }
    with pytest.raises(ValidationError):
        UserCreate(**invalid_data)
```

#### 异步测试

```python
@pytest.mark.asyncio
async def test_async_endpoint(test_db_session):
    # 准备测试数据
    user = User(username="testuser", email="<EMAIL>")
    test_db_session.add(user)
    await test_db_session.commit()

    # 测试异步函数
    result = await get_user_by_username("testuser", db=test_db_session)
    assert result is not None
    assert result.username == "testuser"
```

### 7.3 异步测试数据工厂

使用工厂模式创建异步测试数据：

```python
# tests/factories.py
import factory
import uuid
from app.user.models import User
from app.order.models import Order

class BaseFactory(factory.Factory):
    """基础工厂类，提供异步支持"""

    @classmethod
    async def create_async(cls, **kwargs):
        """异步创建实例"""
        return cls.create(**kwargs)

    @classmethod
    async def create_batch_async(cls, size, **kwargs):
        """异步批量创建实例"""
        return cls.create_batch(size, **kwargs)

class UserFactory(BaseFactory):
    class Meta:
        model = User

    id = factory.LazyFunction(lambda: uuid.uuid4())
    username = factory.Sequence(lambda n: f"user{n}")
    email = factory.LazyAttribute(lambda o: f"{o.username}@example.com")
    password_hash = factory.LazyFunction(lambda: "$2b$12$IKEQb00u5eHhkplO0/xR0.LZ8tBO4LmqGwV32uKQ.WW8.ZGilGKXS")  # 'password123'
    is_active = True

class OrderFactory(BaseFactory):
    class Meta:
        model = Order

    id = factory.LazyFunction(lambda: uuid.uuid4())
    order_number = factory.Sequence(lambda n: f"ORD-{n:06d}")
    status = "pending"
    amount = factory.Faker('pydecimal', left_digits=3, right_digits=2, positive=True)

    @factory.post_generation
    def user(self, create, extracted, **kwargs):
        """设置订单用户"""
        if not create:
            return

        if extracted:
            self.user_id = extracted.id

# 在测试中异步使用
@pytest.mark.asyncio
async def test_with_factory(test_db_session):
    # 创建测试用户
    user = await UserFactory.create_async()
    test_db_session.add(user)
    await test_db_session.flush()

    # 创建测试订单
    order = await OrderFactory.create_async(user=user)
    test_db_session.add(order)
    await test_db_session.flush()

    # 测试代码...
    result = await test_db_session.execute(
        select(Order).where(Order.user_id == user.id)
    )
    orders = result.scalars().all()
    assert len(orders) == 1
    assert orders[0].order_number.startswith("ORD-")
```

#### 使用工厂与SQLite的最佳实践

1. **批量创建数据**：
   ```python
   # 批量创建用户
   users = await UserFactory.create_batch_async(10)
   for user in users:
       test_db_session.add(user)
   await test_db_session.flush()
   ```

2. **创建关联数据**：
   ```python
   # 创建用户及其订单
   user = await UserFactory.create_async()
   test_db_session.add(user)
   await test_db_session.flush()

   # 为用户创建多个订单
   orders = await OrderFactory.create_batch_async(3, user=user)
   for order in orders:
       test_db_session.add(order)
   await test_db_session.flush()
   ```

3. **使用事务管理数据**：
   ```python
   async with test_db_session.begin():
       user = await UserFactory.create_async()
       test_db_session.add(user)
       # 事务会自动提交或回滚
   ```

## 8. 测试命名规范

为了保持测试代码的一致性和可读性，我们采用以下命名规范：

### 8.1 单元测试

- **测试文件名**：`test_<模块名>_<测试类型>.py`
  - 例如：`test_user_services.py`、`test_order_repository.py`
  - 异步测试可以使用：`test_async_user_services.py`

- **测试类名**：`Test<模块名><测试类型>`
  - 例如：`TestUserServices`、`TestOrderRepository`
  - 异步测试类可以使用：`TestAsyncUserServices`

- **测试方法名**：`test_<功能>_<场景>`
  - 例如：`test_create_user_success`、`test_create_user_validation_error`
  - 异步测试方法同样使用此命名规范，但需要添加`@pytest.mark.asyncio`装饰器

### 8.2 集成测试

- **测试文件名**：`test_<模块名>_<测试类型>.py`
  - 例如：`test_user_integration.py`、`test_order_api.py`

- **测试类名**：`Test<模块名>Integration`
  - 例如：`TestUserIntegration`、`TestOrderIntegration`

- **测试方法名**：`test_<功能>_<场景>`
  - 例如：`test_login_success`、`test_login_invalid_credentials`

### 8.3 数据库测试

- **测试文件名**：`test_<模块名>_db.py`
  - 例如：`test_user_db.py`、`test_order_db.py`

- **测试类名**：`Test<模块名>Db`
  - 例如：`TestUserDb`、`TestOrderDb`

- **测试方法名**：`test_<操作>_<场景>`
  - 例如：`test_insert_success`、`test_query_by_id_not_found`

遵循这些命名规范可以使测试代码更加清晰和一致，便于团队成员理解和维护。

## 9. 总结：FastAPI与数据库异步测试的最佳结合

在使用FastAPI框架时，我们可以通过以下方式优化测试方法：

### 9.1 保留London School TDD的优点

1. **外部驱动设计**：从外部接口开始测试，逐步向内部实现
2. **关注组件交互**：验证组件之间的交互和消息传递
3. **测试隔离性**：保持测试的隔离性，使每个测试只关注一个单元

### 9.2 充分利用FastAPI的最新特性

1. **全异步测试**：使用`pytest-anyio`进行异步测试
2. **异步模拟**：使用`AsyncMock`模拟异步依赖
3. **现代依赖注入**：利用FastAPI的`Annotated`类型和依赖注入系统
4. **异步数据库操作**：使用SQLAlchemy的异步API

### 9.3 数据库测试策略

1. **SQLite优势**：
   - 速度快、配置简单
   - 内存数据库提供良好隔离性
   - 通过`aiosqlite`支持异步操作

2. **PostgreSQL优势**：
   - 支持所有生产环境特性
   - 可以测试数据库特定功能

### 9.4 分层测试策略

1. **单元测试**：使用异步SQLite内存数据库和模拟对象
2. **API测试**：使用TestClient和AsyncClient测试端点
3. **集成测试**：测试多个组件协同工作，关注端到端流程

通过这种方式，我们可以创建高效、可靠的测试代码，同时保持测试的简单性和可维护性。
