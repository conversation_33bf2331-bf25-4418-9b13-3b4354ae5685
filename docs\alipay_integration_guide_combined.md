# Alipay沙箱集成指南

本文档详细介绍了如何在销售平台项目中集成支付宝沙箱环境，实现真实的支付流程测试，同时保留模拟支付功能作为备选方案。

## 一、当前支付系统状态

目前，项目已经实现了模拟支付功能，但尚未集成真正的Alipay沙箱。系统中的支付流程如下：

1. 用户创建订单后，可以点击"支付"按钮
2. 系统生成一个模拟支付链接，将用户引导到模拟支付页面
3. 用户在模拟支付页面确认支付
4. 系统调用`mock_payment_success`接口，模拟支付成功
5. 订单状态更新为"已支付"，如果是软件产品，系统会生成许可密钥

## 二、集成Alipay沙箱的步骤

### 1. 申请Alipay开发者账号和沙箱环境

1. 访问[支付宝开放平台](https://open.alipay.com/)，注册开发者账号
2. 创建应用，选择"自研开发者应用"
3. 在应用详情页面，找到"沙箱环境"，开通沙箱测试功能
4. 获取沙箱环境的应用信息：
   - APPID
   - 支付宝公钥
   - 应用私钥

### 2. 生成RSA密钥对

您需要生成RSA密钥对用于签名验证：

```bash
# 生成私钥
openssl genrsa -out app_private_key.pem 2048

# 生成公钥
openssl rsa -in app_private_key.pem -pubout -out app_public_key.pem

# 将私钥转换为PKCS8格式（支付宝要求）
openssl pkcs8 -topk8 -inform PEM -in app_private_key.pem -outform PEM -nocrypt -out app_private_key_pkcs8.pem
```

### 3. 安装Python Alipay SDK

在后端项目中安装支付宝SDK：

```bash
docker exec -it sales_platform_backend pip install python-alipay-sdk --upgrade
```

### 4. 配置环境变量

修改`.env`文件或`docker-compose.yml`中的环境变量：

```yaml
ALIPAY_APP_ID: "您的沙箱APPID"
ALIPAY_PRIVATE_KEY_PATH: "/app/keys/app_private_key.pem"
ALIPAY_PUBLIC_KEY_PATH: "/app/keys/alipay_public_key.pem"
ALIPAY_NOTIFY_URL: "http://localhost:8000/api/v1/payments/notify"
ALIPAY_RETURN_URL: "http://localhost:5173/payment-result"
```

### 5. 创建密钥目录并上传密钥文件

```bash
# 在后端容器中创建密钥目录
docker exec -it sales_platform_backend mkdir -p /app/keys

# 将密钥文件复制到容器中
docker cp app_private_key.pem sales_platform_backend:/app/keys/
docker cp alipay_public_key.pem sales_platform_backend:/app/keys/
```

### 6. 修改支付服务代码

修改`backend/app/services/payment.py`文件，启用真正的Alipay集成：

```python
from alipay import AliPay
from alipay.utils import AliPayConfig
from app.core.config import settings

# 在PaymentService类中添加初始化方法
def __init__(self):
    """初始化支付服务"""
    # 尝试初始化支付宝客户端
    try:
        self.alipay_client = AliPay(
            appid=settings.ALIPAY_APP_ID,
            app_notify_url=settings.ALIPAY_NOTIFY_URL,
            app_private_key_string=open(settings.ALIPAY_PRIVATE_KEY_PATH).read(),
            alipay_public_key_string=open(settings.ALIPAY_PUBLIC_KEY_PATH).read(),
            sign_type="RSA2",
            debug=True  # 沙箱环境设置为True
        )
        self.alipay_config = AliPayConfig(timeout=15)  # 设置超时时间
        self.alipay_enabled = True
        print("支付宝客户端初始化成功")
    except Exception as e:
        print(f"支付宝客户端初始化失败: {str(e)}")
        self.alipay_enabled = False
```

### 7. 修改生成支付链接的方法

修改`generate_payment_url`方法，使用真实的Alipay沙箱：

```python
async def generate_payment_url(self, db: AsyncSession, order_id: int) -> str:
    """生成支付链接"""
    # 获取订单信息
    order = await order_service.get_order_by_id(db, order_id, include_items=True)
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单不存在",
        )

    if order.status != "pending":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="订单状态不正确",
        )

    # 如果支付宝客户端可用，使用支付宝支付
    if hasattr(self, 'alipay_client') and self.alipay_enabled:
        try:
            # 获取订单商品名称
            product_names = []
            for item in order.order_items:
                product = await product_service.get_product_by_id(db, item.product_id)
                if product:
                    product_names.append(product.name)

            # 商品名称
            subject = "购买 " + ", ".join(product_names) if product_names else f"订单 {order.order_number}"

            # 生成支付宝订单
            order_string = self.alipay_client.api_alipay_trade_page_pay(
                out_trade_no=order.order_number,
                total_amount=str(order.total_amount),
                subject=subject,
                return_url=settings.ALIPAY_RETURN_URL,
                notify_url=settings.ALIPAY_NOTIFY_URL
            )

            # 返回支付链接（沙箱环境）
            return f"https://openapi-sandbox.dl.alipaydev.com/gateway.do?{order_string}"
        except Exception as e:
            print(f"生成支付宝支付链接失败: {str(e)}")
            # 如果生成支付宝链接失败，回退到模拟支付

    # 使用模拟支付链接
    return f"http://localhost:5173/mock-payment?order_id={order.id}&order_number={order.order_number}&amount={order.total_amount}"
```

### 8. 修改支付回调处理方法

修改`handle_payment_notification`方法，处理真实的Alipay回调：

```python
async def handle_payment_notification(self, db: AsyncSession, data: dict) -> bool:
    """处理支付回调"""
    # 如果支付宝客户端可用，验证支付宝回调
    if hasattr(self, 'alipay_client') and self.alipay_enabled:
        try:
            # 验证签名
            signature = data.pop("sign", None)
            if not self.alipay_client.verify(data, signature):
                print("支付宝回调签名验证失败")
                return False

            # 获取订单信息
            out_trade_no = data.get("out_trade_no")  # 商户订单号
            trade_no = data.get("trade_no")  # 支付宝交易号
            trade_status = data.get("trade_status")  # 交易状态

            print(f"支付宝回调: 订单号={out_trade_no}, 交易号={trade_no}, 状态={trade_status}")

            # 只处理交易成功或交易完成的通知
            if trade_status not in ("TRADE_SUCCESS", "TRADE_FINISHED"):
                print(f"支付宝回调: 交易状态不是成功或完成: {trade_status}")
                return True  # 返回成功，避免支付宝重复通知

            # 获取订单
            order = await order_service.get_order_by_order_number(db, out_trade_no)
            if not order:
                print(f"支付宝回调: 未找到订单: {out_trade_no}")
                return False

            # 检查订单状态
            if order.status != "pending":
                print(f"支付宝回调: 订单状态不是待支付: {order.status}")
                return True  # 订单已处理，直接返回成功

            # 创建支付记录
            payment_create = PaymentCreate(
                order_id=order.id,
                payment_id=trade_no,
                amount=order.total_amount,
                status="success",
                payment_method="alipay",
            )
            await order_service.create_payment(db, payment_create)

            # 更新订单状态
            order_update = OrderUpdate(status="paid", payment_method="alipay")
            await order_service.update_order(db, order.id, order_update)

            # 如果是软件产品，生成许可密钥
            for item in order.order_items:
                product = await product_service.get_product_by_id(db, item.product_id)
                if product and product.product_type == "software":
                    # 生成许可密钥
                    license_key = order_service.generate_license_key()
                    license_key_create = LicenseKeyCreate(
                        user_id=order.user_id,
                        product_id=product.id,
                        order_id=order.id,
                        license_key=license_key,
                    )
                    await order_service.create_license_key(db, license_key_create)

            return True
        except Exception as e:
            print(f"处理支付宝回调失败: {str(e)}")
            # 如果处理支付宝回调失败，继续使用模拟支付回调处理

    # 模拟支付回调处理
    # ... 保留原有的模拟支付回调处理代码 ...
```

### 9. 添加查询支付状态的方法

添加一个新方法用于查询支付宝订单状态：

```python
async def query_payment_status(self, db: AsyncSession, order_id: int) -> dict:
    """查询支付状态"""
    # 获取订单信息
    order = await order_service.get_order_by_id(db, order_id)
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单不存在",
        )

    # 如果支付宝客户端可用，查询支付宝订单状态
    if hasattr(self, 'alipay_client') and self.alipay_enabled:
        try:
            # 查询支付宝订单状态
            result = self.alipay_client.api_alipay_trade_query(out_trade_no=order.order_number)

            # 检查查询结果
            if result.get("code") == "10000":  # 接口调用成功
                trade_status = result.get("trade_status")

                # 如果支付成功，更新订单状态
                if trade_status in ("TRADE_SUCCESS", "TRADE_FINISHED") and order.status == "pending":
                    # 创建支付记录
                    payment_create = PaymentCreate(
                        order_id=order.id,
                        payment_id=result.get("trade_no"),
                        amount=order.total_amount,
                        status="success",
                        payment_method="alipay",
                    )
                    await order_service.create_payment(db, payment_create)

                    # 更新订单状态
                    order_update = OrderUpdate(status="paid", payment_method="alipay")
                    await order_service.update_order(db, order.id, order_update)

                    # 如果是软件产品，生成许可密钥
                    for item in order.order_items:
                        product = await product_service.get_product_by_id(db, item.product_id)
                        if product and product.product_type == "software":
                            # 生成许可密钥
                            license_key = order_service.generate_license_key()
                            license_key_create = LicenseKeyCreate(
                                user_id=order.user_id,
                                product_id=product.id,
                                order_id=order.id,
                                license_key=license_key,
                            )
                            await order_service.create_license_key(db, license_key_create)

                # 返回支付状态
                return {
                    "order_id": order.id,
                    "order_number": order.order_number,
                    "status": "paid" if trade_status in ("TRADE_SUCCESS", "TRADE_FINISHED") else "pending",
                    "amount": order.total_amount,
                    "payment_method": "alipay",
                    "payment_time": result.get("send_pay_date") or order.updated_at,
                }
            else:
                # 接口调用失败，返回订单当前状态
                return {
                    "order_id": order.id,
                    "order_number": order.order_number,
                    "status": order.status,
                    "amount": order.total_amount,
                    "payment_method": order.payment_method,
                    "payment_time": order.updated_at,
                }
        except Exception as e:
            print(f"查询支付宝订单状态失败: {str(e)}")
            # 如果查询支付宝订单状态失败，返回订单当前状态

    # 返回订单当前状态
    return {
        "order_id": order.id,
        "order_number": order.order_number,
        "status": order.status,
        "amount": order.total_amount,
        "payment_method": order.payment_method,
        "payment_time": order.updated_at,
    }
```

### 10. 修改API端点

修改`backend/app/api/order.py`中的`check_payment_status`端点，使用新的查询方法：

```python
@router.get("/orders/{order_id}/status")
async def check_payment_status(
    order_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """查询支付状态"""
    try:
        # 获取订单
        order = await order_service.get_order_by_id(db, order_id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在",
            )

        # 检查订单所有权
        if order.user_id != current_user.id and current_user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此订单",
            )

        # 查询支付状态
        payment_status = await payment_service.query_payment_status(db, order_id)

        return {"success": True, "status": payment_status}
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询支付状态失败: {str(e)}",
        )
```

## 三、测试Alipay沙箱支付

### 1. 重启后端服务

修改完代码后，重启后端服务：

```bash
docker-compose restart backend
```

### 2. 使用沙箱买家账号测试

1. 在支付宝开放平台的沙箱环境中，可以找到测试买家账号
2. 创建一个新订单并点击支付
3. 系统会跳转到支付宝沙箱支付页面
4. 使用测试买家账号登录并完成支付
5. 支付完成后，系统会跳转回您的网站，显示支付结果

### 3. 测试支付回调

由于本地开发环境无法接收支付宝的异步通知，您可以：

1. 使用内网穿透工具（如ngrok）将本地服务暴露到公网
2. 修改`ALIPAY_NOTIFY_URL`为公网地址
3. 或者依赖前端的支付状态查询功能来更新订单状态

## 四、保留模拟支付功能

即使集成了Alipay沙箱，也建议保留模拟支付功能，用于以下场景：

1. 当Alipay配置不可用时自动回退到模拟支付
2. 为开发和测试人员提供便捷的测试方式
3. 在演示环境中使用，避免依赖外部服务

## 五、前端适配

前端代码已经做好了准备，无需大量修改。当后端返回真实的支付宝支付链接时，前端会自动跳转到支付宝页面。支付完成后，支付宝会将用户重定向回您的网站。

## 六、注意事项

1. **安全性**：确保私钥文件安全，不要将其提交到代码仓库
2. **沙箱与生产环境**：沙箱和生产环境使用不同的网关地址，上线前需要修改
3. **异步通知**：生产环境中必须正确处理异步通知，这是支付状态的可靠来源
4. **订单超时**：考虑实现订单超时取消机制
5. **退款流程**：如需支持退款，还需实现退款相关接口

## 七、完整的支付流程

集成Alipay后，完整的支付流程如下：

1. 用户创建订单
2. 用户点击"支付"按钮
3. 系统生成支付宝支付链接，将用户重定向到支付宝
4. 用户在支付宝完成支付
5. 支付宝将用户重定向回您的网站（同步通知）
6. 支付宝向您的服务器发送异步通知
7. 您的系统处理异步通知，更新订单状态
8. 如果是软件产品，系统生成许可密钥
9. 用户可以查看订单状态和许可密钥

## 八、生产环境部署注意事项

在将支付功能部署到生产环境时，需要注意以下几点：

1. **申请正式的支付宝应用**：需要完成企业认证，并通过支付宝的审核
2. **更换网关地址**：从沙箱环境的`https://openapi-sandbox.dl.alipaydev.com/gateway.do`更改为生产环境的`https://openapi.alipay.com/gateway.do`
3. **配置真实的回调地址**：确保`ALIPAY_NOTIFY_URL`和`ALIPAY_RETURN_URL`使用真实的域名
4. **安全存储密钥**：在生产环境中，应使用更安全的方式存储密钥，如密钥管理服务
5. **实现完整的日志记录**：记录所有支付相关的操作，便于排查问题
6. **添加监控和告警**：对支付流程进行监控，及时发现和处理异常

通过以上步骤，您可以成功集成Alipay沙箱环境，实现真实的支付流程测试，同时保留模拟支付功能作为备选方案。