# license_keys 表缺少 order_id 列问题

## 问题描述

在获取软件许可密钥列表时，系统报错：

```
获取软件许可密钥列表失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column license_keys.order_id does not exist HINT: Perhaps you meant to reference the column "license_keys.user_id". [SQL: SELECT license_keys.id, license_keys.user_id, license_keys.product_id, license_keys.order_id, license_keys.license_key, license_keys.is_active, license_keys.created_at, license_keys.updated_at, products_1.id AS id_1, products_1.name, products_1.description, products_1.price, products_1.stock, products_1.product_type, products_1.is_active AS is_active_1, products_1.created_at AS created_at_1, products_1.updated_at AS updated_at_1 FROM license_keys LEFT OUTER JOIN products AS products_1 ON products_1.id = license_keys.product_id WHERE license_keys.user_id = $1::INTEGER ORDER BY license_keys.created_at DESC] [parameters: (3,)] (Background on this error at: https://sqlalche.me/e/20/f405)
```

这是因为在 SQLAlchemy 模型中定义了 `order_id` 列，但在数据库表中不存在这个列。

## 原因分析

1. 在 `models/order.py` 中，`LicenseKey` 模型定义了 `order_id` 字段：
   ```python
   class LicenseKey(Base):
       __tablename__ = "license_keys"
       # ...
       order_id = Column(Integer, ForeignKey("orders.id"), nullable=False)
   ```

2. 但在 `database/init/01_init.sql` 中，创建 `license_keys` 表的 SQL 语句没有包含 `order_id` 列：
   ```sql
   CREATE TABLE IF NOT EXISTS license_keys (
       id SERIAL PRIMARY KEY,
       user_id INTEGER NOT NULL REFERENCES users(id),
       product_id INTEGER NOT NULL REFERENCES products(id),
       license_key VARCHAR(100) UNIQUE NOT NULL,
       is_active BOOLEAN DEFAULT TRUE,
       created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
   );
   ```

3. 这导致了模型定义与数据库表结构不一致，当尝试查询 `order_id` 列时出错。

## 解决方案

### 1. 修改数据库初始化脚本

修改 `database/init/01_init.sql` 文件，在 `license_keys` 表中添加 `order_id` 列：

```sql
CREATE TABLE IF NOT EXISTS license_keys (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    product_id INTEGER NOT NULL REFERENCES products(id),
    order_id INTEGER NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    license_key VARCHAR(100) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 2. 修改模型定义

修改 `models/order.py` 文件中的 `LicenseKey` 模型，添加 `order_id` 字段：

```python
class LicenseKey(Base):
    __tablename__ = "license_keys"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    order_id = Column(Integer, ForeignKey("orders.id", ondelete="CASCADE"), nullable=False)
    license_key = Column(String(100), unique=True, nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    user = relationship("User")
    product = relationship("Product")
    order = relationship("Order")
```

### 3. 修改 Pydantic 模型

修改 `schemas/order.py` 文件中的 `LicenseKeyBase` 模型，添加 `order_id` 字段：

```python
class LicenseKeyBase(BaseModel):
    user_id: int = Field(..., description="用户ID")
    product_id: int = Field(..., description="产品ID")
    order_id: int = Field(..., description="订单ID")
    license_key: str = Field(..., description="许可密钥")
```

### 4. 修改服务方法

修改 `services/payment.py` 文件中的 `create_license_key` 和 `get_user_license_keys` 方法，使其使用 ORM 而不是原始 SQL：

```python
async def create_license_key(self, db: AsyncSession, license_key_create: LicenseKeyCreate) -> LicenseKey:
    db_license_key = LicenseKey(**license_key_create.dict())
    db.add(db_license_key)
    await db.commit()
    await db.refresh(db_license_key)
    return db_license_key

async def get_user_license_keys(self, db: AsyncSession, user_id: int) -> List[LicenseKey]:
    result = await db.execute(
        select(LicenseKey)
        .options(joinedload(LicenseKey.product))
        .where(LicenseKey.user_id == user_id)
        .order_by(LicenseKey.created_at.desc())
    )
    return list(result.scalars().all())
```

### 5. 创建数据库迁移脚本

创建 `backend/app/scripts/add_order_id_to_license_keys.py` 脚本，为现有的 `license_keys` 表添加 `order_id` 列：

```python
# 脚本内容见 backend/app/scripts/add_order_id_to_license_keys.py
```

### 6. 执行迁移脚本

```bash
# 在Docker环境中
docker-compose exec backend python app/scripts/add_order_id_to_license_keys.py

# 在本地环境中
cd backend
python -m app.scripts.add_order_id_to_license_keys
```

## 预防措施

为了避免类似问题再次发生，建议采取以下措施：

1. 使用自动化的数据库迁移工具（如Alembic）来管理数据库结构变更。
2. 在开发过程中，定期检查模型定义与数据库表结构的一致性。
3. 在部署前，进行全面的测试，确保所有功能正常工作。
4. 建立数据库结构变更的审核流程，确保所有变更都经过充分的测试和验证。
