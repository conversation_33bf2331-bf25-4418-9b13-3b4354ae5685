# 系统架构设计文档

## 1. 架构概述

本文档描述了一个基于FastAPI和Vue3的软件和设备销售平台的系统架构设计。该系统旨在提供公司信息展示、用户注册登录、软件购买、密钥管理和管理员后台等功能。架构设计严格遵循项目约束，聚焦于MVP核心功能，确保在15天内完成开发并上线。

### 1.1 设计原则

- **简单性**：保持架构简洁，避免不必要的复杂性
- **最小可行产品**：专注于核心功能，确保快速交付
- **可测试性**：支持London School TDD测试方法，确保代码质量
- **资源效率**：在有限硬件资源下高效运行
- **异步优先**：充分利用FastAPI的异步特性提高性能
- **可维护性**：清晰的代码组织和模块化设计

### 1.2 架构约束摘要

- 系统必须部署在阿里云2核心2GB内存40GB存储的云服务器上
- 后端使用Python 3.12和FastAPI框架，保持异步特性
- 前端使用Vue 3、Pinia状态管理和Element Plus组件库
- 数据库使用PostgreSQL 16（生产）和SQLite（开发/测试）
- 支付功能必须集成支付宝作为唯一的第三方支付服务
- 项目必须在15天内完成，总预算不超过20,000人民币

## 2. 系统架构

### 2.1 简化三层架构

为了满足15天开发周期的约束，采用简化的三层架构：

1. **表示层**：Vue3前端应用
2. **业务逻辑层**：FastAPI后端服务
3. **数据访问层**：PostgreSQL数据库

### 2.2 容器化部署

系统使用Docker容器化部署，包含三个主要容器：

1. **前端容器**：Nginx + Vue3静态文件
2. **后端容器**：FastAPI应用
3. **数据库容器**：PostgreSQL 16

## 3. 后端架构

### 3.1 目录结构

```
backend/
├── app/
│   ├── main.py                 # 应用入口点
│   ├── core/                   # 核心模块
│   │   ├── config.py           # 配置管理
│   │   ├── database.py         # 数据库连接
│   │   ├── security.py         # 认证和安全
│   │   ├── dependencies.py     # 依赖注入
│   │   └── schemas.py          # 共享Pydantic模型
│   ├── api/                    # API路由
│   │   ├── user.py             # 用户相关API
│   │   ├── product.py          # 产品相关API
│   │   ├── payment.py          # 支付相关API
│   │   └── admin.py            # 管理员相关API
│   ├── models/                 # 数据模型
│   │   ├── user.py             # 用户模型
│   │   ├── product.py          # 产品模型
│   │   ├── order.py            # 订单模型
│   │   └── site.py             # 站点信息模型
│   ├── schemas/                # Pydantic模型
│   │   ├── user.py             # 用户相关模型
│   │   ├── product.py          # 产品相关模型
│   │   ├── order.py            # 订单相关模型
│   │   └── site.py             # 站点信息相关模型
│   └── services/               # 业务服务
│       ├── user.py             # 用户服务
│       ├── product.py          # 产品服务
│       ├── payment.py          # 支付服务
│       └── admin.py            # 管理员服务
└── tests/                      # 测试目录
    ├── conftest.py             # 测试配置和夹具
    ├── unit/                   # 单元测试
    └── integration/            # 集成测试
```

### 3.2 核心组件

1. **FastAPI应用**：提供RESTful API接口
2. **SQLAlchemy ORM**：异步数据库操作
3. **Pydantic**：数据验证和序列化
4. **JWT认证**：用户身份验证
5. **支付宝SDK**：支付集成

### 3.3 API设计原则

1. **RESTful设计**：遵循REST原则设计API
2. **统一响应格式**：所有API返回统一的响应格式
3. **版本控制**：API路径包含版本信息
4. **OpenAPI文档**：自动生成API文档

## 4. 前端架构

### 4.1 目录结构

```
frontend/
├── src/
│   ├── api/                    # API请求
│   ├── assets/                 # 静态资源
│   ├── components/             # 通用组件
│   ├── views/                  # 页面组件
│   ├── store/                  # Pinia状态管理
│   ├── router/                 # Vue Router
│   ├── utils/                  # 工具函数
│   ├── App.vue                 # 根组件
│   └── main.js                 # 入口文件
└── tests/                      # 测试目录
    └── unit/                   # 单元测试
```

### 4.2 核心组件

1. **Vue3**：前端框架
2. **Element Plus**：UI组件库
3. **Pinia**：状态管理
4. **Vue Router**：路由管理
5. **Axios**：HTTP客户端

## 5. 数据模型

### 5.1 核心实体

1. **User**：用户信息
2. **Product**：产品信息
3. **Order**：订单信息
4. **SiteInfo**：站点信息

### 5.2 简化数据模型

```
User(id, username, email, password_hash, is_active, role, address)
Product(id, name, description, price, type, is_active)
Order(id, order_number, user_id, product_id, status, created_at, license_key)
SiteInfo(id, company_name, description, contact_info)
```

## 6. 关键流程

### 6.1 用户注册流程

1. 用户填写注册表单
2. 前端验证表单数据
3. 发送注册请求到后端
4. 后端验证数据并创建用户
5. 返回注册结果

### 6.2 软件购买流程

1. 用户浏览产品
2. 用户选择产品并点击购买
3. 系统生成订单
4. 用户通过支付宝支付
5. 支付成功后显示软件密钥和下载链接

## 7. 安全设计

### 7.1 认证与授权

1. **JWT令牌认证**：用户登录后获取JWT令牌
2. **简化的角色控制**：区分普通用户和管理员
3. **密码哈希存储**：使用bcrypt算法哈希存储密码

### 7.2 数据安全

1. **HTTPS加密传输**：所有API请求使用HTTPS
2. **输入验证**：使用Pydantic验证所有输入数据
3. **参数化SQL查询**：防止SQL注入攻击

## 8. 测试策略

### 8.1 后端测试

1. **单元测试**：使用pytest测试各个组件，必要组件，其他不考虑
2. **集成测试**：测试组件间的交互，必要测试才进行，其他不考虑
3. **API测试**：使用TestClient测试API端点，必要测试才进行，其他不考虑

### 8.2 前端测试

1. **单元测试**：使用Vitest测试Vue组件，必要测试才进行，其他不考虑
2. **端到端测试**：使用Cypress测试用户流程，必要测试才进行，其他不考虑

## 9. 部署架构

### 9.1 开发环境

1. **Docker-Desktop**：本地开发环境
2. **SQLite数据库**：简化开发和测试
3. **支付宝沙箱环境**：测试支付功能
4. **windows11**：开发操作系统
5. **powershell**：开发终端

### 9.2 生产环境

1. **阿里云服务器**：2核心2GB内存40GB存储
2. **Docker Compose**：容器编排
3. **Nginx**：反向代理和静态文件服务
4. **PostgreSQL**：生产数据库

## 10. 开发计划

1. **环境搭建**（1天）：配置Docker环境和基础框架（已完成）
2. **用户认证**（2天）：实现注册、登录和权限控制
3. **产品管理**（2天）：实现产品展示和管理
4. **订单和支付**（3天）：实现订单流程和支付宝集成
5. **管理员功能**（2天）：实现管理员后台
6. **前端开发**（3天）：实现用户界面
7. **测试和部署**（2天）：测试和上线准备

## 11. 结论

本架构设计严格遵循项目约束，专注于MVP核心功能，确保在15天内完成开发并上线。架构采用现代化的技术栈和最佳实践，同时保持简单性和可维护性，适合小型团队快速开发和部署。