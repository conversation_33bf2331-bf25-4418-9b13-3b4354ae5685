# 01_环境搭建开发计划（1天）

## 1. 项目概述

本计划详细描述了软件和设备销售平台的环境搭建阶段，包括Docker环境配置和基础框架搭建。该计划基于`docs\architecture.md`中的架构设计，确保在15天开发周期内的第一天完成所有必要的环境准备工作。

## 2. 环境搭建任务清单

| 任务ID | 任务描述 | 预计时间 | 状态 | 负责人 |
|--------|----------|----------|------|--------|
| 1.1 | **Docker环境配置** | 3小时 | 已完成 | - |
| 1.1.1 | 完善docker-compose.yml配置 | 1小时 | 已完成 | - |
| 1.1.2 | 创建/优化后端Dockerfile | 30分钟 | 已完成 | - |
| 1.1.3 | 创建/优化前端Dockerfile | 30分钟 | 已完成 | - |
| 1.1.4 | 配置PostgreSQL数据库服务 | 30分钟 | 已完成 | - |
| 1.1.5 | 测试Docker环境完整启动 | 30分钟 | 已完成 | - |
| 1.2 | **后端基础框架搭建** | 3小时 | 已完成 | - |
| 1.2.1 | 创建FastAPI应用基础结构 | 30分钟 | 已完成 | - |
| 1.2.2 | 配置数据库连接（PostgreSQL/SQLite） | 30分钟 | 已完成 | - |
| 1.2.3 | 实现核心配置模块 | 30分钟 | 已完成 | - |
| 1.2.4 | 设置JWT认证基础结构 | 30分钟 | 已完成 | - |
| 1.2.5 | 创建API路由基础结构 | 30分钟 | 已完成 | - |
| 1.2.6 | 配置测试环境（pytest） | 30分钟 | 已完成 | - |
| 1.3 | **前端基础框架搭建** | 2小时 | 已完成 | - |
| 1.3.1 | 初始化Vue3项目结构 | 30分钟 | 已完成 | - |
| 1.3.2 | 集成Element Plus组件库 | 30分钟 | 已完成 | - |
| 1.3.3 | 配置Pinia状态管理 | 30分钟 | 已完成 | - |
| 1.3.4 | 设置Vue Router | 30分钟 | 已完成 | - |
| 1.4 | **项目文档更新** | 1小时 | 已完成 | - |
| 1.4.1 | 更新README.md文件 | 30分钟 | 已完成 | - |
| 1.4.2 | 创建开发指南文档 | 30分钟 | 已完成 | - |

## 3. 详细任务说明

### 3.1 Docker环境配置

#### 3.1.1 完善docker-compose.yml配置
- 检查现有的docker-compose.yml文件
- 确保包含后端、前端和数据库三个主要服务
- 配置适当的端口映射（后端:8000, 前端:5173）
- 设置服务间的依赖关系
- 配置卷挂载以支持开发时的热重载

#### 3.1.2 创建/优化后端Dockerfile
- 基于Python 3.12官方镜像
- 配置工作目录和依赖安装
- 设置适当的环境变量
- 配置启动命令（uvicorn）

#### 3.1.3 创建/优化前端Dockerfile
- 基于Node.js官方镜像
- 配置工作目录和依赖安装
- 设置开发服务器启动命令

#### 3.1.4 配置PostgreSQL数据库服务
- 使用PostgreSQL 16官方镜像
- 设置数据库名称、用户和密码
- 配置数据持久化卷
- 设置适当的环境变量

#### 3.1.5 测试Docker环境完整启动
- 执行`docker-compose up`命令
- 验证所有服务是否正常启动
- 检查服务间的连接是否正常
- 验证开发环境的热重载功能

### 3.2 后端基础框架搭建

#### 3.2.1 创建FastAPI应用基础结构
- 创建main.py作为应用入口
- 设置CORS中间件
- 配置API前缀和版本
- 创建健康检查端点

#### 3.2.2 配置数据库连接
- 实现数据库连接模块
- 配置SQLAlchemy异步会话
- 设置开发环境使用SQLite
- 设置生产环境使用PostgreSQL

#### 3.2.3 实现核心配置模块
- 创建Settings类管理应用配置
- 支持从环境变量加载配置
- 区分开发和生产环境配置
- 配置支付宝相关参数

#### 3.2.4 设置JWT认证基础结构
- 实现JWT令牌创建和验证
- 创建用户认证依赖
- 实现密码哈希和验证功能
- 设置令牌过期时间

#### 3.2.5 创建API路由基础结构
- 创建路由模块目录结构
- 设置用户、产品、支付和管理员路由
- 实现统一的响应模型
- 配置OpenAPI文档

#### 3.2.6 配置测试环境
- 设置pytest配置文件
- 创建测试夹具
- 配置测试数据库（SQLite内存数据库）
- 设置测试客户端

### 3.3 前端基础框架搭建

#### 3.3.1 初始化Vue3项目结构
- 创建/优化项目目录结构
- 配置Vite构建工具
- 设置ESLint和Prettier
- 配置TypeScript支持

#### 3.3.2 集成Element Plus组件库
- 安装Element Plus依赖
- 配置按需导入
- 设置主题和样式
- 创建基础布局组件

#### 3.3.3 配置Pinia状态管理
- 创建store目录结构
- 设置用户状态管理
- 实现持久化存储
- 配置状态初始化

#### 3.3.4 设置Vue Router
- 创建路由配置
- 设置路由守卫
- 配置懒加载
- 实现基础导航组件

### 3.4 项目文档更新

#### 3.4.1 更新README.md文件
- 添加项目概述
- 更新环境要求
- 添加启动和测试说明
- 更新目录结构说明

#### 3.4.2 创建开发指南文档
- 记录开发流程和规范
- 添加API调用示例
- 记录常见问题和解决方案
- 添加测试指南

## 4. 完成标准

环境搭建阶段将在满足以下条件时视为完成：

1. Docker环境可以完整启动所有服务
2. 后端FastAPI应用可以正常运行并响应API请求
3. 前端Vue3应用可以正常运行并显示基础UI
4. 数据库服务可以正常连接并执行基本操作
5. 测试环境配置完成并可以运行基本测试
6. 项目文档更新完成并包含必要的说明

## 5. 进度跟踪

| 日期 | 计划进度 | 实际进度 | 备注 |
|------|----------|----------|------|
| 第1天上午 | 完成Docker环境配置 | 已完成 | 成功配置docker-compose.yml和Dockerfile |
| 第1天下午 | 完成后端和前端基础框架搭建 | 已完成 | 成功搭建FastAPI和Vue3基础框架 |
| 第1天晚上 | 完成项目文档更新和整体测试 | 已完成 | 更新了README和开发指南，测试环境配置完成 |

## 6. 风险与应对措施

| 风险 | 可能性 | 影响 | 应对措施 |
|------|--------|------|----------|
| Docker配置问题 | 中 | 高 | 准备备选配置方案，参考官方文档 |
| 依赖冲突 | 中 | 中 | 使用虚拟环境和版本锁定 |
| 数据库连接问题 | 低 | 高 | 准备SQLite作为备选方案 |
| 前端框架兼容性问题 | 低 | 中 | 严格遵循版本要求，查阅官方示例 |

## 7. 资源需求

- Docker Desktop环境
- Python 3.12
- Node.js环境
- PostgreSQL 16
- 开发IDE（如VS Code）
- Git版本控制

## 8. 参考资料

- FastAPI官方文档: https://fastapi.tiangolo.com/
- Vue3官方文档: https://v3.vuejs.org/
- Docker Compose文档: https://docs.docker.com/compose/
- PostgreSQL文档: https://www.postgresql.org/docs/16/index.html
- Element Plus文档: https://element-plus.org/
