# 支付宝沙箱环境说明

## 沙箱账号信息

### 商家信息
- 商户账号: <EMAIL>
- 登录密码: 111111
- 商户PID: 2088721067958003
- 账户余额: 1000000.00

### 买家信息
- 买家账号: <EMAIL>
- 登录密码: 111111
- 支付密码: 111111
- 用户UID: 2088722067958011
- 用户名称: kcyghi1984
- 证件类型: IDENTITY_CARD
- 证件账号: 984394199209142391
- 账户余额: 1000000.00

## 沙箱环境概述

支付宝沙箱环境是一个与生产环境完全隔离的测试环境，具有以下特点：

1. **完全隔离**：在沙箱环境中的操作不会影响生产环境的数据
2. **低门槛**：无需等待产品签约，可以直接调用接口进行测试
3. **独立数据体系**：沙箱环境的数据（如用户ID等）与生产环境完全独立

## 支持的接口

沙箱环境支持以下电脑网站支付相关接口：

| 接口类型 | 接口中文名 | 接口英文名称 | 沙箱环境支持情况 |
|---------|-----------|------------|---------------|
| 电脑网站支付 | 统一收单下单并支付页面接口 | alipay.trade.page.pay | 支持 |
| 辅助接口 | 统一收单交易查询 | alipay.trade.query | 支持 |
| 辅助接口 | 统一收单交易退款接口 | alipay.trade.refund | 支持 |
| 辅助接口 | 统一收单交易退款查询接口 | alipay.trade.fastpay.refund.query | 支持 |
| 辅助接口 | 统一收单交易关闭接口 | alipay.trade.close | 支持 |
| 辅助接口 | 查询对账单下载地址 | alipay.data.dataservice.bill.downloadurl.query | 支持（但只做模拟调用，下载账单为模板，账单内没有实际数据） |

## 注意事项

1. 沙箱环境并非100%与生产环境一致，接口的实际响应逻辑应以生产环境为准
2. 沙箱环境开发调试完成后，仍然需要在生产环境进行测试验收
3. 不可将沙箱环境返回的数据与生产环境中的数据混淆
4. 沙箱环境不支持银行卡支付，无法模拟测试收单退款冲退完成通知接口
5. 所有交易都是虚拟的，不涉及真实资金流动

## 使用步骤

1. **下载沙箱版支付宝APP**：
   - 在支付宝开放平台的沙箱页面可以找到下载链接
   - 沙箱版APP与正式版是分开的，专门用于测试

2. **配置开发环境**：
   - 使用沙箱网关：`https://openapi-sandbox.dl.alipaydev.com/gateway.do`
   - 使用沙箱环境的APPID和密钥
   - 配置回调地址，可以是本地开发环境的地址

3. **测试支付流程**：
   - 在应用中发起支付请求
   - 使用沙箱版APP扫码或确认支付
   - 使用买家账号的登录密码和支付密码完成支付
   - 系统会按照正常流程发送回调通知
