/* 导入所有样式文件 */
@import './variables.css';
@import './global.css';
@import './element-plus-override.css';

/* 响应式布局辅助类 */
.d-none {
  display: none !important;
}

.d-block {
  display: block !important;
}

.d-flex {
  display: flex !important;
}

.d-inline-flex {
  display: inline-flex !important;
}

/* 小屏幕设备 (手机, 576px 以下) */
@media (max-width: 575.98px) {
  .d-none-sm {
    display: none !important;
  }
  
  .d-block-sm {
    display: block !important;
  }
  
  .d-flex-sm {
    display: flex !important;
  }
}

/* 中等屏幕设备 (平板, 768px 以下) */
@media (max-width: 767.98px) {
  .d-none-md {
    display: none !important;
  }
  
  .d-block-md {
    display: block !important;
  }
  
  .d-flex-md {
    display: flex !important;
  }
}

/* 大屏幕设备 (桌面, 992px 以下) */
@media (max-width: 991.98px) {
  .d-none-lg {
    display: none !important;
  }
  
  .d-block-lg {
    display: block !important;
  }
  
  .d-flex-lg {
    display: flex !important;
  }
}

/* 超大屏幕设备 (大桌面, 1200px 以下) */
@media (max-width: 1199.98px) {
  .d-none-xl {
    display: none !important;
  }
  
  .d-block-xl {
    display: block !important;
  }
  
  .d-flex-xl {
    display: flex !important;
  }
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-normal) ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform var(--transition-normal) ease, opacity var(--transition-normal) ease;
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(20px);
  opacity: 0;
}

.slide-down-enter-active,
.slide-down-leave-active {
  transition: transform var(--transition-normal) ease, opacity var(--transition-normal) ease;
}

.slide-down-enter-from,
.slide-down-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}

/* 页面过渡效果 */
.page-enter-active,
.page-leave-active {
  transition: opacity var(--transition-normal) ease, transform var(--transition-normal) ease;
}

.page-enter-from,
.page-leave-to {
  opacity: 0;
  transform: translateY(30px);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--neutral-400);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-500);
}

/* 焦点轮廓 */
:focus {
  outline: 2px solid var(--primary-light);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

:focus-visible {
  outline: 2px solid var(--primary-light);
  outline-offset: 2px;
}

/* 可访问性辅助类 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.not-sr-only {
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}
