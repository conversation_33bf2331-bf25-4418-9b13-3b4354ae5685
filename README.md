# 软件和设备销售平台

一个基于FastAPI和Vue3的软件和设备销售平台，支持在线支付和软件密钥管理。

## 项目概述

本项目是一个完整的软件和设备销售平台，包括前端和后端实现。主要功能包括：

- 用户注册和登录
- 产品浏览和搜索
- 订单管理
- 在线支付（支付宝）
- 软件密钥管理
- 管理员后台

## 技术栈

### 后端

- FastAPI：高性能的Python Web框架
- SQLAlchemy：ORM框架
- PostgreSQL：数据库
- JWT：身份验证
- Alipay SDK：支付宝支付集成

### 前端

- Vue 3：渐进式JavaScript框架
- Element Plus：UI组件库
- Pinia：状态管理
- Vue Router：路由管理
- Axios：HTTP客户端

### 开发环境

- Docker：容器化部署
- Docker Compose：多容器应用编排

## 目录结构

```
.
├── backend/                # 后端代码
│   ├── app/                # 应用代码
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心模块
│   │   ├── models/         # 数据模型
│   │   ├── schemas/        # Pydantic模型
│   │   ├── services/       # 业务逻辑
│   │   └── main.py         # 应用入口
│   ├── tests/              # 测试代码
│   ├── Dockerfile          # 后端Docker配置
│   └── requirements.txt    # Python依赖
├── frontend/               # 前端代码
│   ├── src/                # 源代码
│   │   ├── api/            # API请求
│   │   ├── assets/         # 静态资源
│   │   ├── components/     # 组件
│   │   ├── router/         # 路由
│   │   ├── store/          # 状态管理
│   │   ├── views/          # 页面
│   │   ├── App.vue         # 根组件
│   │   └── main.js         # 入口文件
│   ├── Dockerfile          # 前端Docker配置
│   └── package.json        # NPM配置
├── database/               # 数据库相关
│   └── init/               # 初始化脚本
├── docs/                   # 文档
│   ├── architecture.md     # 架构文档
│   └── plans/              # 开发计划
├── docker-compose.yml      # Docker Compose配置
└── README.md               # 项目说明
```

## 环境要求

- Docker Desktop
- Git

## 快速开始

1. 克隆仓库

```bash
git clone https://github.com/yourusername/sales-platform.git
cd sales-platform
```

2. 启动Docker容器

```bash
docker-compose up -d
```

3. 访问应用

- 前端：http://localhost:5173
- 后端API：http://localhost:8000/api/v1/docs

## 开发指南

### 后端开发

1. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

2. 运行开发服务器

```bash
uvicorn app.main:app --reload
```

### 前端开发

1. 安装依赖

```bash
cd frontend
npm install
```

2. 运行开发服务器

```bash
npm run dev
```

## 测试

### 后端测试

```bash
cd backend
pytest
```

### 前端测试

```bash
cd frontend
npm run test:unit
```

## 部署

项目使用Docker Compose进行部署，详细部署指南请参考[部署文档](docs/deployment.md)。

## 贡献指南

欢迎贡献代码！请参考[贡献指南](CONTRIBUTING.md)。

## 许可证

本项目采用MIT许可证，详情请参阅[LICENSE](LICENSE)文件。
