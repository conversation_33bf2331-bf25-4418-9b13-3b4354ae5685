#!/bin/bash
# 数据库备份脚本

# 设置变量
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="./backups"
BACKUP_FILE="${BACKUP_DIR}/db_backup_${TIMESTAMP}.sql"
CONTAINER_NAME="sales_platform_database"

# 创建备份目录（如果不存在）
mkdir -p ${BACKUP_DIR}

# 执行备份
echo "开始备份数据库..."
docker exec ${CONTAINER_NAME} pg_dump -U user -d sales_platform > ${BACKUP_FILE}

# 检查备份是否成功
if [ $? -eq 0 ]; then
    echo "数据库备份成功: ${BACKUP_FILE}"
    # 可选：压缩备份文件
    gzip ${BACKUP_FILE}
    echo "备份文件已压缩: ${BACKUP_FILE}.gz"
else
    echo "数据库备份失败"
    exit 1
fi

# 可选：保留最近的N个备份，删除旧备份
MAX_BACKUPS=10
BACKUP_COUNT=$(ls -1 ${BACKUP_DIR}/db_backup_*.sql* | wc -l)

if [ ${BACKUP_COUNT} -gt ${MAX_BACKUPS} ]; then
    echo "删除旧备份文件，保留最新的${MAX_BACKUPS}个备份..."
    ls -1t ${BACKUP_DIR}/db_backup_*.sql* | tail -n +$((${MAX_BACKUPS}+1)) | xargs rm -f
fi

echo "备份过程完成"
