<template>
  <PageContainer customClass="auth-page">
    <div class="auth-container">
      <div class="auth-card">
        <div class="auth-header">
          <h1 class="auth-title">创建账户</h1>
          <p class="auth-subtitle">注册以获取更多服务</p>
        </div>
        
        <el-form
          ref="registerFormRef"
          :model="registerForm"
          :rules="registerRules"
          @submit.prevent="handleRegister"
          class="auth-form"
        >
          <el-form-item prop="username">
            <el-input
              v-model="registerForm.username"
              placeholder="用户名"
              :prefix-icon="User"
              size="large"
            />
          </el-form-item>
          
          <el-form-item prop="email">
            <el-input
              v-model="registerForm.email"
              placeholder="电子邮箱"
              :prefix-icon="Message"
              size="large"
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="registerForm.password"
              type="password"
              placeholder="密码"
              :prefix-icon="Lock"
              show-password
              size="large"
            />
          </el-form-item>
          
          <el-form-item prop="confirmPassword">
            <el-input
              v-model="registerForm.confirmPassword"
              type="password"
              placeholder="确认密码"
              :prefix-icon="Lock"
              show-password
              size="large"
            />
          </el-form-item>
          
          <el-form-item prop="address">
            <el-input
              v-model="registerForm.address"
              placeholder="地址（选填）"
              :prefix-icon="Location"
              size="large"
            />
          </el-form-item>
          
          <el-form-item class="auth-actions">
            <AppButton
              type="primary"
              :loading="loading"
              class="auth-button"
              @click="handleRegister"
              size="large"
              round
            >
              注册
            </AppButton>
          </el-form-item>
        </el-form>
        
        <div class="auth-footer">
          <p>已有账号？ <router-link to="/login" class="auth-link">立即登录</router-link></p>
        </div>
      </div>
      
      <div class="auth-image">
        <img src="https://images.unsplash.com/photo-1581092335878-2d9ff86ca2bf?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80" alt="Register" />
        <div class="image-overlay">
          <div class="overlay-content">
            <h2>加入我们</h2>
            <p>注册账户，开始您的购物之旅</p>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '../store'
import PageContainer from '../components/layout/PageContainer.vue'
import { User, Lock, Message, Location } from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()
const registerFormRef = ref(null)
const loading = ref(false)

// 注册表单数据
const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  address: ''
})

// 密码验证函数
const validatePass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else {
    if (registerForm.confirmPassword !== '') {
      registerFormRef.value.validateField('confirmPassword')
    }
    callback()
  }
}

// 确认密码验证函数
const validateConfirmPass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== registerForm.password) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

// 表单验证规则
const registerRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度应在3-50个字符之间', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入电子邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入有效的电子邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, validator: validatePass, trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, validator: validateConfirmPass, trigger: 'blur' }
  ],
  address: [
    { max: 200, message: '地址长度不能超过200个字符', trigger: 'blur' }
  ]
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return

  await registerFormRef.value.validate(async (valid) => {
    if (!valid) return

    loading.value = true
    try {
      // 准备注册数据
      const userData = {
        username: registerForm.username,
        email: registerForm.email,
        password: registerForm.password,
        address: registerForm.address || undefined
      }

      const result = await userStore.register(userData)

      if (result.success) {
        ElMessage({
          message: '注册成功，请登录',
          type: 'success',
          duration: 2000
        })
        router.push('/login')
      } else {
        ElMessage.error(result.message || '注册失败')
      }
    } catch (error) {
      ElMessage.error('注册过程中发生错误')
      console.error('注册错误:', error)
    } finally {
      loading.value = false
    }
  })
}
</script>

<style scoped>
.auth-page {
  max-width: 100% !important;
  padding: 0 !important;
}

.auth-container {
  display: flex;
  min-height: calc(100vh - 80px - 300px);
  background-color: var(--bg-primary);
}

.auth-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: var(--spacing-10);
  max-width: 500px;
  margin: 0 auto;
}

.auth-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.auth-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-2);
}

.auth-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
}

.auth-form {
  width: 100%;
}

.auth-actions {
  margin-top: var(--spacing-6);
}

.auth-button {
  width: 100%;
}

.auth-footer {
  text-align: center;
  margin-top: var(--spacing-6);
  color: var(--text-secondary);
}

.auth-link {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: color var(--transition-fast) ease;
}

.auth-link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.auth-image {
  flex: 1;
  position: relative;
  display: none;
}

.auth-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.7));
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
}

.overlay-content {
  color: white;
  text-align: center;
}

.overlay-content h2 {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--spacing-4);
}

.overlay-content p {
  font-size: var(--font-size-lg);
  opacity: 0.9;
}

@media (min-width: 992px) {
  .auth-image {
    display: block;
  }
}

@media (max-width: 768px) {
  .auth-card {
    padding: var(--spacing-6);
  }
}
</style>
