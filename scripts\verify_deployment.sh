#!/bin/bash
# 部署验证脚本

# 设置变量
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_DIR="./logs"
LOG_FILE="${LOG_DIR}/verify_${TIMESTAMP}.log"
API_BASE_URL="http://localhost:8000/api/v1"
FRONTEND_URL="http://localhost:80"

# 创建日志目录（如果不存在）
mkdir -p ${LOG_DIR}

# 记录日志的函数
log() {
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1" | tee -a ${LOG_FILE}
}

# 错误处理函数
handle_error() {
    log "错误: $1"
    log "验证失败"
    exit 1
}

# 开始验证
log "开始部署验证流程..."

# 1. 验证所有服务是否正常运行
log "步骤1: 验证服务运行状态"

# 检查容器状态
log "检查容器状态..."
docker-compose -f docker-compose.prod.yml ps | grep -q "Up" || handle_error "容器未正常运行"
log "所有容器正常运行"

# 检查后端健康状态
log "检查后端健康状态..."
curl -s -f http://localhost:8000/health > /dev/null || handle_error "后端服务未就绪"
log "后端服务正常"

# 检查前端可访问性
log "检查前端可访问性..."
curl -s -f ${FRONTEND_URL} > /dev/null || handle_error "前端服务未就绪"
log "前端服务正常"

# 2. 执行关键功能的冒烟测试
log "步骤2: 执行冒烟测试"

# 测试产品API
log "测试产品API..."
curl -s -f ${API_BASE_URL}/products > /dev/null || handle_error "产品API测试失败"
log "产品API正常"

# 测试用户API
log "测试用户API..."
curl -s -f ${API_BASE_URL}/users/me -H "Authorization: Bearer test_token" -o /dev/null -w "%{http_code}" | grep -q "4[0-9][0-9]" || handle_error "用户API测试失败"
log "用户API正常"

# 测试支付API
log "测试支付API..."
curl -s -f ${API_BASE_URL}/payments/status/test -o /dev/null -w "%{http_code}" | grep -q "4[0-9][0-9]" || handle_error "支付API测试失败"
log "支付API正常"

# 3. 监控系统性能和日志
log "步骤3: 监控系统性能和日志"

# 检查容器资源使用情况
log "检查容器资源使用情况..."
docker stats --no-stream || handle_error "无法获取容器资源使用情况"

# 检查容器日志是否有错误
log "检查容器日志是否有错误..."
docker-compose -f docker-compose.prod.yml logs --tail=100 backend | grep -i "error\|exception" > ${LOG_DIR}/backend_errors.log
docker-compose -f docker-compose.prod.yml logs --tail=100 frontend | grep -i "error\|exception" > ${LOG_DIR}/frontend_errors.log

# 检查错误日志文件大小
if [ -s ${LOG_DIR}/backend_errors.log ]; then
    log "警告: 后端日志中发现错误，详情请查看 ${LOG_DIR}/backend_errors.log"
else
    log "后端日志正常，未发现错误"
fi

if [ -s ${LOG_DIR}/frontend_errors.log ]; then
    log "警告: 前端日志中发现错误，详情请查看 ${LOG_DIR}/frontend_errors.log"
else
    log "前端日志正常，未发现错误"
fi

# 4. 确认第三方服务集成正常工作
log "步骤4: 确认第三方服务集成"

# 测试支付宝集成
log "测试支付宝集成..."
curl -s -f ${API_BASE_URL}/payments/alipay/test -o /dev/null -w "%{http_code}" | grep -q "4[0-9][0-9]" || handle_error "支付宝集成测试失败"
log "支付宝集成正常"

# 测试邮件服务集成
log "测试邮件服务集成..."
curl -s -f ${API_BASE_URL}/email/test -o /dev/null -w "%{http_code}" | grep -q "4[0-9][0-9]" || handle_error "邮件服务集成测试失败"
log "邮件服务集成正常"

# 验证完成
log "部署验证流程完成"
log "所有服务正常运行，关键功能测试通过，系统性能正常，第三方服务集成正常"
exit 0
