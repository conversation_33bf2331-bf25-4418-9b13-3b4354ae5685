"""
创建数据库表的脚本
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from sqlalchemy.ext.asyncio import create_async_engine

from app.core.config import settings
from app.core.security import get_password_hash
from app.models.user import Base, User
from app.models.order import Order, OrderItem, Payment, LicenseKey
from app.models.product import Product


async def create_tables():
    """创建数据库表"""
    # 创建异步引擎
    if settings.DATABASE_URL.startswith("sqlite"):
        # SQLite连接
        sqlalchemy_url = settings.DATABASE_URL.replace(
            "sqlite:///", "sqlite+aiosqlite:///"
        )
    else:
        # PostgreSQL连接
        sqlalchemy_url = settings.DATABASE_URL.replace(
            "postgresql://", "postgresql+asyncpg://"
        )

    engine = create_async_engine(sqlalchemy_url)

    # 创建表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    print("数据库表创建成功！")


if __name__ == "__main__":
    asyncio.run(create_tables())
