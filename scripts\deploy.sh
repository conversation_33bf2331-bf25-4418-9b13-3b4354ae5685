#!/bin/bash
# 部署脚本

# 设置变量
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="./backups"
LOG_DIR="./logs"
LOG_FILE="${LOG_DIR}/deploy_${TIMESTAMP}.log"

# 创建日志目录（如果不存在）
mkdir -p ${LOG_DIR}

# 记录日志的函数
log() {
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1" | tee -a ${LOG_FILE}
}

# 错误处理函数
handle_error() {
    log "错误: $1"
    log "部署失败，开始回滚..."
    # 这里可以添加回滚逻辑
    exit 1
}

# 开始部署
log "开始部署流程..."

# 1. 备份数据库
log "步骤1: 备份数据库"
./scripts/backup_database.sh || handle_error "数据库备份失败"
log "数据库备份完成"

# 2. 停止现有服务
log "步骤2: 停止现有服务"
docker-compose -f docker-compose.prod.yml down || handle_error "停止服务失败"
log "服务已停止"

# 3. 构建新镜像
log "步骤3: 构建新镜像"
log "构建前端镜像..."
docker build -t sales-platform-frontend:prod -f frontend/Dockerfile.prod ./frontend || handle_error "前端镜像构建失败"
log "构建后端镜像..."
docker build -t sales-platform-backend:prod -f backend/Dockerfile.prod ./backend || handle_error "后端镜像构建失败"
log "镜像构建完成"

# 4. 启动新服务
log "步骤4: 启动新服务"
docker-compose -f docker-compose.prod.yml up -d || handle_error "启动服务失败"
log "服务已启动"

# 5. 等待服务就绪
log "步骤5: 等待服务就绪"
sleep 10
log "检查后端服务健康状态..."
curl -f http://localhost:8000/health || handle_error "后端服务未就绪"
log "检查前端服务健康状态..."
curl -f http://localhost:80 > /dev/null || handle_error "前端服务未就绪"
log "服务已就绪"

# 6. 运行数据库迁移
log "步骤6: 运行数据库迁移"
docker-compose -f docker-compose.prod.yml exec -T backend alembic upgrade head || handle_error "数据库迁移失败"
log "数据库迁移完成"

# 7. 验证部署
log "步骤7: 验证部署"
# 这里可以添加一些基本的验证逻辑，例如检查关键API是否可访问
curl -f http://localhost:8000/api/v1/products > /dev/null || handle_error "API验证失败"
log "部署验证通过"

log "部署流程完成"
exit 0
