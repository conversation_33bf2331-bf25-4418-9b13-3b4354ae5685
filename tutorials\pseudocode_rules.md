# SPARC 伪代码编写规则

## 什么是伪代码？

伪代码（Pseudocode）是一种非正式的、类似于编程语言的文本描述，用于描述算法或程序的逻辑步骤。它不是一种真正的编程语言，没有严格的语法规则，但它应该足够清晰和详细，以便开发人员能够理解并将其转化为实际的代码。

## 伪代码的目的

在 SPARC 工作流中，伪代码的主要目的是：

1.  **连接需求与实现：** 将需求规格（Specification）转化为具体的逻辑步骤，为后续的架构设计和代码实现提供指导。
2.  **清晰表达逻辑：** 用简洁明了的方式描述复杂的算法或流程，避免自然语言的歧义。
3.  **促进团队沟通：** 作为开发团队成员之间交流设计思路的工具，降低沟通成本。
4.  **辅助思考和设计：** 在编写实际代码之前，通过伪代码梳理思路，发现潜在问题。
5.  **作为 TDD 的基础：** 伪代码中的逻辑步骤可以作为编写测试用例（TDD Anchors）的依据。

## 伪代码编写规则

虽然伪代码没有严格的语法，但遵循一些规则可以使其更易于理解和维护：

1.  **使用清晰、简洁的语言：** 避免使用过于专业或含糊不清的术语。
2.  **描述逻辑步骤，而非具体语法：** 关注“做什么”，而不是“如何做”。避免使用特定编程语言的语法细节。
3.  **使用结构化控制流：** 使用常见的控制结构，如 `如果...那么...否则` (IF...THEN...ELSE)、`循环` (LOOP)、`对于每一个...` (FOR EACH)、`当...时...` (WHILE) 等。
4.  **使用缩进表示层级：** 使用缩进来表示代码块的嵌套关系，提高可读性。
5.  **使用动词开头描述操作：** 例如：`获取用户输入`、`计算总和`、`保存数据`。
6.  **明确变量和数据结构：** 使用有意义的名称来表示变量和数据结构。
7.  **包含 TDD 锚点：** 在伪代码中标记出可以编写测试用例的关键逻辑点，例如：`// TDD: 验证用户输入是否有效`。
8.  **保持适当的粒度：** 伪代码的详细程度应适中，既不过于抽象，也不过于具体。它应该能够指导代码实现，但又不限制具体的实现方式。
9.  **头部包含文件路径：** 根据全局指令，在文件头部添加文件路径。

```
# 文件路径: tutorials/pseudocode_rules.md
```

## 示例

假设我们需要编写一个函数来计算购物车中商品的总价（包含税费）。

### 伪代码示例

```
# 文件路径: tutorials/pseudocode_rules.md

函数 计算购物车总价(购物车商品列表, 税率)
  // TDD: 验证购物车商品列表是否为空
  如果 购物车商品列表为空
    返回 0

  总价 = 0

  // TDD: 验证每个商品的价格和数量是否有效
  对于 购物车商品列表中的每一个商品
    商品小计 = 商品.价格 * 商品.数量
    总价 = 总价 + 商品小计

  // TDD: 验证税率是否有效
  如果 税率小于 0 或大于 1
    // 可以选择抛出错误或使用默认税率，这里选择抛出错误
    抛出 错误("无效的税率")

  税额 = 总价 * 税率
  最终总价 = 总价 + 税额

  // TDD: 验证最终总价计算是否正确
  返回 最终总价
结束函数
```

通过遵循这些规则，您可以编写出清晰、易于理解且有助于后续开发工作的伪代码。