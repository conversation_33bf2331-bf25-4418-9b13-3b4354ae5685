<template>
  <el-header class="navbar" :class="{ 'navbar-scrolled': isScrolled }">
    <div class="navbar-container">
      <div class="logo-container">
        <router-link to="/" class="logo">
          <img v-if="siteStore.siteInfo.logo_url" :src="siteStore.siteInfo.logo_url" alt="Logo" class="logo-image" />
          <span class="logo-text">{{ siteStore.siteInfo.company_name }}</span>
        </router-link>
      </div>

      <!-- 移动端菜单按钮 -->
      <div class="mobile-menu-toggle" @click="toggleMobileMenu">
        <el-icon v-if="!mobileMenuOpen"><Menu /></el-icon>
        <el-icon v-else><Close /></el-icon>
      </div>

      <!-- 导航菜单 -->
      <nav class="nav-menu" :class="{ 'mobile-menu-open': mobileMenuOpen }">
        <ul class="nav-links">
          <li :class="{ 'active': isActive('/') }">
            <router-link to="/">首页</router-link>
          </li>
          <li :class="{ 'active': isActive('/products') }">
            <router-link to="/products">产品列表</router-link>
          </li>
          <li v-if="userStore.isLoggedIn && !userStore.isAdmin" :class="{ 'active': isActive('/orders') }">
            <router-link to="/orders">我的订单</router-link>
          </li>
        </ul>

        <div class="nav-actions">
          <!-- 未登录状态显示登录和注册按钮 -->
          <template v-if="!userStore.isLoggedIn">
            <AppButton type="text" @click="$router.push('/login')">登录</AppButton>
            <AppButton type="primary" @click="$router.push('/register')">注册</AppButton>
          </template>

          <!-- 已登录状态显示用户下拉菜单 -->
          <template v-else>
            <el-dropdown @command="handleCommand" trigger="click">
              <div class="user-dropdown">
                <el-avatar :size="32" icon="UserFilled">{{ userInitial }}</el-avatar>
                <span class="username">{{ userStore.username || '用户' }}</span>
                <el-icon><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>个人中心
                  </el-dropdown-item>
                  <el-dropdown-item v-if="!userStore.isAdmin" command="orders">
                    <el-icon><ShoppingCart /></el-icon>我的订单
                  </el-dropdown-item>
                  <el-dropdown-item v-if="userStore.isAdmin" command="admin">
                    <el-icon><Setting /></el-icon>管理后台
                  </el-dropdown-item>
                  <el-dropdown-item v-if="userStore.isAdmin" command="adminOrders">
                    <el-icon><List /></el-icon>订单管理
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </div>
      </nav>
    </div>
  </el-header>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore, useSiteStore } from '../store'
import {
  ArrowDown,
  Menu,
  Close,
  User,
  ShoppingCart,
  Setting,
  List,
  SwitchButton
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const siteStore = useSiteStore()

// 移动菜单状态
const mobileMenuOpen = ref(false)

// 滚动状态
const isScrolled = ref(false)

// 用户名首字母（用于头像）
const userInitial = computed(() => {
  const username = userStore.username || '用户'
  return username.charAt(0).toUpperCase()
})

// 检查当前路由是否激活
const isActive = (path) => {
  return route.path === path
}

// 切换移动菜单
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value

  // 如果菜单打开，禁止页面滚动
  if (mobileMenuOpen.value) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = ''
  }
}

// 处理下拉菜单命令
const handleCommand = (command) => {
  if (command === 'profile') {
    router.push('/profile')
  } else if (command === 'orders') {
    router.push('/orders')
  } else if (command === 'admin') {
    router.push('/admin')
  } else if (command === 'adminOrders') {
    router.push('/admin/orders')
  } else if (command === 'logout') {
    userStore.logout()
    ElMessage.success('已退出登录')
    router.push('/')
  }

  // 关闭移动菜单
  mobileMenuOpen.value = false
}

// 监听滚动事件
const handleScroll = () => {
  isScrolled.value = window.scrollY > 50
}

// 在组件挂载时获取站点信息并添加滚动监听
onMounted(() => {
  siteStore.fetchSiteInfo()
  window.addEventListener('scroll', handleScroll)
  handleScroll() // 初始检查
})

// 在组件卸载前移除滚动监听
onBeforeUnmount(() => {
  window.removeEventListener('scroll', handleScroll)

  // 恢复页面滚动
  document.body.style.overflow = ''
})

// 监听路由变化，关闭移动菜单
router.afterEach(() => {
  mobileMenuOpen.value = false
  document.body.style.overflow = ''
})
</script>

<style scoped>
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-index-fixed);
  height: auto;
  padding: var(--spacing-4) 0;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal) ease;
}

.navbar-scrolled {
  padding: var(--spacing-2) 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.navbar-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-xl);
  transition: transform var(--transition-fast) ease;
}

.logo:hover {
  transform: scale(1.05);
}

.logo-image {
  height: 36px;
  margin-right: var(--spacing-2);
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-6);
}

.nav-links {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--spacing-6);
}

.nav-links li {
  position: relative;
}

.nav-links a {
  color: var(--text-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-2) 0;
  transition: color var(--transition-fast) ease;
}

.nav-links a:hover,
.nav-links li.active a {
  color: var(--primary-color);
}

.nav-links li.active::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary-color);
  border-radius: var(--border-radius-full);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.user-dropdown {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-md);
  transition: background-color var(--transition-fast) ease;
}

.user-dropdown:hover {
  background-color: var(--bg-secondary);
}

.username {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-right: var(--spacing-1);
}

.mobile-menu-toggle {
  display: none;
  cursor: pointer;
  font-size: 24px;
  color: var(--text-primary);
}

@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: block;
  }

  .nav-menu {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--bg-primary);
    flex-direction: column;
    justify-content: flex-start;
    padding: var(--spacing-6);
    transform: translateX(100%);
    transition: transform var(--transition-normal) ease;
    z-index: var(--z-index-fixed);
    overflow-y: auto;
  }

  .mobile-menu-open {
    transform: translateX(0);
  }

  .nav-links {
    flex-direction: column;
    width: 100%;
    gap: var(--spacing-4);
  }

  .nav-links li {
    width: 100%;
    text-align: center;
  }

  .nav-links a {
    display: block;
    padding: var(--spacing-3);
    font-size: var(--font-size-lg);
  }

  .nav-links li.active::after {
    display: none;
  }

  .nav-links li.active a {
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius-md);
  }

  .nav-actions {
    margin-top: var(--spacing-6);
    width: 100%;
    justify-content: center;
  }

  .user-dropdown {
    width: 100%;
    justify-content: center;
    padding: var(--spacing-3);
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius-md);
  }
}
</style>
