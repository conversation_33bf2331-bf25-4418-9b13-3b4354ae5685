from typing import Dict, List, Optional
from datetime import datetime

from sqlalchemy import select, func, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from app.models.user import User
from app.models.order import Order, OrderItem
from app.models.product import Product
from app.services.user import user_service
from app.services.product import product_service


class AdminService:
    """
    管理员服务类
    """

    async def get_sales_statistics(self, db: AsyncSession) -> Dict:
        """
        获取销售统计

        Args:
            db: 数据库会话

        Returns:
            Dict: 销售统计数据
        """
        # 获取用户总数
        user_count = await user_service.get_user_count(db)

        # 获取订单总数
        order_count_query = select(func.count(Order.id))
        order_count_result = await db.execute(order_count_query)
        order_count = order_count_result.scalar() or 0

        # 获取已支付订单总数
        paid_order_count_query = select(func.count(Order.id)).where(Order.status == "paid")
        paid_order_count_result = await db.execute(paid_order_count_query)
        paid_order_count = paid_order_count_result.scalar() or 0

        # 获取软件产品销售数量
        software_sales_query = (
            select(func.count(Order.id))
            .join(OrderItem, OrderItem.order_id == Order.id)
            .join(Product, OrderItem.product_id == Product.id)
            .where(Product.product_type == "software", Order.status == "paid")
        )
        software_sales_result = await db.execute(software_sales_query)
        software_sales = software_sales_result.scalar() or 0

        # 获取硬件产品销售数量
        hardware_sales_query = (
            select(func.count(Order.id))
            .join(OrderItem, OrderItem.order_id == Order.id)
            .join(Product, OrderItem.product_id == Product.id)
            .where(Product.product_type == "hardware", Order.status == "paid")
        )
        hardware_sales_result = await db.execute(hardware_sales_query)
        hardware_sales = hardware_sales_result.scalar() or 0

        # 获取产品总数
        product_count = await product_service.get_products_count(db)

        # 获取活跃产品数量
        active_product_count_query = select(func.count(Product.id)).where(Product.is_active == True)
        active_product_count_result = await db.execute(active_product_count_query)
        active_product_count = active_product_count_result.scalar() or 0

        return {
            "user_count": user_count,
            "order_count": order_count,
            "paid_order_count": paid_order_count,
            "software_sales": software_sales,
            "hardware_sales": hardware_sales,
            "product_count": product_count,
            "active_product_count": active_product_count,
        }

    async def get_all_orders(
        self, db: AsyncSession, skip: int = 0, limit: int = 100, status: Optional[str] = None
    ) -> List[dict]:
        """
        获取所有订单

        Args:
            db: 数据库会话
            skip: 跳过记录数
            limit: 限制记录数
            status: 订单状态筛选

        Returns:
            List[dict]: 订单列表（字典形式）
        """
        # 构建查询
        query = select(Order).options(joinedload(Order.user), joinedload(Order.order_items).joinedload(OrderItem.product))

        # 添加状态筛选
        if status:
            query = query.where(Order.status == status)

        # 添加排序、分页
        query = query.order_by(Order.created_at.desc()).offset(skip).limit(limit)

        # 执行查询
        result = await db.execute(query)
        orders = list(result.scalars().unique())

        # 将ORM模型对象转换为字典
        order_dicts = []
        for order in orders:
            # 获取订单项和产品信息
            order_items = []
            for item in order.order_items:
                # 构建完整的产品对象
                product_obj = None
                if item.product:
                    product_obj = {
                        "id": item.product.id,
                        "name": item.product.name,
                        "description": item.product.description,
                        "price": item.product.price,
                        "product_type": item.product.product_type,
                        "is_active": item.product.is_active
                    }

                order_items.append({
                    "id": item.id,
                    "product_id": item.product_id,
                    "quantity": item.quantity,
                    "price": item.price,
                    "product": product_obj
                })

            # 构建订单字典
            order_dict = {
                "id": order.id,
                "order_number": order.order_number,
                "user_id": order.user_id,
                "total_amount": order.total_amount,
                "status": order.status,
                "payment_method": order.payment_method,
                "created_at": order.created_at,
                "updated_at": order.updated_at,
                "user": {
                    "id": order.user.id,
                    "username": order.user.username,
                    "email": order.user.email
                } if order.user else None,
                "order_items": order_items,
                # 为了兼容前端，同时提供items字段
                "items": order_items
            }
            order_dicts.append(order_dict)

        return order_dicts

    async def get_orders_count(self, db: AsyncSession, status: Optional[str] = None) -> int:
        """
        获取订单总数

        Args:
            db: 数据库会话
            status: 订单状态筛选

        Returns:
            int: 订单总数
        """
        # 构建查询
        query = select(func.count(Order.id))

        # 添加状态筛选
        if status:
            query = query.where(Order.status == status)

        # 执行查询
        result = await db.execute(query)
        return result.scalar() or 0

    async def get_order_by_id(self, db: AsyncSession, order_id: int) -> Optional[dict]:
        """
        获取订单详情

        Args:
            db: 数据库会话
            order_id: 订单ID

        Returns:
            Optional[dict]: 订单详情字典，如果不存在则返回None
        """
        # 构建查询
        query = (
            select(Order)
            .options(
                joinedload(Order.user),
                joinedload(Order.order_items).joinedload(OrderItem.product)
            )
            .where(Order.id == order_id)
        )

        # 执行查询
        result = await db.execute(query)
        order = result.scalar_one_or_none()

        # 如果订单不存在，返回None
        if not order:
            return None

        # 获取订单项和产品信息
        order_items = []
        for item in order.order_items:
            # 构建完整的产品对象
            product_obj = None
            if item.product:
                product_obj = {
                    "id": item.product.id,
                    "name": item.product.name,
                    "description": item.product.description,
                    "price": item.product.price,
                    "product_type": item.product.product_type,
                    "is_active": item.product.is_active
                }

            order_items.append({
                "id": item.id,
                "product_id": item.product_id,
                "quantity": item.quantity,
                "price": item.price,
                "product": product_obj
            })

        # 构建订单字典
        order_dict = {
            "id": order.id,
            "order_number": order.order_number,
            "user_id": order.user_id,
            "total_amount": order.total_amount,
            "status": order.status,
            "payment_method": order.payment_method,
            "created_at": order.created_at,
            "updated_at": order.updated_at,
            "user": {
                "id": order.user.id,
                "username": order.user.username,
                "email": order.user.email
            } if order.user else None,
            "order_items": order_items,
            # 为了兼容前端，同时提供items字段
            "items": order_items
        }

        return order_dict

    async def update_order_status(self, db: AsyncSession, order_id: int, status: str) -> Optional[dict]:
        """
        更新订单状态

        Args:
            db: 数据库会话
            order_id: 订单ID
            status: 新状态

        Returns:
            Optional[dict]: 更新后的订单字典，如果不存在则返回None
        """
        # 获取订单
        query = select(Order).where(Order.id == order_id)
        result = await db.execute(query)
        order = result.scalar_one_or_none()

        # 如果订单不存在，返回None
        if not order:
            return None

        # 更新订单状态
        order.status = status

        # 如果状态是已支付，更新支付时间
        if status == "paid" and not order.paid_at:
            order.paid_at = datetime.now()
            order.payment_method = order.payment_method or "manual"

        # 提交更改
        await db.commit()
        await db.refresh(order)

        # 获取更新后的订单详情
        return await self.get_order_by_id(db, order_id)

    async def delete_order(self, db: AsyncSession, order_id: int) -> Optional[bool]:
        """
        删除订单
        只能删除未支付且已取消的订单

        Args:
            db: 数据库会话
            order_id: 订单ID

        Returns:
            Optional[bool]:
                - None: 订单不存在
                - False: 订单不满足删除条件
                - True: 删除成功
        """
        # 获取订单
        query = select(Order).where(Order.id == order_id)
        result = await db.execute(query)
        order = result.scalar_one_or_none()

        # 如果订单不存在，返回None
        if not order:
            return None

        # 检查订单是否满足删除条件：未支付且已取消
        if order.status != "cancelled" or order.paid_at is not None:
            return False

        # 删除订单项
        delete_items_query = delete(OrderItem).where(OrderItem.order_id == order_id)
        await db.execute(delete_items_query)

        # 删除订单
        delete_order_query = delete(Order).where(Order.id == order_id)
        await db.execute(delete_order_query)

        # 提交更改
        await db.commit()

        return True

    async def toggle_user_status(
        self, db: AsyncSession, user_id: int, is_active: bool
    ) -> Optional[User]:
        """
        切换用户状态

        Args:
            db: 数据库会话
            user_id: 用户ID
            is_active: 是否激活

        Returns:
            Optional[User]: 更新后的用户对象，如果不存在则返回None
        """
        # 获取用户
        user = await user_service.get_user_by_id(db, user_id)
        if not user:
            return None

        # 更新用户状态
        user.is_active = is_active
        await db.commit()
        await db.refresh(user)
        return user


# 创建管理员服务实例
admin_service = AdminService()