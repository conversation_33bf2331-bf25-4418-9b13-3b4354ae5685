from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Float, Integer, String
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class Product(Base):
    """
    产品数据模型

    Attributes:
        id: 产品ID
        name: 产品名称
        description: 产品描述
        price: 产品价格
        stock: 库存数量
        product_type: 产品类型（software或hardware）
        is_active: 是否激活
        created_at: 创建时间
        updated_at: 更新时间
    """
    __tablename__ = "products"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(String, nullable=True)
    price = Column(Float, nullable=False)
    stock = Column(Integer, default=0)
    product_type = Column(String(20), nullable=False)  # software 或 hardware
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    order_items = relationship("OrderItem", back_populates="product")