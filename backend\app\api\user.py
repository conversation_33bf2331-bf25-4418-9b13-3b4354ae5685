from typing import Annotated, List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy import select, update, func

from app.core.dependencies import DB, get_current_active_user, get_current_admin_user
from app.models.user import User
from app.models.order import LicenseKey
from app.schemas.user import User as UserSchema
from app.schemas.user import UserCreate, UserUpdate
from app.schemas.order import LicenseKeyResponse
from app.services.user import user_service
from app.services.payment import order_service

# 创建路由器
router = APIRouter(prefix="/users", tags=["users"])


@router.post("/", response_model=UserSchema)
async def create_user(user_create: UserCreate, db: DB):
    """
    创建用户

    Args:
        user_create: 用户创建模型
        db: 数据库会话

    Returns:
        User: 创建的用户对象

    Raises:
        HTTPException: 用户名或邮箱已存在时抛出
    """
    # 检查用户名是否已存在
    db_user = await user_service.get_user_by_username(db, user_create.username)
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在",
        )

    # 检查邮箱是否已存在
    db_user = await user_service.get_user_by_email(db, user_create.email)
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已存在",
        )

    # 创建用户
    user = await user_service.create_user(db, user_create)
    return user


@router.get("/me", response_model=UserSchema)
async def read_users_me(
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """
    获取当前用户信息

    Args:
        current_user: 当前用户

    Returns:
        User: 当前用户对象
    """
    return current_user


@router.put("/me", response_model=UserSchema)
async def update_user_me(
    user_update: UserUpdate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: DB,
):
    """
    更新当前用户信息

    Args:
        user_update: 用户更新模型
        current_user: 当前用户
        db: 数据库会话

    Returns:
        User: 更新后的用户对象

    Raises:
        HTTPException: 用户名或邮箱已存在时抛出
    """
    # 检查用户名是否已存在
    if user_update.username and user_update.username != current_user.username:
        db_user = await user_service.get_user_by_username(db, user_update.username)
        if db_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在",
            )

    # 检查邮箱是否已存在
    if user_update.email and user_update.email != current_user.email:
        db_user = await user_service.get_user_by_email(db, user_update.email)
        if db_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在",
            )

    # 更新用户
    user = await user_service.update_user(db, current_user.id, user_update)
    return user


@router.get("/", response_model=List[UserSchema])
async def read_users(
    current_user: Annotated[User, Depends(get_current_admin_user)],
    db: DB,
    skip: int = 0,
    limit: int = 100,
):
    """
    获取用户列表（仅管理员）

    Args:
        skip: 跳过记录数
        limit: 限制记录数
        current_user: 当前管理员用户
        db: 数据库会话

    Returns:
        List[User]: 用户列表
    """
    users = await user_service.get_users(db, skip=skip, limit=limit)
    return users


@router.get("/me/license-keys", response_model=List[LicenseKeyResponse])
async def get_user_license_keys(
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: DB,
):
    """
    获取当前用户的软件许可密钥列表

    Args:
        current_user: 当前用户
        db: 数据库会话

    Returns:
        List[LicenseKeyResponse]: 软件许可密钥响应模型列表
    """
    try:
        license_keys = await order_service.get_user_license_keys(db, current_user.id)
        return license_keys
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取软件许可密钥列表失败: {str(e)}",
        )


@router.post("/me/license-keys/{product_id}/regenerate")
async def regenerate_license_key(
    product_id: int,
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: DB,
):
    """
    重新生成指定产品的许可密钥

    Args:
        product_id: 产品ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        dict: 操作结果
    """
    try:
        # 查找用户的该产品许可密钥
        result = await db.execute(
            select(LicenseKey).where(
                LicenseKey.user_id == current_user.id,
                LicenseKey.product_id == product_id,
                LicenseKey.is_active == True
            )
        )
        license_key = result.scalar_one_or_none()

        if not license_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="未找到该产品的有效许可密钥"
            )

        # 生成新的许可密钥
        new_key = order_service.generate_license_key()

        # 更新数据库中的许可密钥
        await db.execute(
            update(LicenseKey)
            .where(LicenseKey.id == license_key.id)
            .values(
                license_key=new_key,
                updated_at=func.now()
            )
        )
        await db.commit()

        return {
            "success": True,
            "message": "密钥重新生成成功",
            "data": {
                "new_license_key": new_key,
                "product_id": product_id
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重新生成密钥失败: {str(e)}"
        )