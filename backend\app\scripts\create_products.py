"""
创建初始产品数据脚本
"""
import asyncio
import logging

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.schemas.product import ProductCreate
from app.services.product import product_service

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始产品数据
INITIAL_PRODUCTS = [
    ProductCreate(
        name="专业版软件许可证",
        description="我们的旗舰软件产品，包含所有高级功能，适合专业用户和企业使用。",
        price=999.00,
        product_type="software",
        stock=100,
    ),
    ProductCreate(
        name="标准版软件许可证",
        description="适合中小型企业的软件版本，包含核心功能和基本支持。",
        price=499.00,
        product_type="software",
        stock=200,
    ),
    ProductCreate(
        name="入门版软件许可证",
        description="适合个人用户和小型团队的基础版本，包含必要功能。",
        price=199.00,
        product_type="software",
        stock=300,
    ),
    ProductCreate(
        name="高性能服务器",
        description="企业级高性能服务器，适合大型应用和数据处理。",
        price=9999.00,
        product_type="hardware",
        stock=50,
    ),
    ProductCreate(
        name="网络安全设备",
        description="专业级网络安全防护设备，提供全方位的网络安全保障。",
        price=5999.00,
        product_type="hardware",
        stock=30,
    ),
    ProductCreate(
        name="智能办公设备套装",
        description="包含多种智能办公设备，提升办公效率和体验。",
        price=3999.00,
        product_type="hardware",
        stock=80,
    ),
]


async def create_initial_products():
    """创建初始产品数据"""
    logger.info("开始创建初始产品数据...")

    # 获取数据库会话
    db_generator = get_db()
    db: AsyncSession = await anext(db_generator)

    try:
        # 检查是否已有产品数据
        products = await product_service.get_products(db, limit=1)
        if products:
            logger.info("已存在产品数据，跳过创建")
            return

        # 创建产品
        for product_data in INITIAL_PRODUCTS:
            product = await product_service.create_product(db, product_data)
            logger.info(f"创建产品成功: {product.name}")

        logger.info("初始产品数据创建完成")
    except Exception as e:
        logger.error(f"创建产品数据失败: {e}")
    finally:
        await db.close()


if __name__ == "__main__":
    asyncio.run(create_initial_products())
