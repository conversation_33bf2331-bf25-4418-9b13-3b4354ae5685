# 前端管理员视图伪代码

## 1. 管理员仪表盘 (views/admin/Dashboard.vue)

```vue
<template>
  <div class="admin-dashboard">
    <h1>管理员仪表盘</h1>
    
    <el-row :gutter="20">
      <!-- 用户统计卡片 -->
      <el-col :xs="24" :sm="12" :md="8">
        <el-card class="stat-card">
          <h3>用户总数</h3>
          <div class="stat-value">{{ statistics.user_count }}</div>
        </el-card>
      </el-col>
      
      <!-- 软件销售统计卡片 -->
      <el-col :xs="24" :sm="12" :md="8">
        <el-card class="stat-card">
          <h3>软件销售总量</h3>
          <div class="stat-value">{{ statistics.software_sales }}</div>
        </el-card>
      </el-col>
      
      <!-- 设备销售统计卡片 -->
      <el-col :xs="24" :sm="12" :md="8">
        <el-card class="stat-card">
          <h3>设备销售总量</h3>
          <div class="stat-value">{{ statistics.device_sales }}</div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 最近订单 -->
    <el-card class="recent-orders">
      <h2>最近订单</h2>
      <el-table :data="recentOrders" style="width: 100%">
        <el-table-column prop="order_number" label="订单号"></el-table-column>
        <el-table-column prop="product.name" label="产品名称"></el-table-column>
        <el-table-column prop="status" label="状态">
          <template #default="scope">
            <el-tag :type="getOrderStatusType(scope.row.status)">
              {{ getOrderStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间"></el-table-column>
      </el-table>
      <div class="view-more">
        <el-button type="text" @click="goToOrders">查看更多订单</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { adminApi } from '@/api/admin';

const router = useRouter();
const statistics = ref({
  user_count: 0,
  software_sales: 0,
  device_sales: 0
});
const recentOrders = ref([]);

// 获取统计数据
const fetchStatistics = async () => {
  try {
    const response = await adminApi.getStatistics();
    if (response.success) {
      statistics.value = response.data;
    } else {
      ElMessage.error('获取统计数据失败');
    }
  } catch (error) {
    console.error('获取统计数据失败:', error);
    ElMessage.error('获取统计数据失败');
  }
};

// 获取最近订单
const fetchRecentOrders = async () => {
  try {
    const response = await adminApi.getAllOrders({ limit: 5 });
    if (response.success) {
      recentOrders.value = response.data;
    } else {
      ElMessage.error('获取最近订单失败');
    }
  } catch (error) {
    console.error('获取最近订单失败:', error);
    ElMessage.error('获取最近订单失败');
  }
};

// 获取订单状态类型
const getOrderStatusType = (status) => {
  switch (status) {
    case 'paid':
      return 'success';
    case 'pending':
      return 'warning';
    case 'cancelled':
      return 'danger';
    default:
      return 'info';
  }
};

// 获取订单状态文本
const getOrderStatusText = (status) => {
  switch (status) {
    case 'paid':
      return '已支付';
    case 'pending':
      return '待支付';
    case 'cancelled':
      return '已取消';
    default:
      return '未知';
  }
};

// 跳转到订单页面
const goToOrders = () => {
  router.push('/admin/orders');
};

onMounted(() => {
  fetchStatistics();
  fetchRecentOrders();
});
</script>
```

## 2. 产品管理页面 (views/admin/Products.vue)

```vue
<template>
  <div class="admin-products">
    <div class="page-header">
      <h1>产品管理</h1>
      <el-button type="primary" @click="showAddProductDialog">添加新产品</el-button>
    </div>
    
    <!-- 产品列表 -->
    <el-card>
      <el-table :data="products" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="name" label="产品名称"></el-table-column>
        <el-table-column prop="description" label="描述" show-overflow-tooltip></el-table-column>
        <el-table-column prop="price" label="价格">
          <template #default="scope">
            ¥{{ scope.row.price }}
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型">
          <template #default="scope">
            <el-tag :type="scope.row.type === 'software' ? 'success' : 'warning'">
              {{ scope.row.type === 'software' ? '软件' : '设备' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="is_active" label="状态">
          <template #default="scope">
            <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
              {{ scope.row.is_active ? '上架中' : '已下架' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button 
              size="small" 
              :type="scope.row.is_active ? 'danger' : 'success'"
              @click="handleToggleStatus(scope.row)"
            >
              {{ scope.row.is_active ? '下架' : '上架' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 添加/编辑产品对话框 -->
    <el-dialog 
      :title="isEdit ? '编辑产品' : '添加新产品'" 
      v-model="dialogVisible"
      width="500px"
    >
      <el-form :model="productForm" :rules="rules" ref="productFormRef" label-width="100px">
        <el-form-item label="产品名称" prop="name">
          <el-input v-model="productForm.name"></el-input>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input type="textarea" v-model="productForm.description" rows="4"></el-input>
        </el-form-item>
        
        <el-form-item label="价格" prop="price">
          <el-input-number v-model="productForm.price" :precision="2" :min="0"></el-input-number>
        </el-form-item>
        
        <el-form-item label="类型" prop="type">
          <el-select v-model="productForm.type" placeholder="请选择产品类型">
            <el-option label="软件" value="software"></el-option>
            <el-option label="设备" value="device"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveProduct" :loading="loading">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { adminApi } from '@/api/admin';

const products = ref([]);
const dialogVisible = ref(false);
const isEdit = ref(false);
const loading = ref(false);
const productFormRef = ref(null);
const currentProductId = ref(null);

// 产品表单
const productForm = reactive({
  name: '',
  description: '',
  price: 0,
  type: 'software'
});

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入产品名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入产品描述', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入产品价格', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择产品类型', trigger: 'change' }
  ]
};

// 获取产品列表
const fetchProducts = async () => {
  try {
    const response = await adminApi.getProducts();
    if (response.success) {
      products.value = response.data;
    } else {
      ElMessage.error('获取产品列表失败');
    }
  } catch (error) {
    console.error('获取产品列表失败:', error);
    ElMessage.error('获取产品列表失败');
  }
};

// 显示添加产品对话框
const showAddProductDialog = () => {
  isEdit.value = false;
  currentProductId.value = null;
  productForm.name = '';
  productForm.description = '';
  productForm.price = 0;
  productForm.type = 'software';
  dialogVisible.value = true;
};

// 处理编辑产品
const handleEdit = (product) => {
  isEdit.value = true;
  currentProductId.value = product.id;
  productForm.name = product.name;
  productForm.description = product.description;
  productForm.price = product.price;
  productForm.type = product.type;
  dialogVisible.value = true;
};

// 处理切换产品状态
const handleToggleStatus = async (product) => {
  try {
    const response = await adminApi.updateProduct(product.id, {
      is_active: !product.is_active
    });
    
    if (response.success) {
      ElMessage.success(`产品已${product.is_active ? '下架' : '上架'}`);
      fetchProducts();
    } else {
      ElMessage.error('操作失败');
    }
  } catch (error) {
    console.error('操作失败:', error);
    ElMessage.error('操作失败');
  }
};

// 保存产品
const handleSaveProduct = async () => {
  if (!productFormRef.value) return;
  
  await productFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true;
      try {
        let response;
        
        if (isEdit.value) {
          // 更新产品
          response = await adminApi.updateProduct(currentProductId.value, productForm);
        } else {
          // 创建产品
          response = await adminApi.createProduct(productForm);
        }
        
        if (response.success) {
          ElMessage.success(isEdit.value ? '产品更新成功' : '产品添加成功');
          dialogVisible.value = false;
          fetchProducts();
        } else {
          ElMessage.error(response.message || '操作失败');
        }
      } catch (error) {
        console.error('操作失败:', error);
        ElMessage.error('操作失败');
      } finally {
        loading.value = false;
      }
    }
  });
};

onMounted(() => {
  fetchProducts();
});
</script>
```
