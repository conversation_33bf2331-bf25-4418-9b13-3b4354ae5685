from typing import List, Optional, Annotated

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.dependencies import get_current_admin_user
from app.models.user import User
from app.schemas.user import User as UserSchema
from app.schemas.order import OrderResponse as OrderSchema
from app.schemas.response import ResponseModel, PaginatedResponseModel, DictResponseModel, DictListResponseModel
from app.services.user import user_service
from app.services.admin import admin_service

# 创建路由器
router = APIRouter(prefix="/admin", tags=["admin"])


@router.get("/users", response_model=DictListResponseModel)
async def get_users(
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_admin_user)],
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(10, ge=1, le=100, description="限制记录数"),
):
    """
    获取用户列表（仅管理员）

    Args:
        db: 数据库会话
        current_user: 当前管理员用户
        skip: 跳过记录数
        limit: 限制记录数

    Returns:
        PaginatedResponseModel: 分页用户列表
    """
    # 获取用户列表（已经是字典形式）
    users = await user_service.get_users(db, skip=skip, limit=limit)
    # 获取用户总数
    total = await user_service.get_user_count(db)

    return PaginatedResponseModel(
        success=True,
        message="获取用户列表成功",
        data=users,
        total=total,
        page=skip // limit + 1,
        page_size=limit,
    )


@router.get("/statistics", response_model=ResponseModel)
async def get_statistics(
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_admin_user)],
):
    """
    获取销售统计（仅管理员）

    Args:
        db: 数据库会话
        current_user: 当前管理员用户

    Returns:
        ResponseModel: 销售统计数据
    """
    # 获取销售统计
    statistics = await admin_service.get_sales_statistics(db)

    return ResponseModel(
        success=True,
        message="获取销售统计成功",
        data=statistics,
    )


@router.get("/orders", response_model=DictListResponseModel)
async def get_all_orders(
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_admin_user)],
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(10, ge=1, le=100, description="限制记录数"),
    status: Optional[str] = Query(None, description="订单状态筛选"),
):
    """
    获取所有订单（仅管理员）

    Args:
        db: 数据库会话
        current_user: 当前管理员用户
        skip: 跳过记录数
        limit: 限制记录数
        status: 订单状态筛选

    Returns:
        PaginatedResponseModel: 分页订单列表
    """
    # 获取订单列表（已经是字典形式）
    orders = await admin_service.get_all_orders(db, skip=skip, limit=limit, status=status)
    # 获取订单总数
    total = await admin_service.get_orders_count(db, status=status)

    return PaginatedResponseModel(
        success=True,
        message="获取订单列表成功",
        data=orders,
        total=total,
        page=skip // limit + 1,
        page_size=limit,
    )


@router.get("/orders/{order_id}", response_model=DictResponseModel)
async def get_order_by_id(
    order_id: int,
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_admin_user)],
):
    """
    获取订单详情（仅管理员）

    Args:
        order_id: 订单ID
        db: 数据库会话
        current_user: 当前管理员用户

    Returns:
        ResponseModel: 订单详情

    Raises:
        HTTPException: 订单不存在时抛出
    """
    # 获取订单详情
    order = await admin_service.get_order_by_id(db, order_id)
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单不存在",
        )

    return ResponseModel(
        success=True,
        message="获取订单详情成功",
        data=order,
    )


@router.patch("/orders/{order_id}/status", response_model=DictResponseModel)
async def update_order_status(
    order_id: int,
    status: str,
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_admin_user)],
):
    """
    更新订单状态（仅管理员）

    Args:
        order_id: 订单ID
        status: 新状态（paid, cancelled等）
        db: 数据库会话
        current_user: 当前管理员用户

    Returns:
        ResponseModel: 更新后的订单

    Raises:
        HTTPException: 订单不存在或状态无效时抛出
    """
    # 验证状态值
    valid_statuses = ["pending", "paid", "cancelled"]
    if status not in valid_statuses:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无效的订单状态，有效值为: {', '.join(valid_statuses)}",
        )

    # 更新订单状态
    updated_order = await admin_service.update_order_status(db, order_id, status)
    if not updated_order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单不存在",
        )

    return ResponseModel(
        success=True,
        message=f"订单状态已更新为{status}",
        data=updated_order,
    )


@router.delete("/orders/{order_id}", response_model=DictResponseModel)
async def delete_order(
    order_id: int,
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_admin_user)],
):
    """
    删除订单（仅管理员）
    只能删除未支付且已取消的订单

    Args:
        order_id: 订单ID
        db: 数据库会话
        current_user: 当前管理员用户

    Returns:
        ResponseModel: 删除结果

    Raises:
        HTTPException: 订单不存在或不满足删除条件时抛出
    """
    # 删除订单
    result = await admin_service.delete_order(db, order_id)

    if result is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单不存在",
        )

    if result is False:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只能删除未支付且已取消的订单",
        )

    return ResponseModel(
        success=True,
        message="订单已删除",
        data={"id": order_id},
    )


@router.patch("/users/{user_id}/status", response_model=DictResponseModel)
async def toggle_user_status(
    user_id: int,
    is_active: bool,
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_admin_user)],
):
    """
    切换用户状态（仅管理员）

    Args:
        user_id: 用户ID
        is_active: 是否激活
        db: 数据库会话
        current_user: 当前管理员用户

    Returns:
        ResponseModel: 更新后的用户

    Raises:
        HTTPException: 用户不存在时抛出
    """
    # 不允许管理员禁用自己
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能修改自己的状态",
        )

    # 切换用户状态
    user = await admin_service.toggle_user_status(db, user_id, is_active)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )

    # 将ORM模型对象转换为字典
    user_dict = {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "is_active": user.is_active,
        "role": user.role,
        "address": user.address,
        "created_at": user.created_at
    }

    return ResponseModel(
        success=True,
        message=f"用户已{'激活' if is_active else '禁用'}",
        data=user_dict,
    )