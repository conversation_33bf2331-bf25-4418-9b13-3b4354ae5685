from typing import List, Optional, Tuple

from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.product import Product
from app.schemas.product import ProductCreate, ProductUpdate


class ProductService:
    """
    产品服务类
    """

    async def create_product(self, db: AsyncSession, product_create: ProductCreate) -> Product:
        """
        创建产品

        Args:
            db: 数据库会话
            product_create: 产品创建模型

        Returns:
            Product: 创建的产品对象
        """
        db_product = Product(**product_create.model_dump())
        db.add(db_product)
        await db.commit()
        await db.refresh(db_product)
        return db_product

    async def get_product_by_id(self, db: AsyncSession, product_id: int) -> Optional[Product]:
        """
        通过ID获取产品

        Args:
            db: 数据库会话
            product_id: 产品ID

        Returns:
            Optional[Product]: 产品对象，如果不存在则返回None
        """
        result = await db.execute(select(Product).where(Product.id == product_id))
        return result.scalars().first()

    async def get_products(
        self,
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100,
        product_type: Optional[str] = None,
        is_active: Optional[bool] = None,
    ) -> List[Product]:
        """
        获取产品列表

        Args:
            db: 数据库会话
            skip: 跳过记录数
            limit: 限制记录数
            product_type: 产品类型筛选
            is_active: 是否激活筛选

        Returns:
            List[Product]: 产品列表
        """
        query = select(Product)

        # 应用筛选条件
        if product_type:
            query = query.where(Product.product_type == product_type)
        if is_active is not None:
            query = query.where(Product.is_active == is_active)

        # 应用分页
        query = query.offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    async def get_products_count(
        self,
        db: AsyncSession,
        product_type: Optional[str] = None,
        is_active: Optional[bool] = None,
    ) -> int:
        """
        获取产品总数

        Args:
            db: 数据库会话
            product_type: 产品类型筛选
            is_active: 是否激活筛选

        Returns:
            int: 产品总数
        """
        query = select(func.count(Product.id))

        # 应用筛选条件
        if product_type:
            query = query.where(Product.product_type == product_type)
        if is_active is not None:
            query = query.where(Product.is_active == is_active)

        result = await db.execute(query)
        return result.scalar()

    async def update_product(
        self, db: AsyncSession, product_id: int, product_update: ProductUpdate
    ) -> Optional[Product]:
        """
        更新产品

        Args:
            db: 数据库会话
            product_id: 产品ID
            product_update: 产品更新模型

        Returns:
            Optional[Product]: 更新后的产品对象，如果不存在则返回None
        """
        # 获取产品
        db_product = await self.get_product_by_id(db, product_id)
        if not db_product:
            return None

        # 更新产品
        update_data = product_update.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_product, key, value)

        await db.commit()
        await db.refresh(db_product)
        return db_product

    async def toggle_product_status(
        self, db: AsyncSession, product_id: int, is_active: bool
    ) -> Optional[Product]:
        """
        切换产品状态

        Args:
            db: 数据库会话
            product_id: 产品ID
            is_active: 是否激活

        Returns:
            Optional[Product]: 更新后的产品对象，如果不存在则返回None
        """
        # 获取产品
        db_product = await self.get_product_by_id(db, product_id)
        if not db_product:
            return None

        # 更新状态
        db_product.is_active = is_active
        await db.commit()
        await db.refresh(db_product)
        return db_product

    async def delete_product(self, db: AsyncSession, product_id: int) -> bool:
        """
        删除产品

        Args:
            db: 数据库会话
            product_id: 产品ID

        Returns:
            bool: 删除成功返回True，产品不存在返回False
        """
        # 获取产品
        db_product = await self.get_product_by_id(db, product_id)
        if not db_product:
            return False

        # 删除产品
        await db.delete(db_product)
        await db.commit()
        return True


# 创建产品服务实例
product_service = ProductService()