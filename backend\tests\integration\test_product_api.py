"""
产品API集成测试
"""
import pytest
from fastapi import status
from httpx import AsyncClient

from app.core.config import settings
from app.schemas.product import ProductCreate


@pytest.mark.asyncio
async def test_get_products(client: AsyncClient, admin_token_headers):
    """测试获取产品列表"""
    # 创建测试产品
    product_data = {
        "name": "测试产品",
        "description": "这是一个测试产品",
        "price": 100.00,
        "product_type": "software",
        "stock": 10,
    }
    response = await client.post(
        f"{settings.API_V1_PREFIX}/products/admin",
        json=product_data,
        headers=admin_token_headers,
    )
    assert response.status_code == status.HTTP_200_OK
    assert response.json()["success"] is True

    # 获取产品列表
    response = await client.get(f"{settings.API_V1_PREFIX}/products/")
    assert response.status_code == status.HTTP_200_OK
    assert response.json()["success"] is True
    assert len(response.json()["data"]) > 0
    assert response.json()["total"] > 0


@pytest.mark.asyncio
async def test_get_product_by_id(client: AsyncClient, admin_token_headers):
    """测试通过ID获取产品"""
    # 创建测试产品
    product_data = {
        "name": "测试产品详情",
        "description": "这是一个测试产品详情",
        "price": 200.00,
        "product_type": "hardware",
        "stock": 20,
    }
    response = await client.post(
        f"{settings.API_V1_PREFIX}/products/admin",
        json=product_data,
        headers=admin_token_headers,
    )
    assert response.status_code == status.HTTP_200_OK
    product_id = response.json()["data"]["id"]

    # 获取产品详情
    response = await client.get(f"{settings.API_V1_PREFIX}/products/{product_id}")
    assert response.status_code == status.HTTP_200_OK
    assert response.json()["success"] is True
    assert response.json()["data"]["id"] == product_id
    assert response.json()["data"]["name"] == "测试产品详情"
    assert response.json()["data"]["description"] == "这是一个测试产品详情"
    assert response.json()["data"]["price"] == 200.00
    assert response.json()["data"]["product_type"] == "hardware"
    assert response.json()["data"]["stock"] == 20


@pytest.mark.asyncio
async def test_get_product_not_found(client: AsyncClient):
    """测试获取不存在的产品"""
    response = await client.get(f"{settings.API_V1_PREFIX}/products/9999")
    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json()["detail"] == "产品不存在"


@pytest.mark.asyncio
async def test_create_product(client: AsyncClient, admin_token_headers):
    """测试创建产品（管理员）"""
    product_data = {
        "name": "新产品",
        "description": "这是一个新产品",
        "price": 300.00,
        "product_type": "software",
        "stock": 30,
    }
    response = await client.post(
        f"{settings.API_V1_PREFIX}/products/admin",
        json=product_data,
        headers=admin_token_headers,
    )
    assert response.status_code == status.HTTP_200_OK
    assert response.json()["success"] is True
    assert response.json()["data"]["name"] == "新产品"
    assert response.json()["data"]["description"] == "这是一个新产品"
    assert response.json()["data"]["price"] == 300.00
    assert response.json()["data"]["product_type"] == "software"
    assert response.json()["data"]["stock"] == 30
    assert response.json()["data"]["is_active"] is True


@pytest.mark.asyncio
async def test_create_product_unauthorized(client: AsyncClient):
    """测试未授权创建产品"""
    product_data = {
        "name": "未授权产品",
        "description": "这是一个未授权产品",
        "price": 400.00,
        "product_type": "software",
        "stock": 40,
    }
    response = await client.post(
        f"{settings.API_V1_PREFIX}/products/admin", json=product_data
    )
    assert response.status_code == status.HTTP_401_UNAUTHORIZED


@pytest.mark.asyncio
async def test_update_product(client: AsyncClient, admin_token_headers):
    """测试更新产品（管理员）"""
    # 创建测试产品
    product_data = {
        "name": "待更新产品",
        "description": "这是一个待更新产品",
        "price": 500.00,
        "product_type": "hardware",
        "stock": 50,
    }
    response = await client.post(
        f"{settings.API_V1_PREFIX}/products/admin",
        json=product_data,
        headers=admin_token_headers,
    )
    assert response.status_code == status.HTTP_200_OK
    product_id = response.json()["data"]["id"]

    # 更新产品
    update_data = {
        "name": "已更新产品",
        "description": "这是一个已更新产品",
        "price": 600.00,
        "stock": 60,
    }
    response = await client.put(
        f"{settings.API_V1_PREFIX}/products/admin/{product_id}",
        json=update_data,
        headers=admin_token_headers,
    )
    assert response.status_code == status.HTTP_200_OK
    assert response.json()["success"] is True
    assert response.json()["data"]["id"] == product_id
    assert response.json()["data"]["name"] == "已更新产品"
    assert response.json()["data"]["description"] == "这是一个已更新产品"
    assert response.json()["data"]["price"] == 600.00
    assert response.json()["data"]["product_type"] == "hardware"  # 未更新
    assert response.json()["data"]["stock"] == 60


@pytest.mark.asyncio
async def test_toggle_product_status(client: AsyncClient, admin_token_headers):
    """测试切换产品状态（管理员）"""
    # 创建测试产品
    product_data = {
        "name": "状态切换产品",
        "description": "这是一个状态切换产品",
        "price": 700.00,
        "product_type": "software",
        "stock": 70,
    }
    response = await client.post(
        f"{settings.API_V1_PREFIX}/products/admin",
        json=product_data,
        headers=admin_token_headers,
    )
    assert response.status_code == status.HTTP_200_OK
    product_id = response.json()["data"]["id"]
    assert response.json()["data"]["is_active"] is True

    # 禁用产品
    response = await client.patch(
        f"{settings.API_V1_PREFIX}/products/admin/{product_id}/status",
        params={"is_active": False},
        headers=admin_token_headers,
    )
    assert response.status_code == status.HTTP_200_OK
    assert response.json()["success"] is True
    assert response.json()["data"]["id"] == product_id
    assert response.json()["data"]["is_active"] is False

    # 启用产品
    response = await client.patch(
        f"{settings.API_V1_PREFIX}/products/admin/{product_id}/status",
        params={"is_active": True},
        headers=admin_token_headers,
    )
    assert response.status_code == status.HTTP_200_OK
    assert response.json()["success"] is True
    assert response.json()["data"]["id"] == product_id
    assert response.json()["data"]["is_active"] is True
