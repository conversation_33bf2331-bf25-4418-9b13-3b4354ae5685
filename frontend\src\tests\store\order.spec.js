import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useOrderStore } from '../../store/order'
import api from '../../api'

// 模拟API
vi.mock('../../api', () => ({
  default: {
    order: {
      createOrder: vi.fn(),
      getOrders: vi.fn(),
      getOrderById: vi.fn()
    },
    payment: {
      initiatePayment: vi.fn(),
      checkPaymentStatus: vi.fn(),
      getLicenseKeys: vi.fn(),
      mockPaymentSuccess: vi.fn()
    },
    get: vi.fn(),
    post: vi.fn()
  }
}))

describe('Order Store', () => {
  let store
  
  beforeEach(() => {
    // 创建一个新的Pinia实例
    setActivePinia(createPinia())
    
    // 获取store实例
    store = useOrderStore()
    
    // 重置mock
    vi.resetAllMocks()
  })
  
  describe('createOrder', () => {
    it('creates an order successfully', async () => {
      // 模拟API响应
      const mockResponse = {
        data: {
          id: 1,
          order_number: 'ORD-**********',
          status: 'pending',
          total_amount: 100,
          created_at: '2023-11-21T12:34:56',
          items: [
            {
              id: 1,
              product_id: 1,
              price: 100,
              quantity: 1
            }
          ]
        }
      }
      api.order.createOrder.mockResolvedValue(mockResponse)
      
      // 调用createOrder方法
      const orderData = {
        items: [
          {
            product_id: 1,
            quantity: 1
          }
        ]
      }
      const result = await store.createOrder(orderData)
      
      // 验证API是否被正确调用
      expect(api.order.createOrder).toHaveBeenCalledWith(orderData)
      
      // 验证返回结果
      expect(result.success).toBe(true)
      expect(result.order).toEqual(mockResponse.data)
      
      // 验证store状态
      expect(store.currentOrder).toEqual(mockResponse.data)
      expect(store.loading).toBe(false)
      expect(store.error).toBe(null)
    })
    
    it('handles error when creating an order', async () => {
      // 模拟API错误
      const mockError = {
        response: {
          data: {
            detail: '创建订单失败'
          }
        }
      }
      api.order.createOrder.mockRejectedValue(mockError)
      
      // 调用createOrder方法
      const orderData = {
        items: [
          {
            product_id: 1,
            quantity: 1
          }
        ]
      }
      const result = await store.createOrder(orderData)
      
      // 验证API是否被正确调用
      expect(api.order.createOrder).toHaveBeenCalledWith(orderData)
      
      // 验证返回结果
      expect(result.success).toBe(false)
      expect(result.message).toBe('创建订单失败')
      
      // 验证store状态
      expect(store.loading).toBe(false)
      expect(store.error).toBe('创建订单失败')
    })
  })
  
  describe('fetchOrders', () => {
    it('fetches orders successfully', async () => {
      // 模拟API响应
      const mockResponse = {
        data: [
          {
            id: 1,
            order_number: 'ORD-**********',
            status: 'pending',
            total_amount: 100,
            created_at: '2023-11-21T12:34:56'
          },
          {
            id: 2,
            order_number: 'ORD-2345678901',
            status: 'paid',
            total_amount: 200,
            created_at: '2023-11-22T12:34:56'
          }
        ]
      }
      api.order.getOrders.mockResolvedValue(mockResponse)
      
      // 调用fetchOrders方法
      const result = await store.fetchOrders()
      
      // 验证API是否被正确调用
      expect(api.order.getOrders).toHaveBeenCalled()
      
      // 验证返回结果
      expect(result.success).toBe(true)
      
      // 验证store状态
      expect(store.orders).toEqual(mockResponse.data)
      expect(store.loading).toBe(false)
      expect(store.error).toBe(null)
    })
    
    it('handles error when fetching orders', async () => {
      // 模拟API错误
      const mockError = {
        response: {
          data: {
            detail: '获取订单列表失败'
          }
        }
      }
      api.order.getOrders.mockRejectedValue(mockError)
      
      // 调用fetchOrders方法
      const result = await store.fetchOrders()
      
      // 验证API是否被正确调用
      expect(api.order.getOrders).toHaveBeenCalled()
      
      // 验证返回结果
      expect(result.success).toBe(false)
      expect(result.message).toBe('获取订单列表失败')
      
      // 验证store状态
      expect(store.loading).toBe(false)
      expect(store.error).toBe('获取订单列表失败')
    })
  })
  
  describe('fetchOrderById', () => {
    it('fetches order by id successfully', async () => {
      // 模拟API响应
      const mockResponse = {
        data: {
          id: 1,
          order_number: 'ORD-**********',
          status: 'pending',
          total_amount: 100,
          created_at: '2023-11-21T12:34:56'
        }
      }
      api.order.getOrderById.mockResolvedValue(mockResponse)
      
      // 调用fetchOrderById方法
      const result = await store.fetchOrderById(1)
      
      // 验证API是否被正确调用
      expect(api.order.getOrderById).toHaveBeenCalledWith(1)
      
      // 验证返回结果
      expect(result.success).toBe(true)
      expect(result.order).toEqual(mockResponse.data)
      
      // 验证store状态
      expect(store.currentOrder).toEqual(mockResponse.data)
      expect(store.loading).toBe(false)
      expect(store.error).toBe(null)
    })
  })
  
  describe('initiatePayment', () => {
    it('initiates payment successfully', async () => {
      // 模拟API响应
      const mockResponse = {
        data: {
          payment_url: 'https://example.com/pay'
        }
      }
      api.payment.initiatePayment.mockResolvedValue(mockResponse)
      
      // 调用initiatePayment方法
      const result = await store.initiatePayment(1)
      
      // 验证API是否被正确调用
      expect(api.payment.initiatePayment).toHaveBeenCalledWith(1)
      
      // 验证返回结果
      expect(result.success).toBe(true)
      expect(result.paymentUrl).toBe('https://example.com/pay')
      
      // 验证store状态
      expect(store.loading).toBe(false)
      expect(store.error).toBe(null)
    })
  })
  
  describe('checkPaymentStatus', () => {
    it('checks payment status successfully', async () => {
      // 模拟API响应
      const mockResponse = {
        data: {
          status: 'paid',
          payment_id: '202311212200147398**********',
          payment_time: '2023-11-21T12:34:56'
        }
      }
      api.payment.checkPaymentStatus.mockResolvedValue(mockResponse)
      
      // 设置当前订单
      store.currentOrder = {
        id: 1,
        status: 'pending'
      }
      
      // 设置订单列表
      store.orders = [
        {
          id: 1,
          status: 'pending'
        }
      ]
      
      // 调用checkPaymentStatus方法
      const result = await store.checkPaymentStatus(1)
      
      // 验证API是否被正确调用
      expect(api.payment.checkPaymentStatus).toHaveBeenCalledWith(1)
      
      // 验证返回结果
      expect(result.success).toBe(true)
      expect(result.status).toEqual(mockResponse.data)
      
      // 验证store状态
      expect(store.currentOrder.status).toBe('paid')
      expect(store.orders[0].status).toBe('paid')
      expect(store.loading).toBe(false)
      expect(store.error).toBe(null)
    })
  })
  
  describe('fetchLicenseKeys', () => {
    it('fetches license keys successfully', async () => {
      // 模拟API响应
      const mockResponse = {
        data: [
          {
            id: 1,
            user_id: 1,
            product_id: 1,
            order_id: 1,
            license_key: 'LICENSE-**********',
            is_active: true
          }
        ]
      }
      api.payment.getLicenseKeys.mockResolvedValue(mockResponse)
      
      // 调用fetchLicenseKeys方法
      const result = await store.fetchLicenseKeys()
      
      // 验证API是否被正确调用
      expect(api.payment.getLicenseKeys).toHaveBeenCalled()
      
      // 验证返回结果
      expect(result.success).toBe(true)
      
      // 验证store状态
      expect(store.licenseKeys).toEqual(mockResponse.data)
      expect(store.loading).toBe(false)
      expect(store.error).toBe(null)
    })
  })
  
  describe('mockPaymentSuccess', () => {
    it('mocks payment success successfully', async () => {
      // 模拟API响应
      const mockResponse = {
        data: {
          success: true
        }
      }
      api.payment.mockPaymentSuccess.mockResolvedValue(mockResponse)
      
      // 调用mockPaymentSuccess方法
      const result = await store.mockPaymentSuccess(1)
      
      // 验证API是否被正确调用
      expect(api.payment.mockPaymentSuccess).toHaveBeenCalledWith(1)
      
      // 验证返回结果
      expect(result.success).toBe(true)
      expect(result.result).toEqual(mockResponse.data)
      
      // 验证store状态
      expect(store.loading).toBe(false)
      expect(store.error).toBe(null)
    })
  })
})
