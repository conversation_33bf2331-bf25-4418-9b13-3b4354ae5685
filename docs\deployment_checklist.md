# 生产环境部署检查清单

## 1. 数据库脚本与代码一致性检查

### ✅ 已验证的一致性
- **用户表 (users)**: 数据库脚本与 User 模型完全一致
- **产品表 (products)**: 数据库脚本与 Product 模型完全一致
- **订单表 (orders)**: 数据库脚本与 Order 模型完全一致
- **订单项表 (order_items)**: 数据库脚本与 OrderItem 模型完全一致
- **支付记录表 (payments)**: 数据库脚本与 Payment 模型完全一致
- **许可密钥表 (license_keys)**: 数据库脚本与 LicenseKey 模型完全一致
- **站点信息表 (site_info)**: 数据库脚本与 SiteInfo 模型完全一致

### ⚠️ 发现的问题

#### 1. 数据类型不一致
- **Product.price**: 模型使用 `Float`，数据库使用 `DECIMAL(10, 2)`
- **Order.total_amount**: 模型使用 `Float(precision=2)`，数据库使用 `DECIMAL(10, 2)`
- **OrderItem.price**: 模型使用 `Float(precision=2)`，数据库使用 `DECIMAL(10, 2)`
- **Payment.amount**: 模型使用 `Float(precision=2)`，数据库使用 `DECIMAL(10, 2)`

**建议**: 统一使用 `DECIMAL` 类型处理金额，避免浮点数精度问题

#### 2. 字段长度限制
- **User.username**: 数据库限制 50 字符，模型未指定长度
- **User.email**: 数据库限制 100 字符，模型未指定长度
- **Product.name**: 数据库限制 100 字符，模型指定 100 字符 ✅
- **Order.order_number**: 数据库限制 50 字符，模型指定 50 字符 ✅

## 2. 生产环境配置问题

### ❌ 严重安全问题

#### 1. 硬编码的敏感信息
```yaml
# docker-compose.yml 中的问题
SECRET_KEY: "2088721067958003"  # 硬编码密钥
POSTGRES_PASSWORD: password     # 弱密码
ALIPAY_APP_ID: "2021000148685433"  # 硬编码支付宝配置
```

#### 2. 生产环境配置不完整
- `docker-compose.prod.yml` 配置不完整
- 缺少环境变量文件 `.env`
- 缺少生产环境的安全配置

### ⚠️ 配置问题

#### 1. 端口冲突
```yaml
# docker-compose.prod.yml 中的问题
backend:
  ports:
    - "80:8000"  # 后端使用 80 端口
frontend:
  ports:
    - "80:80"    # 前端也使用 80 端口 - 冲突！
```

#### 2. 数据库暴露
```yaml
# docker-compose.yml 开发环境暴露数据库端口
database:
  ports:
    - "5432:5432"  # 生产环境不应暴露
```

## 3. 安全检查

### ❌ 需要修复的安全问题

1. **密钥管理**
   - 私钥文件直接存储在代码仓库中
   - 缺少密钥轮换机制
   - 没有使用密钥管理服务

2. **认证安全**
   - JWT 密钥硬编码
   - 默认管理员密码过于简单
   - 缺少密码复杂度验证

3. **数据库安全**
   - 数据库密码过于简单
   - 缺少数据库连接加密
   - 没有配置数据库访问限制

## 4. 性能优化建议

### 数据库优化
1. 添加必要的索引
2. 配置连接池
3. 启用查询缓存

### 应用优化
1. 启用 Gzip 压缩
2. 配置静态文件缓存
3. 实现 API 限流

## 5. 监控和日志

### ❌ 缺少的监控配置
1. 应用性能监控 (APM)
2. 错误日志收集
3. 健康检查端点
4. 指标收集和告警

## 6. 备份和恢复

### ❌ 缺少的备份策略
1. 数据库自动备份
2. 配置文件备份
3. 密钥文件备份
4. 灾难恢复计划

## 7. 部署前必须修复的问题

### 高优先级 (必须修复)
1. 修复数据类型不一致问题
2. 创建生产环境配置文件
3. 实现密钥管理
4. 修复端口冲突
5. 加强密码安全

### 中优先级 (建议修复)
1. 添加数据库索引
2. 配置监控和日志
3. 实现备份策略
4. 优化 Docker 配置

### 低优先级 (可后续优化)
1. 性能优化
2. 缓存配置
3. CDN 配置
4. 负载均衡

## 8. 部署前必须完成的修复

### 🔴 立即修复 (阻塞部署)

1. **修复数据类型不一致**
   ```sql
   -- 在 database/init/02_production_fixes.sql 中已提供修复脚本
   -- 将 Float 类型改为 DECIMAL 类型处理金额
   ```

2. **创建生产环境配置**
   ```bash
   # 复制环境变量模板
   cp .env.example .env
   # 填入生产环境的实际值
   ```

3. **修复 Docker 配置端口冲突**
   ```yaml
   # 已在 docker-compose.prod.yml 中修复
   # 使用 Nginx 反向代理解决端口冲突
   ```

4. **加强安全配置**
   - 更改默认密码
   - 生成强随机密钥
   - 配置 HTTPS

### 🟡 建议修复 (影响安全性)

1. **密钥管理**
   - 将私钥移出代码仓库
   - 使用环境变量或密钥管理服务
   - 实现密钥轮换

2. **数据库安全**
   - 使用强密码
   - 启用连接加密
   - 限制数据库访问

## 9. 部署步骤

### 准备阶段
1. **环境准备**
   ```bash
   # 安装 Docker 和 Docker Compose
   # 克隆代码仓库
   git clone <repository-url>
   cd sales-platform
   ```

2. **配置环境变量**
   ```bash
   # 复制并编辑环境变量
   cp .env.example .env
   # 编辑 .env 文件，填入生产环境配置
   ```

3. **准备密钥文件**
   ```bash
   # 将生产环境的密钥文件放入 backend/keys/ 目录
   # 确保文件权限正确 (600)
   chmod 600 backend/keys/*.pem
   ```

### 部署阶段
1. **执行部署脚本**
   ```bash
   # 给脚本执行权限
   chmod +x scripts/deploy.sh

   # 执行部署
   ./scripts/deploy.sh
   ```

2. **验证部署**
   ```bash
   # 检查服务状态
   docker-compose -f docker-compose.prod.yml ps

   # 检查日志
   docker-compose -f docker-compose.prod.yml logs -f
   ```

### 验证阶段
1. **功能验证**
   - 访问前端页面
   - 测试用户注册/登录
   - 测试产品浏览和购买
   - 测试支付功能

2. **性能验证**
   - 检查响应时间
   - 验证数据库连接
   - 测试并发访问

## 10. 部署后配置

### 监控配置
1. **启用监控服务**
   ```bash
   # 如果需要监控，取消注释 docker-compose.prod.yml 中的监控服务
   # 访问 http://your-domain:3000 配置 Grafana
   ```

2. **配置告警**
   - 设置磁盘空间告警
   - 设置服务健康检查告警
   - 配置错误日志告警

### 备份配置
1. **自动备份**
   ```bash
   # 配置定时备份任务
   crontab -e
   # 添加: 0 2 * * * /path/to/scripts/backup_database.sh
   ```

2. **备份验证**
   ```bash
   # 定期验证备份文件完整性
   ./scripts/verify_backup.sh
   ```

## 11. 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 查看详细日志
   docker-compose -f docker-compose.prod.yml logs service-name

   # 检查配置文件
   docker-compose -f docker-compose.prod.yml config
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker exec sales_platform_database pg_isready

   # 检查连接字符串
   echo $DATABASE_URL
   ```

3. **支付功能异常**
   ```bash
   # 检查支付宝配置
   # 验证密钥文件路径和权限
   # 检查回调地址配置
   ```

### 回滚步骤
1. **快速回滚**
   ```bash
   # 停止当前服务
   docker-compose -f docker-compose.prod.yml down

   # 恢复备份
   ./scripts/restore_database.sh backup-file.sql

   # 启动之前的版本
   docker-compose -f docker-compose.prod.yml up -d
   ```

## 12. 维护建议

### 定期维护
1. **每日检查**
   - 服务健康状态
   - 磁盘空间使用
   - 错误日志

2. **每周维护**
   - 数据库备份验证
   - 安全更新检查
   - 性能指标分析

3. **每月维护**
   - 密钥轮换
   - 依赖更新
   - 安全审计

### 扩展建议
1. **负载均衡**
   - 使用 Nginx 负载均衡
   - 配置多个后端实例
   - 实现会话粘性

2. **缓存优化**
   - 启用 Redis 缓存
   - 配置 CDN
   - 优化数据库查询

3. **安全加固**
   - 启用 WAF
   - 配置 DDoS 防护
   - 实现 API 限流
