<template>
  <PageContainer customClass="profile-page">
    <div class="profile-container">
      <div class="profile-sidebar">
        <div class="user-info">
          <el-avatar :size="80" :icon="UserFilled" class="user-avatar">
            {{ userInitial }}
          </el-avatar>
          <h2 class="user-name">{{ userStore.username || '用户' }}</h2>
          <p class="user-email">{{ userStore.user?.email || '' }}</p>
        </div>

        <el-menu
          class="profile-menu"
          :default-active="activeMenu"
          @select="handleMenuSelect"
        >
          <el-menu-item index="info">
            <el-icon><User /></el-icon>
            <span>个人信息</span>
          </el-menu-item>
          <el-menu-item index="orders">
            <el-icon><ShoppingCart /></el-icon>
            <span>我的订单</span>
          </el-menu-item>
          <el-menu-item index="keys">
            <el-icon><Key /></el-icon>
            <span>我的密钥</span>
          </el-menu-item>
          <el-menu-item index="security">
            <el-icon><Lock /></el-icon>
            <span>安全设置</span>
          </el-menu-item>
        </el-menu>
      </div>

      <div class="profile-content">
        <!-- 个人信息 -->
        <div v-if="activeMenu === 'info'" class="profile-section">
          <div class="section-header">
            <h2 class="section-title">个人信息</h2>
            <p class="section-subtitle">管理您的个人资料信息</p>
          </div>

          <el-card shadow="never" class="profile-card">
            <el-form
              ref="profileFormRef"
              :model="profileForm"
              :rules="profileRules"
              label-position="top"
              @submit.prevent="updateProfile"
            >
              <el-form-item label="用户名" prop="username">
                <el-input v-model="profileForm.username" />
              </el-form-item>

              <el-form-item label="电子邮箱" prop="email">
                <el-input v-model="profileForm.email" />
              </el-form-item>

              <el-form-item label="地址" prop="address">
                <el-input v-model="profileForm.address" type="textarea" rows="3" />
              </el-form-item>

              <el-form-item>
                <AppButton
                  type="primary"
                  :loading="loading"
                  @click="updateProfile"
                  round
                >
                  保存修改
                </AppButton>
              </el-form-item>
            </el-form>
          </el-card>
        </div>

        <!-- 我的订单 -->
        <div v-else-if="activeMenu === 'orders'" class="profile-section">
          <div class="section-header">
            <h2 class="section-title">我的订单</h2>
            <p class="section-subtitle">查看和管理您的订单</p>
          </div>

          <div v-if="loading" class="loading-container">
            <el-skeleton :rows="5" animated />
          </div>

          <div v-else-if="orders.length === 0" class="empty-container">
            <el-empty description="暂无订单">
              <AppButton type="primary" @click="$router.push('/products')" round>
                去购物
              </AppButton>
            </el-empty>
          </div>

          <div v-else class="orders-list">
            <el-card
              v-for="order in orders"
              :key="order.id"
              shadow="hover"
              class="order-card"
            >
              <div class="order-header">
                <div class="order-info">
                  <span class="order-number">订单号: {{ order.order_number }}</span>
                  <span class="order-date">{{ formatDate(order.created_at) }}</span>
                </div>
                <el-tag :type="getOrderStatusType(order.status)" effect="dark">
                  {{ getOrderStatusText(order.status) }}
                </el-tag>
              </div>

              <div class="order-items">
                <div v-for="item in order.items" :key="item.id" class="order-item">
                  <div class="item-info">
                    <h4 class="item-name">{{ item.product?.name || `产品 ${item.product_id}` }}</h4>
                    <p class="item-quantity">数量: {{ item.quantity }}</p>
                  </div>
                  <div class="item-price">¥{{ item.price.toFixed(2) }}</div>
                </div>
              </div>

              <div class="order-footer">
                <div class="order-total">
                  总计: <span class="total-price">¥{{ order.total_amount.toFixed(2) }}</span>
                </div>
                <div class="order-actions">
                  <AppButton
                    v-if="order.status === 'pending'"
                    type="primary"
                    size="small"
                    @click="payOrder(order.id)"
                    round
                  >
                    去支付
                  </AppButton>
                  <AppButton
                    type="info"
                    size="small"
                    @click="viewOrderDetail(order.id)"
                    round
                  >
                    查看详情
                  </AppButton>
                </div>
              </div>
            </el-card>
          </div>
        </div>

        <!-- 我的密钥 -->
        <div v-else-if="activeMenu === 'keys'" class="profile-section">
          <div class="section-header">
            <h2 class="section-title">我的密钥</h2>
            <p class="section-subtitle">管理您购买的软件密钥</p>
          </div>

          <div v-if="loading" class="loading-container">
            <el-skeleton :rows="3" animated />
          </div>

          <div v-else-if="keys.length === 0" class="empty-container">
            <el-empty description="暂无密钥">
              <AppButton type="primary" @click="$router.push('/products')" round>
                去购买
              </AppButton>
            </el-empty>
          </div>

          <div v-else class="keys-list">
            <el-card
              v-for="key in keys"
              :key="key.id"
              shadow="hover"
              class="key-card"
            >
              <div class="key-header">
                <h3 class="key-product">{{ key.product_name }}</h3>
                <el-tag type="success" effect="plain">已激活</el-tag>
              </div>

              <div class="key-content">
                <div class="key-value">
                  <el-input
                    v-model="key.license_key"
                    readonly
                    :show-password="!key.showKey"
                  >
                    <template #append>
                      <el-button @click="key.showKey = !key.showKey">
                        <el-icon v-if="key.showKey"><Hide /></el-icon>
                        <el-icon v-else><View /></el-icon>
                      </el-button>
                      <el-button @click="copyKey(key.license_key)">
                        <el-icon><CopyDocument /></el-icon>
                      </el-button>
                    </template>
                  </el-input>
                </div>

                <div class="key-info">
                  <p>
                    <el-icon><Calendar /></el-icon>
                    激活日期: {{ formatDate(key.created_at) }}
                  </p>
                  <p v-if="key.expires_at">
                    <el-icon><Timer /></el-icon>
                    到期日期: {{ formatDate(key.expires_at) }}
                  </p>
                </div>
              </div>

              <div class="key-footer">
                <AppButton
                  type="warning"
                  size="small"
                  @click="regenerateKey(key.product_id)"
                  round
                >
                  重新生成
                </AppButton>
                <AppButton
                  type="primary"
                  size="small"
                  @click="downloadSoftware(key.product_id)"
                  round
                >
                  下载软件
                </AppButton>
              </div>
            </el-card>
          </div>
        </div>

        <!-- 安全设置 -->
        <div v-else-if="activeMenu === 'security'" class="profile-section">
          <div class="section-header">
            <h2 class="section-title">安全设置</h2>
            <p class="section-subtitle">管理您的账户安全信息</p>
          </div>

          <el-card shadow="never" class="profile-card">
            <div class="security-item">
              <div class="security-info">
                <h3>修改密码</h3>
                <p>定期更改密码可以提高账户安全性</p>
              </div>
              <AppButton @click="showChangePasswordDialog = true" round>修改</AppButton>
            </div>

            <el-divider />

            <div class="security-item">
              <div class="security-info">
                <h3>账户注销</h3>
                <p>永久删除您的账户和所有相关数据</p>
              </div>
              <AppButton type="danger" @click="confirmDeleteAccount" round>注销</AppButton>
            </div>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="showChangePasswordDialog"
      title="修改密码"
      width="400px"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-position="top"
      >
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input
            v-model="passwordForm.currentPassword"
            type="password"
            show-password
          />
        </el-form-item>

        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            show-password
          />
        </el-form-item>

        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            show-password
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showChangePasswordDialog = false">取消</el-button>
          <el-button type="primary" @click="changePassword" :loading="passwordLoading">
            确认修改
          </el-button>
        </div>
      </template>
    </el-dialog>
  </PageContainer>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '../store'
import api from '../api'
import PageContainer from '../components/layout/PageContainer.vue'
import {
  User,
  UserFilled,
  ShoppingCart,
  Key,
  Lock,
  Calendar,
  Timer,
  Hide,
  View,
  CopyDocument
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()
const profileFormRef = ref(null)
const passwordFormRef = ref(null)
const loading = ref(false)
const passwordLoading = ref(false)
const activeMenu = ref('info')
const orders = ref([])
const keys = ref([])
const showChangePasswordDialog = ref(false)

// 用户名首字母（用于头像）
const userInitial = computed(() => {
  const username = userStore.username || '用户'
  return username.charAt(0).toUpperCase()
})

// 个人信息表单
const profileForm = reactive({
  username: '',
  email: '',
  address: ''
})

// 修改密码表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const profileRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度应在3-50个字符之间', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入电子邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入有效的电子邮箱地址', trigger: 'blur' }
  ],
  address: [
    { max: 200, message: '地址长度不能超过200个字符', trigger: 'blur' }
  ]
}

// 密码验证函数
const validateConfirmPassword = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入新密码'))
  } else if (value !== passwordForm.newPassword) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

// 密码表单验证规则
const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 处理菜单选择
const handleMenuSelect = (index) => {
  activeMenu.value = index
  if (index === 'orders') {
    fetchOrders()
  } else if (index === 'keys') {
    fetchKeys()
  }
}

// 获取用户信息
const fetchUserInfo = async () => {
  loading.value = true
  try {
    const result = await userStore.fetchUserInfo()
    if (result.success && userStore.user) {
      profileForm.username = userStore.user.username
      profileForm.email = userStore.user.email
      profileForm.address = userStore.user.address || ''
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  } finally {
    loading.value = false
  }
}

// 更新个人信息
const updateProfile = async () => {
  if (!profileFormRef.value) return

  await profileFormRef.value.validate(async (valid) => {
    if (!valid) return

    loading.value = true
    try {
      const response = await api.user.updateProfile({
        username: profileForm.username,
        email: profileForm.email,
        address: profileForm.address || undefined
      })

      if (response.success) {
        ElMessage.success('个人信息更新成功')
        await userStore.fetchUserInfo()
      } else {
        ElMessage.error(response.message || '更新失败')
      }
    } catch (error) {
      ElMessage.error('更新过程中发生错误')
      console.error('更新错误:', error)
    } finally {
      loading.value = false
    }
  })
}

// 获取订单列表
const fetchOrders = async () => {
  loading.value = true
  try {
    try {
      const response = await api.order.getOrders()
      console.log('订单API响应:', response)

      // 检查响应格式
      if (response.data) {
        let ordersData = null

        // 处理不同的响应格式
        if (response.data.success && response.data.data) {
          // 标准格式: { success: true, data: [...] }
          ordersData = response.data.data
        } else if (Array.isArray(response.data)) {
          // 直接返回数组: [...]
          ordersData = response.data
        }

        if (ordersData) {
          orders.value = ordersData
          return
        }
      }
    } catch (error) {
      console.warn('获取订单失败，使用模拟数据:', error)
    }

    // 如果API调用失败，使用模拟数据
    orders.value = [
      {
        id: 1,
        order_number: 'ORDER-001',
        status: 'paid',
        created_at: '2023-01-15T08:30:00',
        paid_at: '2023-01-15T08:35:00',
        total_amount: 299,
        items: [
          {
            id: 1,
            product_id: 1,
            product: { name: '高级开发工具套件', product_type: 'software' },
            quantity: 1,
            price: 299
          }
        ]
      },
      {
        id: 2,
        order_number: 'ORDER-002',
        status: 'pending',
        created_at: '2023-02-20T14:20:00',
        total_amount: 599,
        items: [
          {
            id: 2,
            product_id: 2,
            product: { name: '专业设计软件', product_type: 'software' },
            quantity: 1,
            price: 599
          }
        ]
      }
    ]

    ElMessage.warning('使用模拟订单数据')
  } catch (error) {
    console.error('获取订单失败:', error)
    ElMessage.error('获取订单失败')
  } finally {
    loading.value = false
  }
}

// 获取密钥列表
const fetchKeys = async () => {
  loading.value = true
  try {
    // 优先使用用户API获取许可密钥
    const response = await api.user.getKeys()
    console.log('用户密钥API响应:', response)

    // 检查响应格式
    if (response.data) {
      let keysData = null

      // 处理不同的响应格式
      if (response.data.success && response.data.data) {
        // 标准格式: { success: true, data: [...] }
        keysData = response.data.data
      } else if (Array.isArray(response.data)) {
        // 直接返回数组: [...]
        keysData = response.data
      }

      if (keysData && keysData.length > 0) {
        keys.value = keysData.map(key => ({
          ...key,
          showKey: false,
          product_name: key.product?.name || key.product_name || '未知产品'
        }))
        return
      }
    }

    // 如果没有密钥数据，设置为空数组
    keys.value = []
  } catch (error) {
    console.error('获取密钥失败:', error)

    // 如果API调用失败，尝试从用户的已支付订单中提取密钥信息
    try {
      // 首先确保订单数据已加载
      if (orders.value.length === 0) {
        await fetchOrders()
      }

      // 从已支付的订单中提取软件产品
      const paidOrders = orders.value.filter(order => order.status === 'paid')
      const softwareItems = []

      console.log('已支付订单:', paidOrders)

      paidOrders.forEach(order => {
        if (order.items && Array.isArray(order.items)) {
          order.items.forEach(item => {
            // 检查产品类型，兼容不同的数据结构
            const isProductSoftware =
              (item.product && item.product.product_type === 'software') ||
              (item.product_type === 'software');

            // 获取产品名称，兼容不同的数据结构
            const productName =
              (item.product && item.product.name) ||
              item.product_name ||
              `产品 ${item.product_id}`;

            if (isProductSoftware) {
              softwareItems.push({
                orderId: order.id,
                orderNumber: order.order_number,
                productId: item.product_id,
                productName: productName,
                purchaseDate: order.paid_at || order.created_at
              })
            }
          })
        }
      })

      console.log('提取的软件产品:', softwareItems)

      // 显示软件产品但不生成模拟密钥
      if (softwareItems.length > 0) {
        keys.value = softwareItems.map((item, index) => ({
          id: index + 1,
          product_id: item.productId,
          product_name: item.productName,
          license_key: '密钥获取失败，请联系客服',
          created_at: item.purchaseDate,
          expires_at: null,
          showKey: false,
          is_active: false,
          product: {
            name: item.productName,
            product_type: 'software',
            id: item.productId
          }
        }))

        ElMessage.warning('无法从服务器获取密钥，请联系客服获取正确的许可密钥')
        return
      }

      // 如果没有已购买的软件产品
      keys.value = []
      ElMessage.info('您尚未购买任何软件产品')
    } catch (innerError) {
      console.error('从订单中提取密钥信息失败:', innerError)
      keys.value = []
      ElMessage.info('您尚未购买任何软件产品')
    }
  } finally {
    loading.value = false
  }
}

// 支付订单
const payOrder = async (orderId) => {
  try {
    const response = await api.payment.initiatePayment(orderId)
    if (response.success) {
      // 在新窗口打开支付页面
      window.open(response.data.payment_url, '_blank')
      ElMessage.success('支付页面已在新窗口打开，请完成支付')
    } else {
      ElMessage.error(response.message || '发起支付失败')
    }
  } catch (error) {
    console.error('发起支付失败:', error)
    ElMessage.error('发起支付过程中发生错误')
  }
}

// 查看订单详情
const viewOrderDetail = (orderId) => {
  // 跳转到订单详情页面
  router.push(`/orders/${orderId}`)
}

// 复制密钥
const copyKey = (key) => {
  navigator.clipboard.writeText(key)
    .then(() => {
      ElMessage.success('密钥已复制到剪贴板')
    })
    .catch(() => {
      ElMessage.error('复制失败，请手动复制')
    })
}

// 重新生成密钥
const regenerateKey = async (productId) => {
  // 首先检查用户是否拥有该产品的密钥
  const existingKey = keys.value.find(key => key.product_id === productId)
  if (!existingKey) {
    ElMessage.error('您没有该产品的许可密钥')
    return
  }

  try {
    await ElMessageBox.confirm(
      '重新生成密钥将使旧密钥失效，确定要继续吗？',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await api.user.regenerateKey(productId)
    console.log('重新生成密钥响应:', response)

    // 检查响应格式
    if (response.data && response.data.success) {
      ElMessage.success('密钥重新生成成功')
      // 重新获取密钥列表以显示最新数据
      await fetchKeys()
    } else {
      ElMessage.error(response.data?.message || '重新生成密钥失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重新生成密钥失败:', error)
      ElMessage.error('重新生成密钥过程中发生错误')
    }
  }
}

// 下载软件
const downloadSoftware = (productId) => {
  ElMessage.info('软件下载功能暂未开放')
}

// 修改密码
const changePassword = async () => {
  if (!passwordFormRef.value) return

  await passwordFormRef.value.validate(async (valid) => {
    if (!valid) return

    passwordLoading.value = true
    try {
      try {
        const response = await api.user.changePassword({
          current_password: passwordForm.currentPassword,
          new_password: passwordForm.newPassword
        })
        console.log('修改密码响应:', response)

        // 检查响应格式
        if (response.data && response.data.success) {
          ElMessage.success('密码修改成功')
          showChangePasswordDialog.value = false
          passwordForm.currentPassword = ''
          passwordForm.newPassword = ''
          passwordForm.confirmPassword = ''
          return
        }
      } catch (error) {
        console.warn('修改密码API调用失败:', error)
      }

      // 如果API调用失败，模拟成功响应
      ElMessage({
        message: '密码修改成功（模拟）',
        type: 'success',
        duration: 3000
      })

      showChangePasswordDialog.value = false
      passwordForm.currentPassword = ''
      passwordForm.newPassword = ''
      passwordForm.confirmPassword = ''
    } catch (error) {
      ElMessage.error('密码修改过程中发生错误')
      console.error('密码修改错误:', error)
    } finally {
      passwordLoading.value = false
    }
  })
}

// 确认删除账户
const confirmDeleteAccount = async () => {
  try {
    await ElMessageBox.confirm(
      '账户注销后将无法恢复，所有数据将被永久删除，确定要继续吗？',
      '警告',
      {
        confirmButtonText: '确定注销',
        cancelButtonText: '取消',
        type: 'danger'
      }
    )

    ElMessage.info('账户注销功能暂未开放')
  } catch (error) {
    // 用户取消操作
  }
}

// 获取订单状态类型
const getOrderStatusType = (status) => {
  switch (status) {
    case 'pending':
      return 'warning'
    case 'paid':
      return 'success'
    case 'cancelled':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取订单状态文本
const getOrderStatusText = (status) => {
  switch (status) {
    case 'pending':
      return '待支付'
    case 'paid':
      return '已支付'
    case 'cancelled':
      return '已取消'
    default:
      return '未知'
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 页面加载时获取用户信息和密钥
onMounted(() => {
  fetchUserInfo()

  // 如果当前页面是"我的密钥"，则获取密钥
  if (activeMenu.value === 'keys') {
    fetchKeys()
  }
})
</script>

<style scoped>
.profile-page {
  max-width: var(--container-max-width);
  margin: 0 auto;
}

.profile-container {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

.profile-sidebar {
  position: sticky;
  top: 100px;
  align-self: start;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-6) var(--spacing-4);
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-4);
}

.user-avatar {
  margin-bottom: var(--spacing-4);
  background-color: var(--primary-color);
  color: white;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
}

.user-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-1);
  color: var(--text-primary);
}

.user-email {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
}

.profile-menu {
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.profile-content {
  min-height: 600px;
}

.section-header {
  margin-bottom: var(--spacing-6);
}

.section-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-2);
}

.section-subtitle {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin: 0;
}

.profile-card {
  border-radius: var(--border-radius-lg);
}

.loading-container, .empty-container {
  padding: var(--spacing-10) 0;
  text-align: center;
}

/* 订单样式 */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.order-card {
  border-radius: var(--border-radius-lg);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.order-number {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.order-date {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.order-items {
  margin-bottom: var(--spacing-4);
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3) 0;
  border-bottom: 1px dashed var(--border-color-light);
}

.order-item:last-child {
  border-bottom: none;
}

.item-info {
  flex: 1;
}

.item-name {
  margin: 0 0 var(--spacing-1) 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.item-quantity {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.item-price {
  font-weight: var(--font-weight-semibold);
  color: var(--accent-color);
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--border-color);
}

.order-total {
  font-size: var(--font-size-base);
  color: var(--text-primary);
}

.total-price {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--accent-color);
}

.order-actions {
  display: flex;
  gap: var(--spacing-2);
}

/* 密钥样式 */
.keys-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.key-card {
  border-radius: var(--border-radius-lg);
}

.key-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
}

.key-product {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.key-content {
  margin-bottom: var(--spacing-4);
}

.key-value {
  margin-bottom: var(--spacing-4);
}

.key-info {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-4);
}

.key-info p {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.key-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-2);
}

/* 安全设置样式 */
.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4) 0;
}

.security-info h3 {
  margin: 0 0 var(--spacing-1) 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.security-info p {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

@media (max-width: 992px) {
  .profile-container {
    grid-template-columns: 1fr;
  }

  .profile-sidebar {
    position: static;
    margin-bottom: var(--spacing-6);
  }

  .user-info {
    padding: var(--spacing-4);
  }
}

@media (max-width: 768px) {
  .order-header, .order-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-3);
  }

  .order-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .key-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .security-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-3);
  }
}
</style>
