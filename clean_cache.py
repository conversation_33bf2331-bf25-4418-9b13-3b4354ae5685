# 文件路径: clean_cache.py

import os
import shutil

def clean_cache_directories():
    """
    递归查找并删除当前目录及其所有子目录下的 "__pycache__" 和 ".pytest_cache" 文件夹。
    """
    deleted_count = 0
    # 获取当前工作目录
    current_dir = os.getcwd()

    print(f"正在清理目录: {current_dir}")

    # 遍历当前目录及其所有子目录
    for root, dirs, files in os.walk(current_dir, topdown=False):
        for dir_name in dirs:
            # 检查目录名是否是需要删除的缓存目录
            if dir_name == "__pycache__" or dir_name == ".pytest_cache":
                dir_path = os.path.join(root, dir_name)
                try:
                    print(f"正在删除: {dir_path}")
                    shutil.rmtree(dir_path)
                    deleted_count += 1
                except OSError as e:
                    print(f"删除目录 {dir_path} 失败: {e}")

    print(f"清理完成。共删除了 {deleted_count} 个目录。")

if __name__ == "__main__":
    clean_cache_directories()