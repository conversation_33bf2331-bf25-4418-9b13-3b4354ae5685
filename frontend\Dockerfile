# 使用 Node.js 镜像作为基础镜像
FROM node:18-alpine

# 设置工作目录
WORKDIR /src

# 设置环境变量
ENV NODE_ENV=development \
    TZ=Asia/Shanghai

# 安装依赖
COPY package*.json ./
RUN npm config set registry https://registry.npmmirror.com && \
    npm install

# 将应用源代码复制到工作目录
COPY . .

# 创建非root用户
RUN addgroup -S appgroup && \
    adduser -S appuser -G appgroup && \
    chown -R appuser:appgroup /src

# 切换到非root用户
USER appuser

# 暴露开发服务器端口
EXPOSE 5173

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD wget --spider http://0.0.0.0:5173 || exit 1

# 启动开发服务器
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]