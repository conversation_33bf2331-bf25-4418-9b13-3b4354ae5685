# 前端伪代码结构

## 1. API请求模块 (api/index.js)

```javascript
// API基础配置
import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
});

// 请求拦截器
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data;
  },
  error => {
    if (error.response && error.response.status === 401) {
      // 未授权，清除token并跳转到登录页
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default api;
```

## 2. API服务模块

### 2.1 用户API (api/user.js)

```javascript
import api from './index';

export const userApi = {
  // 用户注册
  register(userData) {
    return api.post('/users/', userData);
  },
  
  // 用户登录
  login(username, password) {
    const formData = new FormData();
    formData.append('username', username);
    formData.append('password', password);
    return api.post('/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
  },
  
  // 获取当前用户信息
  getCurrentUser() {
    return api.get('/users/me');
  },
  
  // 更新用户信息
  updateUserInfo(userData) {
    return api.put('/users/me', userData);
  },
  
  // 获取用户订单
  getUserOrders() {
    return api.get('/users/me/orders');
  }
};
```

### 2.2 产品API (api/product.js)

```javascript
import api from './index';

export const productApi = {
  // 获取产品列表
  getProducts(params = {}) {
    return api.get('/products/', { params });
  },
  
  // 获取单个产品
  getProduct(productId) {
    return api.get(`/products/${productId}`);
  }
};
```

### 2.3 支付API (api/payment.js)

```javascript
import api from './index';

export const paymentApi = {
  // 创建订单
  createOrder(productId) {
    return api.post('/payments/orders', { product_id: productId });
  },
  
  // 获取支付链接
  getPaymentUrl(orderId) {
    return api.get(`/payments/orders/${orderId}/pay`);
  }
};
```

### 2.4 管理员API (api/admin.js)

```javascript
import api from './index';

export const adminApi = {
  // 获取用户列表
  getUsers(params = {}) {
    return api.get('/admin/users', { params });
  },
  
  // 获取销售统计
  getStatistics() {
    return api.get('/admin/statistics');
  },
  
  // 获取所有订单
  getAllOrders(params = {}) {
    return api.get('/admin/orders', { params });
  },
  
  // 创建产品
  createProduct(productData) {
    return api.post('/admin/products', productData);
  },
  
  // 更新产品
  updateProduct(productId, productData) {
    return api.put(`/admin/products/${productId}`, productData);
  }
};
```

### 2.5 站点API (api/site.js)

```javascript
import api from './index';

export const siteApi = {
  // 获取站点信息
  getSiteInfo() {
    return api.get('/site/info');
  },
  
  // 更新站点信息
  updateSiteInfo(siteData) {
    return api.put('/site/info', siteData);
  }
};
```

## 3. Pinia状态管理

### 3.1 用户状态 (store/user.js)

```javascript
import { defineStore } from 'pinia';
import { userApi } from '@/api/user';

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    userInfo: null,
    loading: false,
    error: null
  }),
  
  getters: {
    isLoggedIn: (state) => !!state.token,
    isAdmin: (state) => state.userInfo?.role === 'admin'
  },
  
  actions: {
    // 用户登录
    async login(username, password) {
      this.loading = true;
      this.error = null;
      try {
        const response = await userApi.login(username, password);
        const { access_token } = response;
        this.token = access_token;
        localStorage.setItem('token', access_token);
        await this.fetchUserInfo();
        return true;
      } catch (error) {
        this.error = error.response?.data?.message || '登录失败';
        return false;
      } finally {
        this.loading = false;
      }
    },
    
    // 获取用户信息
    async fetchUserInfo() {
      if (!this.token) return;
      
      this.loading = true;
      try {
        const response = await userApi.getCurrentUser();
        if (response.success) {
          this.userInfo = response.data;
        }
      } catch (error) {
        this.error = error.response?.data?.message || '获取用户信息失败';
        this.logout();
      } finally {
        this.loading = false;
      }
    },
    
    // 用户注销
    logout() {
      this.token = '';
      this.userInfo = null;
      localStorage.removeItem('token');
    }
  }
});
```
