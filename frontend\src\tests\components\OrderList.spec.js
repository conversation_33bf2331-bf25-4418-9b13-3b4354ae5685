import { mount } from '@vue/test-utils'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import OrderList from '../../components/OrderList.vue'
import { useOrderStore, useUserStore } from '../../store'
import { ElMessage, ElMessageBox } from 'element-plus'

// 模拟Element Plus组件
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
  },
  ElMessageBox: {
    confirm: vi.fn(),
  },
}))

describe('OrderList.vue', () => {
  let wrapper
  let orderStore
  let userStore
  
  // 模拟订单数据
  const mockOrders = [
    {
      id: 1,
      order_number: 'ORD-1234567890',
      status: 'pending',
      total_amount: 100,
      created_at: '2023-11-21T12:34:56',
      items: [
        {
          id: 1,
          product_id: 1,
          product: { name: 'Test Product 1' },
          price: 100,
          quantity: 1
        }
      ]
    },
    {
      id: 2,
      order_number: 'ORD-2345678901',
      status: 'paid',
      total_amount: 200,
      created_at: '2023-11-22T12:34:56',
      items: [
        {
          id: 2,
          product_id: 2,
          product: { name: 'Test Product 2' },
          price: 200,
          quantity: 1
        }
      ]
    }
  ]
  
  beforeEach(() => {
    // 创建一个新的Pinia实例
    setActivePinia(createPinia())
    
    // 获取store实例
    orderStore = useOrderStore()
    userStore = useUserStore()
    
    // 模拟store方法
    orderStore.initiatePayment = vi.fn().mockResolvedValue({ success: true, paymentUrl: 'https://example.com/pay' })
    orderStore.mockPaymentSuccess = vi.fn().mockResolvedValue({ success: true })
    
    // 模拟计算属性
    vi.spyOn(userStore, 'isAdmin', 'get').mockReturnValue(false)
    
    // 模拟window.location
    delete window.location
    window.location = { href: '' }
    
    // 挂载组件
    wrapper = mount(OrderList, {
      props: {
        orders: mockOrders
      },
      global: {
        stubs: ['el-button', 'el-tag', 'el-card'],
        provide: {
          orderStore,
          userStore
        }
      }
    })
  })
  
  it('renders order list correctly', () => {
    // 验证组件是否正确渲染
    expect(wrapper.findAll('.order-card')).toHaveLength(2)
    
    // 验证订单号是否正确显示
    const orderNumbers = wrapper.findAll('.order-number .value')
    expect(orderNumbers[0].text()).toBe('ORD-1234567890')
    expect(orderNumbers[1].text()).toBe('ORD-2345678901')
    
    // 验证订单状态是否正确显示
    const orderStatuses = wrapper.findAll('.order-status')
    expect(orderStatuses[0].text()).toContain('待支付')
    expect(orderStatuses[1].text()).toContain('已支付')
    
    // 验证订单总金额是否正确显示
    const orderTotals = wrapper.findAll('.order-total .value')
    expect(orderTotals[0].text()).toBe('¥100.00')
    expect(orderTotals[1].text()).toBe('¥200.00')
  })
  
  it('shows pay button only for pending orders', () => {
    // 验证只有待支付订单才显示支付按钮
    const payButtons = wrapper.findAll('.order-actions button')
    
    // 第一个订单是待支付状态，应该有两个按钮：查看详情和去支付
    expect(payButtons.length).toBeGreaterThanOrEqual(2)
    
    // 第二个订单是已支付状态，应该只有一个按钮：查看详情
    const secondOrderButtons = wrapper.findAll('.order-card:nth-child(2) .order-actions button')
    expect(secondOrderButtons.length).toBe(1)
  })
  
  it('initiates payment when pay button is clicked', async () => {
    // 模拟点击支付按钮
    const payButton = wrapper.find('.order-actions button:nth-child(2)')
    await payButton.trigger('click')
    
    // 验证支付方法是否被调用
    expect(orderStore.initiatePayment).toHaveBeenCalledWith(1)
    
    // 验证页面是否跳转到支付链接
    expect(window.location.href).toBe('https://example.com/pay')
  })
  
  it('shows mock payment button for admin users', async () => {
    // 模拟管理员用户
    vi.spyOn(userStore, 'isAdmin', 'get').mockReturnValue(true)
    
    // 重新挂载组件
    wrapper = mount(OrderList, {
      props: {
        orders: mockOrders
      },
      global: {
        stubs: ['el-button', 'el-tag', 'el-card'],
        provide: {
          orderStore,
          userStore
        }
      }
    })
    
    // 验证模拟支付按钮是否显示
    const mockPaymentButtons = wrapper.findAll('.order-actions button')
    expect(mockPaymentButtons.length).toBeGreaterThanOrEqual(2)
    
    // 模拟确认对话框返回Promise.resolve
    ElMessageBox.confirm.mockResolvedValue()
    
    // 模拟点击模拟支付按钮
    const mockPaymentButton = wrapper.find('.order-actions button:nth-child(2)')
    await mockPaymentButton.trigger('click')
    
    // 验证确认对话框是否被调用
    expect(ElMessageBox.confirm).toHaveBeenCalled()
    
    // 验证模拟支付方法是否被调用
    expect(orderStore.mockPaymentSuccess).toHaveBeenCalledWith(1)
    
    // 验证成功消息是否被显示
    expect(ElMessage.success).toHaveBeenCalledWith('模拟支付成功')
  })
  
  it('formats date correctly', () => {
    // 获取组件实例
    const vm = wrapper.vm
    
    // 测试日期格式化方法
    const formattedDate = vm.formatDate('2023-11-21T12:34:56')
    
    // 验证格式化后的日期是否符合预期
    expect(formattedDate).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/)
  })
  
  it('emits refresh event when needed', async () => {
    // 模拟点击刷新按钮
    const refreshButton = wrapper.find('.order-list-header button')
    await refreshButton.trigger('click')
    
    // 验证refresh事件是否被触发
    expect(wrapper.emitted().refresh).toBeTruthy()
    expect(wrapper.emitted().refresh.length).toBe(1)
  })
})
