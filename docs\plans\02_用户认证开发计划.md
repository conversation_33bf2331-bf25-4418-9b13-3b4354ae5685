# 02_用户认证开发计划（2天）

## 1. 项目概述

本计划详细描述了软件和设备销售平台的用户认证模块开发，包括用户注册、登录和权限控制功能。该计划基于`docs\architecture.md`中的架构设计，确保在15天开发周期内的第2-3天完成所有必要的用户认证功能。

## 2. 用户认证任务清单

| 任务ID | 任务描述 | 预计时间 | 状态 | 负责人 |
|--------|----------|----------|------|--------|
| 2.1 | **后端用户模型与数据库设计** | 3小时 | 已完成 | - |
| 2.1.1 | 完善用户模型（User Model） | 1小时 | 已完成 | - |
| 2.1.2 | 创建数据库迁移脚本 | 30分钟 | 已完成 | - |
| 2.1.3 | 实现用户服务（User Service） | 1小时 | 已完成 | - |
| 2.1.4 | 编写用户模型单元测试 | 30分钟 | 已完成 | - |
| 2.2 | **认证与安全模块实现** | 4小时 | 已完成 | - |
| 2.2.1 | 实现密码哈希与验证功能 | 30分钟 | 已完成 | - |
| 2.2.2 | 实现JWT令牌生成与验证 | 1小时 | 已完成 | - |
| 2.2.3 | 创建认证依赖（Dependencies） | 1小时 | 已完成 | - |
| 2.2.4 | 实现权限控制（用户/管理员） | 1小时 | 已完成 | - |
| 2.2.5 | 编写安全模块单元测试 | 30分钟 | 已完成 | - |
| 2.3 | **用户API路由实现** | 4小时 | 已完成 | - |
| 2.3.1 | 实现用户注册API | 1小时 | 已完成 | - |
| 2.3.2 | 实现用户登录API | 1小时 | 已完成 | - |
| 2.3.3 | 实现获取用户信息API | 30分钟 | 已完成 | - |
| 2.3.4 | 实现更新用户信息API | 30分钟 | 已完成 | - |
| 2.3.5 | 编写API集成测试 | 1小时 | 部分完成 | - |
| 2.4 | **前端认证组件开发** | 5小时 | 已完成 | - |
| 2.4.1 | 实现登录页面组件 | 1小时 | 已完成 | - |
| 2.4.2 | 实现注册页面组件 | 1小时 | 已完成 | - |
| 2.4.3 | 实现用户状态管理（Pinia Store） | 1小时 | 已完成 | - |
| 2.4.4 | 实现路由守卫（权限控制） | 1小时 | 已完成 | - |
| 2.4.5 | 实现API请求拦截器（Token处理） | 1小时 | 已完成 | - |

## 3. 详细任务说明

### 3.1 后端用户模型与数据库设计

#### 3.1.1 完善用户模型（User Model）
- 检查现有的用户模型（`app/models/user.py`）
- 确保包含必要字段：id, username, email, password_hash, is_active, role, address, created_at
- 添加适当的索引和约束
- 确保模型与SQLAlchemy异步API兼容

#### 3.1.2 创建数据库迁移脚本
- 使用Alembic创建迁移脚本
- 确保迁移脚本能够创建用户表
- 添加初始管理员用户的种子数据

#### 3.1.3 实现用户服务（User Service）
- 实现用户创建方法（包含密码哈希）
- 实现用户查询方法（按ID、用户名、邮箱）
- 实现用户更新方法
- 实现用户认证方法（验证用户名和密码）

#### 3.1.4 编写用户模型单元测试
- 测试用户创建和查询功能
- 测试用户更新功能
- 测试用户认证功能

### 3.2 认证与安全模块实现

#### 3.2.1 实现密码哈希与验证功能
- 使用passlib库实现密码哈希
- 实现密码验证功能
- 确保密码安全存储

#### 3.2.2 实现JWT令牌生成与验证
- 使用python-jose库实现JWT令牌生成
- 实现令牌验证和解码功能
- 设置令牌过期时间

#### 3.2.3 创建认证依赖（Dependencies）
- 实现获取当前用户依赖
- 实现获取当前活跃用户依赖
- 实现获取当前管理员用户依赖

#### 3.2.4 实现权限控制（用户/管理员）
- 实现基于角色的权限控制
- 区分普通用户和管理员权限
- 确保API端点使用正确的权限依赖

#### 3.2.5 编写安全模块单元测试
- 测试密码哈希和验证功能
- 测试JWT令牌生成和验证
- 测试认证依赖功能

### 3.3 用户API路由实现

#### 3.3.1 实现用户注册API
- 创建用户注册端点（POST /users）
- 实现用户名和邮箱唯一性验证
- 实现密码强度验证
- 返回统一的响应格式

#### 3.3.2 实现用户登录API
- 创建用户登录端点（POST /auth/login）
- 实现用户认证逻辑
- 生成并返回JWT令牌
- 处理认证失败情况

#### 3.3.3 实现获取用户信息API
- 创建获取当前用户信息端点（GET /users/me）
- 使用认证依赖确保用户已登录
- 返回用户详细信息（不包含密码）

#### 3.3.4 实现更新用户信息API
- 创建更新用户信息端点（PUT /users/me）
- 允许用户更新个人信息（用户名、邮箱、地址）
- 确保更新后的用户名和邮箱仍然唯一

#### 3.3.5 编写API集成测试
- 测试用户注册流程
- 测试用户登录流程
- 测试获取和更新用户信息
- 测试权限控制功能

### 3.4 前端认证组件开发

#### 3.4.1 实现登录页面组件
- 创建登录表单组件
- 实现表单验证
- 实现登录请求和错误处理
- 登录成功后存储令牌和重定向

#### 3.4.2 实现注册页面组件
- 创建注册表单组件
- 实现表单验证（用户名、邮箱、密码）
- 实现注册请求和错误处理
- 注册成功后自动登录或重定向到登录页

#### 3.4.3 实现用户状态管理（Pinia Store）
- 创建用户状态存储
- 实现登录和注销功能
- 实现用户信息获取和更新
- 实现状态持久化（localStorage）

#### 3.4.4 实现路由守卫（权限控制）
- 配置Vue Router路由守卫
- 实现基于登录状态的路由重定向
- 实现基于用户角色的路由访问控制
- 处理未授权访问

#### 3.4.5 实现API请求拦截器（Token处理）
- 配置Axios请求拦截器
- 自动添加Authorization头
- 配置响应拦截器
- 处理401未授权响应（自动登出）

## 4. 完成标准

用户认证模块将在满足以下条件时视为完成：

1. 用户可以成功注册新账户
2. 用户可以使用用户名和密码登录
3. 登录后用户可以查看和更新个人信息
4. 系统能够区分普通用户和管理员权限
5. 前端能够正确处理认证状态和权限控制
6. 所有API端点都受到适当的权限保护
7. 所有单元测试和集成测试通过

## 5. 进度跟踪

| 日期 | 计划进度 | 实际进度 | 备注 |
|------|----------|----------|------|
| 第2天上午 | 完成后端用户模型与数据库设计 | 已完成 | 用户模型、服务和数据库迁移脚本已完成 |
| 第2天下午 | 完成认证与安全模块实现 | 已完成 | 核心功能和单元测试已完成 |
| 第3天上午 | 完成用户API路由实现 | 已完成 | API路由已实现，集成测试部分完成 |
| 第3天下午 | 完成前端认证组件开发 | 已完成 | 所有前端组件已实现 |

## 6. 风险与应对措施

| 风险 | 可能性 | 影响 | 应对措施 |
|------|--------|------|----------|
| JWT配置问题 | 中 | 高 | 确保密钥安全存储，设置合理的过期时间 |
| 密码安全性问题 | 低 | 高 | 使用bcrypt算法，实施密码强度验证 |
| 用户数据唯一性冲突 | 中 | 中 | 实现适当的错误处理和用户反馈 |
| 前后端集成问题 | 中 | 中 | 确保API契约一致，编写详细的API文档 |

## 7. 资源需求

- Python 3.12
- FastAPI框架
- SQLAlchemy ORM
- Alembic迁移工具
- Passlib和python-jose库
- Vue 3和Pinia
- Element Plus组件库
- Axios HTTP客户端

## 8. 参考资料

- FastAPI安全文档: https://fastapi.tiangolo.com/tutorial/security/
- JWT认证最佳实践: https://auth0.com/blog/jwt-authentication-best-practices/
- Vue Router导航守卫: https://router.vuejs.org/guide/advanced/navigation-guards.html
- Pinia状态管理: https://pinia.vuejs.org/
- Element Plus表单组件: https://element-plus.org/en-US/component/form.html
