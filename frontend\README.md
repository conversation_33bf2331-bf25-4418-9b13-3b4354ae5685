# 软件和设备销售平台 - 前端

本目录包含软件和设备销售平台的前端代码，基于Vue 3和Element Plus开发。

## 技术栈

- **Vue 3**: 渐进式JavaScript框架
- **Element Plus**: UI组件库
- **Pinia**: 状态管理
- **Vue Router**: 路由管理
- **Axios**: HTTP客户端
- **Vite**: 构建工具

## 目录结构

```
frontend/
├── public/             # 静态资源
├── src/                # 源代码
│   ├── __tests__/      # 测试目录
│   │   └── components/ # 组件测试
│   ├── api/            # API请求
│   │   └── index.js    # API配置和请求函数
│   ├── assets/         # 静态资源
│   │   ├── images/     # 图片
│   │   └── styles/     # 样式
│   ├── components/     # 组件
│   │   ├── common/     # 通用组件
│   │   ├── layout/     # 布局组件
│   │   └── ui/         # UI组件
│   ├── router/         # 路由
│   │   └── index.js    # 路由配置
│   ├── store/          # 状态管理
│   │   └── index.js    # Pinia存储
│   ├── utils/          # 工具函数
│   │   ├── request.js  # 请求工具
│   │   └── validate.js # 验证工具
│   ├── views/          # 页面
│   │   ├── admin/      # 管理员页面
│   │   ├── Home.vue    # 首页
│   │   ├── Login.vue   # 登录页
│   │   ├── Register.vue # 注册页
│   │   ├── Profile.vue # 个人中心
│   │   ├── Products.vue # 产品列表
│   │   ├── ProductDetail.vue # 产品详情
│   │   └── NotFound.vue # 404页面
│   ├── App.vue         # 根组件
│   └── main.js         # 入口文件
├── tests/              # 其他测试目录
├── Dockerfile          # Docker配置
├── index.html          # HTML模板
├── package.json        # NPM配置
└── vite.config.js      # Vite配置
```

## 环境要求

- Node.js 18+
- npm 9+ 或 yarn 1.22+

## 本地开发

### 安装依赖

```bash
# 使用npm
npm install

# 或使用yarn
yarn install
```

### 环境变量

创建`.env.local`文件，包含以下环境变量：

```
VITE_API_BASE_URL=http://localhost:8000/api/v1
```

### 运行开发服务器

```bash
# 使用npm
npm run dev

# 或使用yarn
yarn dev
```

### 构建生产版本

```bash
# 使用npm
npm run build

# 或使用yarn
yarn build
```

### 运行测试

#### 使用Docker容器运行测试（推荐）

项目已配置好Docker容器化测试环境，这是推荐的测试方式，可以确保测试环境一致：

```bash
# 在项目根目录运行前端测试
docker-compose run --rm frontend_tests

# 运行特定测试文件
docker-compose run --rm frontend_tests npm run test:unit src/__tests__/components/App.test.js
```

#### 本地运行测试（可选）

如果您已经设置了本地开发环境，也可以直接在本地运行测试：

```bash
# 使用npm
npm run test:unit

# 或使用yarn
yarn test:unit

# 运行特定测试文件
npm run test:unit src/__tests__/components/App.test.js
```

注意：本地测试需要正确安装依赖，可能与Docker环境有差异。

## Docker部署

```bash
# 构建镜像
docker build -t sales-platform-frontend .

# 运行容器
docker run -p 5173:5173 sales-platform-frontend
```

## 使用Docker Compose

在项目根目录中运行：

```bash
docker-compose up -d
```

## 主要功能

### 用户功能

- 用户注册和登录
- 个人信息管理
- 订单历史查看
- 软件密钥管理

### 产品功能

- 产品浏览和搜索
- 产品详情查看
- 产品购买

### 支付功能

- 支付宝支付集成
- 支付状态查询

### 管理员功能

- 用户管理
- 产品管理
- 订单管理
- 销售统计

## 开发与测试流程

### 推荐的Docker工作流（适合所有开发者）

1. **启动开发环境**：
   ```bash
   # 启动所有服务（后端、前端、数据库）
   docker-compose up -d
   ```

2. **查看日志**：
   ```bash
   # 查看前端日志
   docker-compose logs -f frontend

   # 查看后端日志
   docker-compose logs -f backend
   ```

3. **运行测试**：
   ```bash
   # 运行前端测试
   docker-compose run --rm frontend_tests

   # 运行后端测试
   docker-compose run --rm backend_tests
   ```

4. **访问应用**：
   - 前端：http://localhost:5173
   - 后端API文档：http://localhost:8000/api/v1/docs

### 本地开发工作流（可选）

1. **TDD开发**：遵循"先写测试、再实现、最后重构"流程，每个功能点均有对应测试用例。
2. **测试运行**：`npm run test:unit`
3. **开发启动**：`npm run dev`，访问 http://localhost:5173/
4. **代码风格**：统一使用ESLint+Prettier自动格式化

## 组件使用示例

### 使用API请求

```javascript
import api from '@/api'

// 获取产品列表
const fetchProducts = async () => {
  try {
    const response = await api.product.getProducts()
    products.value = response.data
  } catch (error) {
    console.error('获取产品列表失败:', error)
  }
}
```

### 使用Pinia状态管理

```javascript
import { useUserStore } from '@/store'

const userStore = useUserStore()

// 登录
const login = async () => {
  const result = await userStore.login(username, password)
  if (result.success) {
    router.push('/')
  }
}

// 获取用户信息
const getUserInfo = () => {
  return userStore.user
}
```

## 贡献指南

1. Fork仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request