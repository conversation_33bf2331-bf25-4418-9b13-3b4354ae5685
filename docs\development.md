# 开发指南

本文档提供了软件和设备销售平台的开发指南，包括环境设置、开发流程、代码规范和常见问题解决方案。

## 目录

- [环境设置](#环境设置)
- [开发流程](#开发流程)
- [代码规范](#代码规范)
- [API文档](#api文档)
- [测试指南](#测试指南)
- [常见问题](#常见问题)

## 环境设置

### 本地开发环境

#### 后端开发环境

1. 安装Python 3.12
2. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

3. 设置环境变量（可以创建.env文件）

```
DATABASE_URL=postgresql://user:password@localhost:5432/sales_platform
SECRET_KEY=your_secret_key
ACCESS_TOKEN_EXPIRE_MINUTES=1440
ENVIRONMENT=development
ALIPAY_APP_ID=your_alipay_app_id
ALIPAY_NOTIFY_URL=http://localhost:8000/api/v1/payments/notify
ALIPAY_RETURN_URL=http://localhost:5173/payment-result
```

4. 运行开发服务器

```bash
uvicorn app.main:app --reload
```

#### 前端开发环境

1. 安装Node.js
2. 安装依赖

```bash
cd frontend
npm install
```

3. 设置环境变量（可以创建.env.local文件）

```
VITE_API_BASE_URL=http://localhost:8000/api/v1
```

4. 运行开发服务器

```bash
npm run dev
```

### Docker开发环境

使用Docker Compose可以一键启动完整的开发环境：

```bash
docker-compose up -d
```

## 开发流程

### 分支管理

- `main`: 主分支，保持稳定可发布状态
- `develop`: 开发分支，所有功能开发完成后合并到此分支
- `feature/*`: 功能分支，用于开发新功能
- `bugfix/*`: 修复分支，用于修复bug
- `release/*`: 发布分支，用于准备发布

### 开发步骤

1. 从`develop`分支创建新的功能分支
2. 在功能分支上进行开发
3. 编写测试并确保测试通过
4. 提交代码并创建Pull Request
5. 代码审查通过后合并到`develop`分支

## 代码规范

### Python代码规范

- 遵循PEP 8规范
- 使用类型注解
- 编写文档字符串
- 使用Black进行代码格式化

### JavaScript/Vue代码规范

- 遵循ESLint配置
- 使用Prettier进行代码格式化
- 组件使用PascalCase命名
- 使用Composition API

## API文档

API文档使用Swagger UI自动生成，可以在开发环境中访问：

```
http://localhost:8000/api/v1/docs
```

### 认证API

#### 登录

```
POST /api/v1/auth/login
```

请求体：

```json
{
  "username": "string",
  "password": "string"
}
```

响应：

```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "access_token": "string",
    "token_type": "bearer"
  }
}
```

### 用户API

#### 获取当前用户信息

```
GET /api/v1/users/me
```

请求头：

```
Authorization: Bearer {token}
```

响应：

```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "id": 1,
    "username": "string",
    "email": "<EMAIL>",
    "is_active": true,
    "role": "user",
    "address": "string",
    "created_at": "2023-01-01T00:00:00"
  }
}
```

## 测试指南

### 后端测试

使用pytest进行测试：

```bash
cd backend
pytest
```

运行特定测试：

```bash
pytest tests/test_auth.py
```

生成测试覆盖率报告：

```bash
pytest --cov=app
```

### 前端测试

使用Vitest进行测试：

```bash
cd frontend
npm run test:unit
```

运行特定测试：

```bash
npm run test:unit src/__tests__/components/App.test.js
```

## 常见问题

### 数据库迁移

使用Alembic进行数据库迁移：

```bash
cd backend
alembic revision --autogenerate -m "描述"
alembic upgrade head
```

### 前端构建问题

如果遇到前端构建问题，可以尝试清除缓存：

```bash
npm clean-install
```

### Docker相关问题

如果遇到Docker相关问题，可以尝试重新构建：

```bash
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```
