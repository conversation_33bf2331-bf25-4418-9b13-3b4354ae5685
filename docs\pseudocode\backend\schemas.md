# Pydantic 模型伪代码

## 1. 用户相关模型 (schemas/user.py)

```python
# 用户基础模型
class UserBase(BaseModel):
    username: str
    email: EmailStr

# 用户创建模型
class UserCreate(UserBase):
    password: str
    address: Optional[str] = None

# 用户更新模型
class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    address: Optional[str] = None

# 用户登录模型
class UserLogin(BaseModel):
    username: str
    password: str

# 用户响应模型
class User(UserBase):
    id: int
    is_active: bool
    role: str
    address: Optional[str] = None
    created_at: datetime
    
    class Config:
        orm_mode = True

# 令牌模型
class Token(BaseModel):
    access_token: str
    token_type: str

# 令牌数据模型
class TokenData(BaseModel):
    user_id: Optional[int] = None
```

## 2. 产品相关模型 (schemas/product.py)

```python
# 产品基础模型
class ProductBase(BaseModel):
    name: str
    description: str
    price: float
    type: str  # software 或 device

# 产品创建模型
class ProductCreate(ProductBase):
    pass

# 产品更新模型
class ProductUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    price: Optional[float] = None
    is_active: Optional[bool] = None

# 产品响应模型
class Product(ProductBase):
    id: int
    is_active: bool
    created_at: datetime
    
    class Config:
        orm_mode = True
```

## 3. 订单相关模型 (schemas/order.py)

```python
# 订单基础模型
class OrderBase(BaseModel):
    product_id: int

# 订单创建模型
class OrderCreate(OrderBase):
    pass

# 订单更新模型
class OrderUpdate(BaseModel):
    status: Optional[str] = None
    license_key: Optional[str] = None

# 订单响应模型
class Order(OrderBase):
    id: int
    order_number: str
    user_id: int
    status: str
    created_at: datetime
    license_key: Optional[str] = None
    product: Product
    
    class Config:
        orm_mode = True

# 支付响应模型
class PaymentResponse(BaseModel):
    order_id: int
    payment_url: str
```

## 4. 站点信息相关模型 (schemas/site.py)

```python
# 站点信息基础模型
class SiteInfoBase(BaseModel):
    company_name: str
    description: str
    contact_info: str

# 站点信息创建模型
class SiteInfoCreate(SiteInfoBase):
    pass

# 站点信息更新模型
class SiteInfoUpdate(BaseModel):
    company_name: Optional[str] = None
    description: Optional[str] = None
    contact_info: Optional[str] = None

# 站点信息响应模型
class SiteInfo(SiteInfoBase):
    id: int
    updated_at: datetime
    
    class Config:
        orm_mode = True
```

## 5. 通用响应模型 (core/schemas.py)

```python
# 通用响应模型
class ResponseModel(BaseModel):
    success: bool
    message: str
    data: Optional[Any] = None

# 分页响应模型
class PaginatedResponse(BaseModel):
    success: bool
    message: str
    data: List[Any]
    total: int
    page: int
    size: int
```
