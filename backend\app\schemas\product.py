from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field, validator


# 产品基础模型
class ProductBase(BaseModel):
    """
    产品基础模型

    Attributes:
        name: 产品名称
        description: 产品描述
        price: 产品价格
        product_type: 产品类型
        stock: 库存数量
    """
    name: str = Field(..., min_length=1, max_length=100, description="产品名称")
    description: Optional[str] = Field(None, description="产品描述")
    price: float = Field(..., gt=0, description="产品价格")
    product_type: str = Field(..., description="产品类型，software或hardware")
    stock: Optional[int] = Field(0, ge=0, description="库存数量")

    @validator("product_type")
    def validate_product_type(cls, v):
        if v not in ["software", "hardware"]:
            raise ValueError("产品类型必须是software或hardware")
        return v


# 产品创建模型
class ProductCreate(ProductBase):
    """
    产品创建模型
    """
    pass


# 产品更新模型
class ProductUpdate(BaseModel):
    """
    产品更新模型

    Attributes:
        name: 产品名称
        description: 产品描述
        price: 产品价格
        product_type: 产品类型
        stock: 库存数量
        is_active: 是否激活
    """
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="产品名称")
    description: Optional[str] = Field(None, description="产品描述")
    price: Optional[float] = Field(None, gt=0, description="产品价格")
    product_type: Optional[str] = Field(None, description="产品类型，software或hardware")
    stock: Optional[int] = Field(None, ge=0, description="库存数量")
    is_active: Optional[bool] = Field(None, description="是否激活")

    @validator("product_type")
    def validate_product_type(cls, v):
        if v is not None and v not in ["software", "hardware"]:
            raise ValueError("产品类型必须是software或hardware")
        return v


# 产品响应模型
class Product(ProductBase):
    """
    产品响应模型

    Attributes:
        id: 产品ID
        is_active: 是否激活
        created_at: 创建时间
        updated_at: 更新时间
    """
    id: int = Field(..., description="产品ID")
    is_active: bool = Field(..., description="是否激活")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True