文件路径: docs/user_stories.md

# 用户故事和验收标准

## 用户故事 1: 访客浏览公司信息

**作为** 访客，
**我想要** 浏览这家公司的网页，看公司介绍、软件介绍和设备介绍，
**以便** 看看这家公司的软件和设备是否适合我的需求。

### 验收标准

**Given** 访客访问公司网站
**When** 访客导航到公司介绍、软件介绍或设备介绍页面
**Then** 访客应该能够看到相应的信息

## 用户故事 2: 新用户注册并购买软件

**作为** 新用户，
**我想要** 注册并购买软件，
**以便** 在我的 Linux 电脑上安装和使用它。

### 验收标准

**Given** 我直接点击他们软件介绍页面的购买链接
**When** 进入付费页面，扫描二维码并完成支付
**Then** 页面跳转到新页面，显示下载链接和密钥
**And** 下载链应该可复制，密钥应该可复制
**And** 页面上应该有安装说明，密钥应该是唯一的

## 用户故事 3: 注册用户找回软件密钥

**作为** 注册用户，
**我想要** 找回软件密钥，
**以便** 我在需要重新安装软件时能够验证并完成安装。

### 验收标准

**Given** 我已经购买了他们公司的软件并且密钥忘了
**When** 我登录账户并尝试查看密钥
**Then** 系统应该提示密钥只显示一次
**And** 系统应该提供重新生成密钥的选项
**And** 重新生成的密钥应该可以被复制

## 用户故事 4: 注册用户修改个人信息

**作为** 注册用户，
**我想要** 修改我的地址信息，
**以便** 保持我的资料最新，特别是当我购买了设备需要邮寄时。

### 验收标准

**Given** 注册用户已登录
**And** 用户导航到个人信息页面
**When** 用户修改地址信息
**And** 用户保存修改
**Then** 用户的地址信息应该更新

## 用户故事 5.1: 管理员查看用户数量

**作为一个** 管理员，
**我想要** 查看注册用户的总数量，
**以便** 我能够了解用户增长情况。

### 验收标准

**Given** 我已成功登录管理员后台
**When** 我导航到用户管理页面
**Then** 我应该能看到注册用户的总数量

## 用户故事 5.2: 管理员查看销售数量

**作为一个** 管理员，
**我想要** 查看软件和设备的销售总量，
**以便** 我能够了解销售业绩。

### 验收标准

**Given** 我已成功登录管理员后台
**When** 我导航到销售报告页面
**Then** 我应该能看到软件的总销售数量
**And** 我应该能看到设备的总销售数量

## 用户故事 5.3: 管理员添加销售产品

**作为一个** 管理员，
**我想要** 添加新的软件或设备到销售列表中，
**以便** 我能够扩展产品线。

### 验收标准

**Given** 我已成功登录管理员后台
**And** 我导航到产品管理页面
**When** 我点击“添加新产品”按钮并填写产品信息（如名称、描述、价格、类型）
**And** 我点击“保存”按钮
**Then** 新产品应该出现在产品列表中
**And** 产品信息应该正确显示

## 用户故事 5.4: 管理员修改销售产品信息

**作为一个** 管理员，
**我想要** 修改现有销售产品的描述、价格等信息，
**以便** 我能够保持产品信息的准确性。

### 验收标准

**Given** 我已成功登录管理员后台
**And** 我导航到产品管理页面
**And** 产品列表中存在一个现有产品
**When** 我选择该产品并点击“编辑”按钮
**And** 我修改产品信息（如描述、价格）
**And** 我点击“保存”按钮
**Then** 产品的修改应该被保存
**And** 产品列表中的信息应该更新

## 用户故事 5.5: 管理员修改公司站点信息

**作为一个** 管理员，
**我想要** 修改公司介绍、联系方式等站点信息，
**以便** 我能够及时更新公司对外展示的内容。

### 验收标准

**Given** 我已成功登录管理员后台
**And** 我导航到站点设置页面
**When** 我修改公司介绍或联系方式等信息
**And** 我点击“保存”按钮
**Then** 站点信息应该被更新
**And** 用户访问网站时应该能看到更新后的信息