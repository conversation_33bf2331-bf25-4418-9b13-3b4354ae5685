from typing import List, Optional

from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.security import get_password_hash, verify_password
from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate


class UserService:
    """用户服务类"""

    async def get_user_by_id(self, db: AsyncSession, user_id: int) -> Optional[User]:
        """
        通过ID获取用户

        Args:
            db: 数据库会话
            user_id: 用户ID

        Returns:
            Optional[User]: 用户对象，如果不存在则返回None
        """
        result = await db.execute(select(User).where(User.id == user_id))
        return result.scalars().first()

    async def get_user_by_username(self, db: AsyncSession, username: str) -> Optional[User]:
        """
        通过用户名获取用户

        Args:
            db: 数据库会话
            username: 用户名

        Returns:
            Optional[User]: 用户对象，如果不存在则返回None
        """
        result = await db.execute(select(User).where(User.username == username))
        return result.scalars().first()

    async def get_user_by_email(self, db: AsyncSession, email: str) -> Optional[User]:
        """
        通过邮箱获取用户

        Args:
            db: 数据库会话
            email: 邮箱

        Returns:
            Optional[User]: 用户对象，如果不存在则返回None
        """
        result = await db.execute(select(User).where(User.email == email))
        return result.scalars().first()

    async def get_users(
        self, db: AsyncSession, skip: int = 0, limit: int = 100
    ) -> List[dict]:
        """
        获取用户列表

        Args:
            db: 数据库会话
            skip: 跳过记录数
            limit: 限制记录数

        Returns:
            List[dict]: 用户列表（字典形式）
        """
        result = await db.execute(select(User).offset(skip).limit(limit))
        users = result.scalars().all()

        # 将ORM模型对象转换为字典
        return [
            {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "is_active": user.is_active,
                "role": user.role,
                "address": user.address,
                "created_at": user.created_at
            }
            for user in users
        ]

    async def get_user_count(self, db: AsyncSession) -> int:
        """
        获取用户总数

        Args:
            db: 数据库会话

        Returns:
            int: 用户总数
        """
        result = await db.execute(select(func.count()).select_from(User))
        return result.scalar()

    async def create_user(self, db: AsyncSession, user_create: UserCreate) -> User:
        """
        创建用户

        Args:
            db: 数据库会话
            user_create: 用户创建模型

        Returns:
            User: 创建的用户对象
        """
        # 创建用户对象
        db_user = User(
            username=user_create.username,
            email=user_create.email,
            password_hash=get_password_hash(user_create.password),
            address=user_create.address,
        )

        # 添加到数据库
        db.add(db_user)
        await db.commit()
        await db.refresh(db_user)

        return db_user

    async def update_user(
        self, db: AsyncSession, user_id: int, user_update: UserUpdate
    ) -> Optional[User]:
        """
        更新用户

        Args:
            db: 数据库会话
            user_id: 用户ID
            user_update: 用户更新模型

        Returns:
            Optional[User]: 更新后的用户对象，如果不存在则返回None
        """
        # 获取用户
        user = await self.get_user_by_id(db, user_id)
        if not user:
            return None

        # 更新用户属性
        update_data = user_update.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(user, key, value)

        # 提交更改
        await db.commit()
        await db.refresh(user)

        return user

    async def authenticate_user(
        self, db: AsyncSession, username: str, password: str
    ) -> Optional[User]:
        """
        验证用户

        Args:
            db: 数据库会话
            username: 用户名
            password: 密码

        Returns:
            Optional[User]: 验证成功的用户对象，如果验证失败则返回None
        """
        user = await self.get_user_by_username(db, username)
        if not user:
            return None
        if not verify_password(password, user.password_hash):
            return None
        return user


# 创建用户服务实例
user_service = UserService()