# 文件路径: docker-compose.prod.yml

version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "80:8000" # 生产环境通常使用 80 端口
    volumes:
      # 生产环境通常不挂载本地代码卷，而是将代码构建到镜像中
      # - ./backend:/app
      - backend_static:/app/static # 如果有静态文件需要服务
    depends_on:
      - database
    environment:
      # 生产环境所需的环境变量，例如数据库连接信息、外部服务地址等
      DATABASE_URL: ****************************************/mydatabase_prod
      # 其他生产环境变量...
    # restart: always # 生产环境建议配置重启策略

  frontend:
    build: ./frontend
    ports:
      - "80:80" # 生产环境通常使用 80 端口
    volumes:
      # 生产环境通常不挂载本地代码卷
      # - ./frontend:/app
      - frontend_dist:/usr/share/nginx/html # 假设使用 nginx 服务静态文件
    # depends_on:
    #   - backend # 前端通常通过反向代理访问后端，不直接依赖
    # restart: always # 生产环境建议配置重启策略

  database:
    image: postgres:16
    # 生产环境通常不暴露数据库端口到宿主机
    # ports:
    #   - "5432:5432"
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: mydatabase_prod
    volumes:
      - db_data_prod:/var/lib/postgresql/data
    # restart: always # 生产环境建议配置重启策略

  # 示例：生产环境可能需要一个反向代理
  # nginx:
  #   image: nginx:latest
  #   ports:
  #     - "80:80"
  #   volumes:
  #     - ./nginx.conf:/etc/nginx/nginx.conf:ro
  #     - frontend_dist:/usr/share/nginx/html:ro
  #   depends_on:
  #     - backend
  #   restart: always

volumes:
  db_data_prod:
  backend_static: # 如果有后端静态文件
  frontend_dist: # 前端构建后的文件