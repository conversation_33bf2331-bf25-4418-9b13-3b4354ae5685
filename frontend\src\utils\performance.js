/**
 * 性能监控工具
 * 用于收集和分析前端性能指标
 */

// 性能指标名称
const METRICS = {
  FP: 'first-paint', // 首次绘制
  FCP: 'first-contentful-paint', // 首次内容绘制
  LCP: 'largest-contentful-paint', // 最大内容绘制
  FID: 'first-input-delay', // 首次输入延迟
  CLS: 'cumulative-layout-shift', // 累积布局偏移
  TTFB: 'time-to-first-byte', // 首字节时间
  DCL: 'dom-content-loaded', // DOM内容加载完成
  L: 'load', // 页面完全加载
}

// 收集的性能数据
let performanceData = {
  [METRICS.FP]: 0,
  [METRICS.FCP]: 0,
  [METRICS.LCP]: 0,
  [METRICS.FID]: 0,
  [METRICS.CLS]: 0,
  [METRICS.TTFB]: 0,
  [METRICS.DCL]: 0,
  [METRICS.L]: 0,
  resourceLoading: [], // 资源加载时间
  jsErrors: [], // JS错误
  apiRequests: [], // API请求
}

/**
 * 初始化性能监控
 */
export function initPerformanceMonitoring() {
  // 检查浏览器是否支持性能API
  if (!window.performance || !window.performance.timing) {
    console.warn('当前浏览器不支持Performance API')
    return
  }

  // 收集基本性能指标
  collectBasicMetrics()
  
  // 收集Web Vitals指标
  collectWebVitals()
  
  // 监听资源加载
  observeResourceTiming()
  
  // 监听JS错误
  observeJSErrors()
  
  // 监听API请求
  observeAPIRequests()
  
  // 页面加载完成后输出性能数据
  window.addEventListener('load', () => {
    // 延迟执行以确保所有指标都已收集
    setTimeout(() => {
      finalizeMetrics()
      
      // 开发环境下在控制台输出性能数据
      if (process.env.NODE_ENV === 'development') {
        console.group('📊 页面性能指标')
        console.log('首次绘制 (FP):', formatTime(performanceData[METRICS.FP]))
        console.log('首次内容绘制 (FCP):', formatTime(performanceData[METRICS.FCP]))
        console.log('最大内容绘制 (LCP):', formatTime(performanceData[METRICS.LCP]))
        console.log('首次输入延迟 (FID):', formatTime(performanceData[METRICS.FID]))
        console.log('累积布局偏移 (CLS):', performanceData[METRICS.CLS].toFixed(3))
        console.log('首字节时间 (TTFB):', formatTime(performanceData[METRICS.TTFB]))
        console.log('DOM内容加载完成 (DCL):', formatTime(performanceData[METRICS.DCL]))
        console.log('页面完全加载 (L):', formatTime(performanceData[METRICS.L]))
        console.groupEnd()
        
        // 输出资源加载情况
        if (performanceData.resourceLoading.length > 0) {
          console.group('🔄 资源加载时间 (>500ms)')
          performanceData.resourceLoading
            .filter(item => item.duration > 500)
            .sort((a, b) => b.duration - a.duration)
            .forEach(item => {
              console.log(`${item.name}: ${formatTime(item.duration)}`)
            })
          console.groupEnd()
        }
      }
      
      // 在生产环境中可以将数据发送到分析服务器
      if (process.env.NODE_ENV === 'production') {
        // sendToAnalyticsServer(performanceData)
      }
    }, 3000)
  })
}

/**
 * 收集基本性能指标
 */
function collectBasicMetrics() {
  // 使用Performance Observer API收集绘制指标
  if (window.PerformanceObserver) {
    // 观察绘制指标
    try {
      const paintObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries()
        entries.forEach(entry => {
          if (entry.name === 'first-paint') {
            performanceData[METRICS.FP] = entry.startTime
          } else if (entry.name === 'first-contentful-paint') {
            performanceData[METRICS.FCP] = entry.startTime
          }
        })
      })
      paintObserver.observe({ type: 'paint', buffered: true })
    } catch (e) {
      console.warn('Paint Timing API not supported', e)
    }
  }
}

/**
 * 收集Web Vitals指标
 */
function collectWebVitals() {
  // 最大内容绘制 (LCP)
  try {
    const lcpObserver = new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries()
      const lastEntry = entries[entries.length - 1]
      performanceData[METRICS.LCP] = lastEntry.startTime
    })
    lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true })
  } catch (e) {
    console.warn('LCP not supported', e)
  }
  
  // 首次输入延迟 (FID)
  try {
    const fidObserver = new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries()
      entries.forEach(entry => {
        performanceData[METRICS.FID] = entry.processingStart - entry.startTime
      })
    })
    fidObserver.observe({ type: 'first-input', buffered: true })
  } catch (e) {
    console.warn('FID not supported', e)
  }
  
  // 累积布局偏移 (CLS)
  try {
    let clsValue = 0
    let clsEntries = []
    
    const clsObserver = new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries()
      
      entries.forEach(entry => {
        // 只有不是由用户输入引起的布局偏移才计入CLS
        if (!entry.hadRecentInput) {
          clsValue += entry.value
          clsEntries.push(entry)
        }
      })
      
      performanceData[METRICS.CLS] = clsValue
    })
    
    clsObserver.observe({ type: 'layout-shift', buffered: true })
  } catch (e) {
    console.warn('CLS not supported', e)
  }
}

/**
 * 观察资源加载时间
 */
function observeResourceTiming() {
  try {
    const resourceObserver = new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries()
      
      entries.forEach(entry => {
        // 只关注加载时间较长的资源
        if (entry.duration > 100) {
          performanceData.resourceLoading.push({
            name: entry.name,
            type: entry.initiatorType,
            duration: entry.duration,
            size: entry.transferSize,
            startTime: entry.startTime
          })
        }
      })
    })
    
    resourceObserver.observe({ type: 'resource', buffered: true })
  } catch (e) {
    console.warn('Resource Timing API not supported', e)
  }
}

/**
 * 监听JS错误
 */
function observeJSErrors() {
  window.addEventListener('error', (event) => {
    performanceData.jsErrors.push({
      message: event.message,
      source: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      timestamp: Date.now()
    })
  }, true)
  
  window.addEventListener('unhandledrejection', (event) => {
    performanceData.jsErrors.push({
      message: `Unhandled Promise Rejection: ${event.reason}`,
      timestamp: Date.now()
    })
  })
}

/**
 * 监听API请求
 * 需要与Axios拦截器配合使用
 */
function observeAPIRequests() {
  // 这部分需要在Axios拦截器中实现
}

/**
 * 完成指标收集
 */
function finalizeMetrics() {
  const timing = window.performance.timing
  
  // 计算首字节时间
  performanceData[METRICS.TTFB] = timing.responseStart - timing.navigationStart
  
  // 计算DOM内容加载完成时间
  performanceData[METRICS.DCL] = timing.domContentLoadedEventEnd - timing.navigationStart
  
  // 计算页面完全加载时间
  performanceData[METRICS.L] = timing.loadEventEnd - timing.navigationStart
}

/**
 * 格式化时间
 * @param {number} time - 毫秒时间
 * @returns {string} 格式化后的时间
 */
function formatTime(time) {
  return `${time.toFixed(2)}ms`
}

/**
 * 发送数据到分析服务器
 * @param {Object} data - 性能数据
 */
function sendToAnalyticsServer(data) {
  // 实现发送数据到分析服务器的逻辑
  // 例如使用Beacon API或XHR
  if (navigator.sendBeacon) {
    navigator.sendBeacon('/analytics/performance', JSON.stringify(data))
  } else {
    fetch('/analytics/performance', {
      method: 'POST',
      body: JSON.stringify(data),
      keepalive: true
    })
  }
}
