from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field, validator

from app.schemas.product import Product


class OrderItemBase(BaseModel):
    """
    订单项基础模型

    Attributes:
        product_id: 产品ID
        quantity: 数量
    """
    product_id: int = Field(..., description="产品ID")
    quantity: int = Field(1, ge=1, description="数量")


class OrderItemCreate(OrderItemBase):
    """
    订单项创建模型
    """
    pass


class OrderBase(BaseModel):
    """
    订单基础模型

    Attributes:
        items: 订单项列表
    """
    items: List[OrderItemCreate] = Field(..., description="订单项列表")


class OrderCreate(OrderBase):
    """
    订单创建模型
    """
    pass


class OrderItemResponse(OrderItemBase):
    """
    订单项响应模型

    Attributes:
        id: 订单项ID
        price: 单价
        created_at: 创建时间
        product: 产品信息
    """
    id: int = Field(..., description="订单项ID")
    price: float = Field(..., description="单价")
    created_at: datetime = Field(..., description="创建时间")
    product: Optional[Product] = Field(None, description="产品信息")

    class Config:
        from_attributes = True


class OrderResponse(BaseModel):
    """
    订单响应模型

    Attributes:
        id: 订单ID
        order_number: 订单编号
        user_id: 用户ID
        total_amount: 订单总金额
        status: 订单状态
        payment_method: 支付方式
        created_at: 创建时间
        updated_at: 更新时间
        items: 订单项列表
    """
    id: int = Field(..., description="订单ID")
    order_number: str = Field(..., description="订单编号")
    user_id: int = Field(..., description="用户ID")
    total_amount: float = Field(..., description="订单总金额")
    status: str = Field(..., description="订单状态")
    payment_method: Optional[str] = Field(None, description="支付方式")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    items: List[OrderItemResponse] = Field([], description="订单项列表")

    class Config:
        from_attributes = True


class OrderUpdate(BaseModel):
    """
    订单更新模型

    Attributes:
        status: 订单状态
        payment_method: 支付方式
    """
    status: Optional[str] = Field(None, description="订单状态")
    payment_method: Optional[str] = Field(None, description="支付方式")


class PaymentBase(BaseModel):
    """
    支付记录基础模型

    Attributes:
        order_id: 订单ID
        payment_id: 支付平台交易号
        amount: 支付金额
        status: 支付状态
        payment_method: 支付方式
    """
    order_id: int = Field(..., description="订单ID")
    payment_id: str = Field(..., description="支付平台交易号")
    amount: float = Field(..., gt=0, description="支付金额")
    status: str = Field(..., description="支付状态")
    payment_method: str = Field(..., description="支付方式")


class PaymentCreate(PaymentBase):
    """
    支付记录创建模型
    """
    pass


class PaymentResponse(PaymentBase):
    """
    支付记录响应模型

    Attributes:
        id: 支付记录ID
        payment_time: 支付时间
        created_at: 创建时间
        updated_at: 更新时间
    """
    id: int = Field(..., description="支付记录ID")
    payment_time: Optional[datetime] = Field(None, description="支付时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class PaymentUpdate(BaseModel):
    """
    支付记录更新模型

    Attributes:
        status: 支付状态
        payment_time: 支付时间
    """
    status: Optional[str] = Field(None, description="支付状态")
    payment_time: Optional[datetime] = Field(None, description="支付时间")


class LicenseKeyBase(BaseModel):
    """
    软件许可密钥基础模型

    Attributes:
        user_id: 用户ID
        product_id: 产品ID
        order_id: 订单ID
        license_key: 许可密钥
    """
    user_id: int = Field(..., description="用户ID")
    product_id: int = Field(..., description="产品ID")
    order_id: int = Field(..., description="订单ID")
    license_key: str = Field(..., description="许可密钥")


class LicenseKeyCreate(LicenseKeyBase):
    """
    软件许可密钥创建模型
    """
    pass


class LicenseKeyResponse(LicenseKeyBase):
    """
    软件许可密钥响应模型

    Attributes:
        id: 密钥ID
        is_active: 是否激活
        created_at: 创建时间
        updated_at: 更新时间
        product: 产品信息
    """
    id: int = Field(..., description="密钥ID")
    is_active: bool = Field(..., description="是否激活")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    product: Optional[Product] = Field(None, description="产品信息")

    class Config:
        from_attributes = True


class PaymentInitiateResponse(BaseModel):
    """
    支付发起响应模型

    Attributes:
        order_id: 订单ID
        payment_url: 支付链接
    """
    order_id: int = Field(..., description="订单ID")
    payment_url: str = Field(..., description="支付链接")