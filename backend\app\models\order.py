from enum import Enum
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Float, Foreign<PERSON>ey, Integer, String
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class OrderStatus(str, Enum):
    """订单状态枚举"""
    PENDING = "pending"
    PAID = "paid"
    CANCELLED = "cancelled"


class Order(Base):
    """
    订单数据模型

    Attributes:
        id: 订单ID
        order_number: 订单编号
        user_id: 用户ID
        total_amount: 订单总金额
        status: 订单状态（pending, paid, cancelled）
        payment_method: 支付方式
        created_at: 创建时间
        updated_at: 更新时间
        user: 用户关系
        order_items: 订单项关系
        payments: 支付记录关系
    """
    __tablename__ = "orders"

    id = Column(Integer, primary_key=True, index=True)
    order_number = Column(String(50), unique=True, nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    total_amount = Column(Float(precision=2), nullable=False)
    status = Column(String(20), default="pending")  # pending, paid, cancelled
    payment_method = Column(String(20), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    user = relationship("User", back_populates="orders")
    order_items = relationship("OrderItem", back_populates="order", cascade="all, delete-orphan")
    payments = relationship("Payment", back_populates="order", cascade="all, delete-orphan")


class OrderItem(Base):
    """
    订单项数据模型

    Attributes:
        id: 订单项ID
        order_id: 订单ID
        product_id: 产品ID
        quantity: 数量
        price: 单价
        created_at: 创建时间
        order: 订单关系
        product: 产品关系
    """
    __tablename__ = "order_items"

    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=False)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    quantity = Column(Integer, nullable=False, default=1)
    price = Column(Float(precision=2), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关系
    order = relationship("Order", back_populates="order_items")
    product = relationship("Product", back_populates="order_items")


class Payment(Base):
    """
    支付记录数据模型

    Attributes:
        id: 支付记录ID
        order_id: 订单ID
        payment_id: 支付平台交易号
        amount: 支付金额
        status: 支付状态（pending, success, failed）
        payment_method: 支付方式
        payment_time: 支付时间
        created_at: 创建时间
        updated_at: 更新时间
        order: 订单关系
    """
    __tablename__ = "payments"

    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=False)
    payment_id = Column(String(100), unique=True, nullable=False)
    amount = Column(Float(precision=2), nullable=False)
    status = Column(String(20), nullable=False)  # pending, success, failed
    payment_method = Column(String(20), nullable=False)
    payment_time = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    order = relationship("Order", back_populates="payments")


class LicenseKey(Base):
    """
    软件许可密钥数据模型

    Attributes:
        id: 密钥ID
        user_id: 用户ID
        product_id: 产品ID
        order_id: 订单ID
        license_key: 许可密钥
        is_active: 是否激活
        created_at: 创建时间
        updated_at: 更新时间
        user: 用户关系
        product: 产品关系
        order: 订单关系
    """
    __tablename__ = "license_keys"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    order_id = Column(Integer, ForeignKey("orders.id", ondelete="CASCADE"), nullable=False)
    license_key = Column(String(100), unique=True, nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    user = relationship("User")
    product = relationship("Product")
    order = relationship("Order")