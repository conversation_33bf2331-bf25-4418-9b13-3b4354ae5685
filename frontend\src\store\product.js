import { defineStore } from 'pinia'
import api from '../api'

// 产品状态管理
export const useProductStore = defineStore('product', {
  state: () => ({
    products: [],
    currentProduct: null,
    loading: false,
    filters: {
      productType: '',
      page: 1,
      pageSize: 8
    },
    total: 0
  }),

  getters: {
    // 获取软件产品
    softwareProducts: (state) => state.products.filter(p => p.product_type === 'software'),
    
    // 获取硬件产品
    hardwareProducts: (state) => state.products.filter(p => p.product_type === 'hardware'),
    
    // 获取产品总数
    productsCount: (state) => state.total
  },

  actions: {
    // 获取产品列表
    async fetchProducts(params = {}) {
      this.loading = true
      try {
        // 合并过滤条件
        const queryParams = {
          skip: params.skip !== undefined ? params.skip : (this.filters.page - 1) * this.filters.pageSize,
          limit: params.limit !== undefined ? params.limit : this.filters.pageSize
        }
        
        if (params.product_type || this.filters.productType) {
          queryParams.product_type = params.product_type || this.filters.productType
        }
        
        const response = await api.product.getProducts(queryParams)
        
        if (response.data.success) {
          this.products = response.data.data
          this.total = response.data.total
          
          // 更新过滤条件
          if (params.product_type !== undefined) {
            this.filters.productType = params.product_type
          }
          if (params.page !== undefined) {
            this.filters.page = params.page
          }
          if (params.pageSize !== undefined) {
            this.filters.pageSize = params.pageSize
          }
          
          return { success: true, data: response.data.data, total: response.data.total }
        } else {
          return { success: false, message: response.data.message }
        }
      } catch (error) {
        console.error('获取产品列表失败:', error)
        return { success: false, message: error.response?.data?.message || '获取产品列表失败' }
      } finally {
        this.loading = false
      }
    },

    // 获取产品详情
    async fetchProductById(id) {
      this.loading = true
      try {
        const response = await api.product.getProductById(id)
        
        if (response.data.success) {
          this.currentProduct = response.data.data
          return { success: true, data: response.data.data }
        } else {
          return { success: false, message: response.data.message }
        }
      } catch (error) {
        console.error('获取产品详情失败:', error)
        return { success: false, message: error.response?.data?.message || '获取产品详情失败' }
      } finally {
        this.loading = false
      }
    },
    
    // 设置过滤条件
    setFilters(filters) {
      this.filters = { ...this.filters, ...filters }
    },
    
    // 重置过滤条件
    resetFilters() {
      this.filters = {
        productType: '',
        page: 1,
        pageSize: 8
      }
    }
  }
})
