from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Integer, String
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class User(Base):
    """
    用户数据模型

    Attributes:
        id: 用户ID
        username: 用户名
        email: 电子邮箱
        password_hash: 密码哈希
        is_active: 是否活跃
        role: 角色（user或admin）
        address: 地址
        created_at: 创建时间
        orders: 用户订单关系
    """
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    password_hash = Column(String)
    is_active = Column(Boolean, default=True)
    role = Column(String, default="user")  # user 或 admin
    address = Column(String, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关系
    orders = relationship("Order", back_populates="user")