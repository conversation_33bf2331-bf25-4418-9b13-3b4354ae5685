"""
安全模块单元测试
"""
import time
from datetime import timed<PERSON><PERSON>

import pytest
import pytest_asyncio
from fastapi import HTT<PERSON>Exception
from jose import jwt
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.dependencies import (
    get_current_active_user,
    get_current_admin_user,
    get_current_user,
)
from app.core.security import (
    ALGORITHM,
    create_access_token,
    get_password_hash,
    verify_password,
)
from app.models.user import User
from app.schemas.user import UserCreate


@pytest_asyncio.fixture(scope="function")
async def test_user(db: AsyncSession) -> User:
    """创建测试用户"""
    from app.services.user import user_service

    user_create = UserCreate(
        username="testuser",
        email="<EMAIL>",
        password="password123",
    )
    user = await user_service.create_user(db, user_create)
    return user


@pytest_asyncio.fixture(scope="function")
async def admin_user(db: AsyncSession) -> User:
    """创建管理员用户"""
    from app.services.user import user_service

    user_create = UserCreate(
        username="admin",
        email="<EMAIL>",
        password="admin123",
    )
    user = await user_service.create_user(db, user_create)
    # 设置为管理员
    user.role = "admin"
    db.add(user)
    await db.commit()
    await db.refresh(user)
    return user


@pytest.fixture(scope="function")
def user_token(test_user: User) -> str:
    """生成用户令牌"""
    return create_access_token(subject=test_user.id)


@pytest.fixture(scope="function")
def admin_token(admin_user: User) -> str:
    """生成管理员令牌"""
    return create_access_token(subject=admin_user.id)


def test_get_password_hash():
    """测试密码哈希生成"""
    # 生成密码哈希
    password = "password123"
    hashed = get_password_hash(password)

    # 验证哈希不等于原始密码
    assert hashed != password

    # 验证哈希长度
    assert len(hashed) > 0


def test_verify_password_success():
    """测试密码验证成功"""
    # 生成密码哈希
    password = "password123"
    hashed = get_password_hash(password)

    # 验证密码
    assert verify_password(password, hashed) is True


def test_verify_password_failure():
    """测试密码验证失败"""
    # 生成密码哈希
    password = "password123"
    hashed = get_password_hash(password)

    # 验证错误密码
    assert verify_password("wrongpassword", hashed) is False


def test_create_access_token():
    """测试创建访问令牌"""
    # 创建访问令牌
    user_id = 1
    token = create_access_token(subject=user_id)

    # 验证令牌不为空
    assert token is not None
    assert len(token) > 0

    # 解码令牌
    payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[ALGORITHM])

    # 验证令牌内容
    assert payload["sub"] == str(user_id)
    assert "exp" in payload


def test_create_access_token_with_expires_delta():
    """测试创建带有过期时间的访问令牌"""
    # 创建访问令牌
    user_id = 1
    expires_delta = timedelta(minutes=30)
    token = create_access_token(subject=user_id, expires_delta=expires_delta)

    # 验证令牌不为空
    assert token is not None
    assert len(token) > 0

    # 解码令牌
    payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[ALGORITHM])

    # 验证令牌内容
    assert payload["sub"] == str(user_id)
    assert "exp" in payload


def test_create_access_token_expired():
    """测试创建已过期的访问令牌"""
    # 创建访问令牌
    user_id = 1
    expires_delta = timedelta(seconds=-1)
    token = create_access_token(subject=user_id, expires_delta=expires_delta)

    # 验证令牌不为空
    assert token is not None
    assert len(token) > 0

    # 解码令牌（应该抛出异常）
    with pytest.raises(jwt.JWTError):
        jwt.decode(token, settings.SECRET_KEY, algorithms=[ALGORITHM])


@pytest.mark.asyncio
async def test_get_current_user_success(db: AsyncSession, test_user: User, user_token: str):
    """测试获取当前用户成功"""
    # 获取当前用户
    user = await get_current_user(db, token=user_token)

    # 验证用户字段
    assert user is not None
    assert user.id == test_user.id
    assert user.username == test_user.username
    assert user.email == test_user.email


@pytest.mark.asyncio
async def test_get_current_user_invalid_token(db: AsyncSession):
    """测试获取当前用户失败（无效令牌）"""
    # 获取当前用户
    with pytest.raises(HTTPException) as excinfo:
        await get_current_user(db, token="invalid_token")

    # 验证异常
    assert excinfo.value.status_code == 401
    assert excinfo.value.detail == "无法验证凭据"
    assert excinfo.value.headers["WWW-Authenticate"] == "Bearer"


@pytest.mark.asyncio
async def test_get_current_user_user_not_found(db: AsyncSession):
    """测试获取当前用户失败（用户不存在）"""
    # 创建访问令牌
    token = create_access_token(subject=999)

    # 获取当前用户
    with pytest.raises(HTTPException) as excinfo:
        await get_current_user(db, token=token)

    # 验证异常
    assert excinfo.value.status_code == 401
    assert excinfo.value.detail == "无法验证凭据"


@pytest.mark.asyncio
async def test_get_current_active_user_success(db: AsyncSession, test_user: User, user_token: str):
    """测试获取当前活跃用户成功"""
    # 获取当前用户
    current_user = await get_current_user(db, token=user_token)

    # 获取当前活跃用户
    user = await get_current_active_user(current_user)

    # 验证用户字段
    assert user is not None
    assert user.id == test_user.id
    assert user.username == test_user.username
    assert user.email == test_user.email


@pytest.mark.asyncio
async def test_get_current_active_user_inactive(db: AsyncSession, test_user: User, user_token: str):
    """测试获取当前活跃用户失败（用户不活跃）"""
    # 设置用户为不活跃
    test_user.is_active = False
    db.add(test_user)
    await db.commit()

    # 获取当前用户
    current_user = await get_current_user(db, token=user_token)

    # 获取当前活跃用户
    with pytest.raises(HTTPException) as excinfo:
        await get_current_active_user(current_user)

    # 验证异常
    assert excinfo.value.status_code == 400
    assert excinfo.value.detail == "用户不活跃"


@pytest.mark.asyncio
async def test_get_current_admin_user_success(db: AsyncSession, admin_user: User, admin_token: str):
    """测试获取当前管理员用户成功"""
    # 获取当前用户
    current_user = await get_current_user(db, token=admin_token)

    # 获取当前活跃用户
    active_user = await get_current_active_user(current_user)

    # 获取当前管理员用户
    user = await get_current_admin_user(active_user)

    # 验证用户字段
    assert user is not None
    assert user.id == admin_user.id
    assert user.username == admin_user.username
    assert user.email == admin_user.email
    assert user.role == "admin"


@pytest.mark.asyncio
async def test_get_current_admin_user_not_admin(db: AsyncSession, test_user: User, user_token: str):
    """测试获取当前管理员用户失败（用户不是管理员）"""
    # 获取当前用户
    current_user = await get_current_user(db, token=user_token)

    # 获取当前活跃用户
    active_user = await get_current_active_user(current_user)

    # 获取当前管理员用户
    with pytest.raises(HTTPException) as excinfo:
        await get_current_admin_user(active_user)

    # 验证异常
    assert excinfo.value.status_code == 403
    assert excinfo.value.detail == "权限不足"
