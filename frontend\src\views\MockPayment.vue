<template>
  <PageContainer customClass="payment-page">
    <div class="payment-container">
      <div class="payment-header">
        <div class="payment-logo">
          <img src="../assets/alipay-logo.png" alt="支付宝" />
          <h1>模拟支付宝</h1>
        </div>
        <div class="payment-steps">
          <el-steps :active="currentStep" finish-status="success" simple>
            <el-step title="确认订单" />
            <el-step title="支付" />
            <el-step title="完成" />
          </el-steps>
        </div>
      </div>

      <div class="payment-content">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="5" animated />
          <div class="loading-text">正在加载订单信息，请稍候...</div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <el-result
            icon="error"
            title="加载订单失败"
            :sub-title="error"
          >
            <template #extra>
              <AppButton type="primary" @click="goBack" round>返回</AppButton>
            </template>
          </el-result>
        </div>

        <!-- 订单信息 -->
        <div v-else-if="orderInfo" class="order-container">
          <el-card shadow="never" class="order-card">
            <template #header>
              <div class="order-header">
                <h2>订单信息</h2>
                <el-tag type="warning" effect="dark">待支付</el-tag>
              </div>
            </template>

            <div class="order-details">
              <div class="order-info">
                <div class="info-item">
                  <span class="label">订单号:</span>
                  <span class="value">{{ orderInfo.order_number }}</span>
                </div>
                <div class="info-item">
                  <span class="label">创建时间:</span>
                  <span class="value">{{ formatDate(orderInfo.created_at) }}</span>
                </div>
              </div>

              <div class="order-items">
                <h3>订单商品</h3>
                <div v-if="orderInfo.items && orderInfo.items.length > 0">
                  <div v-for="item in orderInfo.items" :key="item.id" class="item-card">
                    <div class="item-info">
                      <div class="item-name">{{ item.product?.name || `产品 ${item.product_id}` }}</div>
                      <div class="item-type">
                        <el-tag size="small" :type="item.product?.product_type === 'software' ? 'success' : 'warning'">
                          {{ item.product?.product_type === 'software' ? '软件' : '硬件' }}
                        </el-tag>
                      </div>
                    </div>
                    <div class="item-price">
                      <span class="price">¥{{ item.price.toFixed(2) }}</span>
                      <span class="quantity">x {{ item.quantity }}</span>
                    </div>
                  </div>
                </div>
                <div v-else>
                  <!-- 如果没有订单商品信息，但有订单ID，尝试显示产品信息 -->
                  <div v-if="productInfo" class="item-card">
                    <div class="item-info">
                      <div class="item-name">{{ productInfo.name }}</div>
                      <div class="item-type">
                        <el-tag size="small" :type="productInfo.product_type === 'software' ? 'success' : 'warning'">
                          {{ productInfo.product_type === 'software' ? '软件' : '硬件' }}
                        </el-tag>
                      </div>
                    </div>
                    <div class="item-price">
                      <span class="price">¥{{ orderInfo.total_amount.toFixed(2) }}</span>
                      <span class="quantity">x 1</span>
                    </div>
                  </div>
                  <el-empty v-else description="暂无订单商品信息" />
                </div>
              </div>

              <div class="order-summary">
                <div class="summary-item">
                  <span>商品总价:</span>
                  <span>¥{{ orderInfo.total_amount.toFixed(2) }}</span>
                </div>
                <div class="summary-item">
                  <span>运费:</span>
                  <span>¥0.00</span>
                </div>
                <div class="summary-item total">
                  <span>应付金额:</span>
                  <span>¥{{ orderInfo.total_amount.toFixed(2) }}</span>
                </div>
              </div>
            </div>
          </el-card>

          <el-card shadow="never" class="payment-methods-card">
            <template #header>
              <h2>选择支付方式</h2>
            </template>

            <div class="payment-methods">
              <div
                class="method-item"
                :class="{ active: paymentMethod === 'alipay' }"
                @click="paymentMethod = 'alipay'"
              >
                <div class="method-icon">
                  <img src="../assets/alipay-logo.png" alt="支付宝" />
                </div>
                <div class="method-info">
                  <div class="method-name">支付宝</div>
                  <div class="method-desc">推荐使用支付宝扫码支付</div>
                </div>
                <div class="method-check">
                  <el-icon v-if="paymentMethod === 'alipay'"><Check /></el-icon>
                </div>
              </div>

              <div class="method-item disabled">
                <div class="method-icon">
                  <el-icon><CreditCard /></el-icon>
                </div>
                <div class="method-info">
                  <div class="method-name">银行卡支付</div>
                  <div class="method-desc">暂不可用</div>
                </div>
              </div>

              <div class="method-item disabled">
                <div class="method-icon">
                  <el-icon><Money /></el-icon>
                </div>
                <div class="method-info">
                  <div class="method-name">微信支付</div>
                  <div class="method-desc">暂不可用</div>
                </div>
              </div>
            </div>
          </el-card>

          <div class="payment-actions">
            <AppButton @click="cancelPayment" round>取消支付</AppButton>
            <AppButton
              type="primary"
              size="large"
              :loading="paying"
              @click="confirmPayment"
              round
            >
              确认支付 ¥{{ orderInfo.total_amount.toFixed(2) }}
            </AppButton>
          </div>
        </div>

        <!-- 订单不存在 -->
        <div v-else class="error-container">
          <el-result
            icon="error"
            title="订单不存在"
            sub-title="未找到订单信息，请返回重试"
          >
            <template #extra>
              <AppButton type="primary" @click="goBack" round>返回</AppButton>
            </template>
          </el-result>
        </div>
      </div>
    </div>
  </PageContainer>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useOrderStore } from '../store'
import { ElMessage, ElMessageBox } from 'element-plus'
import PageContainer from '../components/layout/PageContainer.vue'
import { Check, CreditCard, Money } from '@element-plus/icons-vue'
import api from '../api'

// 路由和状态管理
const route = useRoute()
const router = useRouter()
const orderStore = useOrderStore()

// 状态变量
const loading = ref(true)
const error = ref(null)
const orderInfo = ref(null)
const productInfo = ref(null)
const paymentMethod = ref('alipay')
const paying = ref(false)
const currentStep = ref(1)

// 从URL参数中获取订单信息
const orderId = computed(() => {
  const id = route.query.order_id
  return id ? parseInt(id) : null
})

// 获取订单信息
const fetchOrderInfo = async () => {
  if (!orderId.value) {
    error.value = '未找到订单ID，无法加载订单信息'
    loading.value = false
    return
  }

  try {
    console.log('获取订单信息，订单ID:', orderId.value)
    const result = await orderStore.fetchOrderById(orderId.value)
    console.log('获取订单信息结果:', result)

    if (result.success) {
      orderInfo.value = orderStore.currentOrder
      console.log('订单信息:', orderInfo.value)

      // 检查订单商品信息
      if (!orderInfo.value.items || orderInfo.value.items.length === 0) {
        console.warn('订单商品信息为空，尝试重新获取订单详情')
        // 如果订单商品信息为空，尝试重新获取订单详情
        const detailResult = await orderStore.fetchOrderById(orderId.value, true)
        if (detailResult.success) {
          orderInfo.value = orderStore.currentOrder
          console.log('重新获取的订单信息:', orderInfo.value)
        }

        // 如果仍然没有订单商品信息，尝试从URL参数或订单号中获取产品ID并获取产品信息
        if (!orderInfo.value.items || orderInfo.value.items.length === 0) {
          console.log('订单商品信息为空，尝试获取产品信息')
          console.log('URL参数:', route.query)
          console.log('订单号:', orderInfo.value.order_number)

          let productId = null

          // 尝试从URL参数中获取产品ID
          if (route.query.product_id) {
            console.log('从URL参数中获取产品ID')
            productId = parseInt(route.query.product_id)
          }
          // 尝试从订单号中提取产品ID
          else if (orderInfo.value.order_number) {
            console.log('尝试从订单号中提取产品ID')
            const parts = orderInfo.value.order_number.split('-')
            console.log('订单号拆分结果:', parts)

            if (parts.length >= 3) {
              // 订单号格式：ORD-timestamp-productId-random
              productId = parseInt(parts[2])
              console.log('从订单号中提取的产品ID:', productId)
            }
          }

          // 如果获取到产品ID，尝试获取产品信息
          if (productId) {
            console.log('尝试获取产品信息，产品ID:', productId)
            try {
              const response = await api.product.getProductById(productId)
              console.log('产品信息响应:', response)

              if (response.data && response.data.data) {
                productInfo.value = response.data.data
                console.log('获取到产品信息:', productInfo.value)
              }
            } catch (productError) {
              console.error('获取产品信息失败:', productError)
            }
          } else {
            console.warn('无法获取产品ID')

            // 尝试直接获取入门版软件许可证的产品信息（ID为3）
            try {
              console.log('尝试直接获取入门版软件许可证的产品信息')
              const response = await api.product.getProductById(3)
              console.log('产品信息响应:', response)

              if (response.data && response.data.data) {
                productInfo.value = response.data.data
                console.log('获取到产品信息:', productInfo.value)
              }
            } catch (productError) {
              console.error('获取产品信息失败:', productError)
            }
          }
        }
      } else {
        // 如果有订单商品信息，从第一个订单项中获取产品信息
        if (orderInfo.value.items[0] && orderInfo.value.items[0].product) {
          productInfo.value = orderInfo.value.items[0].product
          console.log('从订单项中获取产品信息:', productInfo.value)
        }
      }

      // 检查订单状态
      if (orderInfo.value.status !== 'pending') {
        // 如果订单已支付，直接跳转到支付结果页面
        if (orderInfo.value.status === 'paid') {
          console.log('订单已支付，跳转到支付结果页面')
          router.replace({
            path: '/payment-result',
            query: { orderId: orderId.value }
          })
          return
        }
      }
    } else {
      error.value = result.message
      console.error('获取订单信息失败:', result.message)
    }
  } catch (err) {
    error.value = '加载订单信息时发生错误'
    console.error('加载订单信息错误:', err)
  } finally {
    loading.value = false
  }
}

// 获取产品名称列表
const getProductNames = () => {
  if (!orderInfo.value || !orderInfo.value.items) return ''

  return orderInfo.value.items
    .map(item => item.product?.name || `产品 ${item.product_id}`)
    .join(', ')
}

// 确认支付
const confirmPayment = async () => {
  if (!orderId.value) {
    ElMessage.error('订单信息不完整，无法完成支付')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要支付 ¥${orderInfo.value.total_amount.toFixed(2)} 吗？`,
      '确认支付',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    paying.value = true
    currentStep.value = 2

    // 模拟支付过程
    setTimeout(async () => {
      try {
        // 调用模拟支付成功接口
        const result = await orderStore.mockPaymentSuccess(orderId.value)
        console.log('模拟支付结果:', result)

        if (result.success) {
          currentStep.value = 3
          ElMessage.success('支付成功')

          console.log('支付成功，准备跳转到支付结果页面...')

          // 确保订单状态已更新
          if (result.orderStatus !== 'paid') {
            console.warn('订单状态未更新为已支付，等待后再次尝试')
            // 等待一段时间后再次检查
            await new Promise(resolve => setTimeout(resolve, 1500))
            await orderStore.fetchOrderById(orderId.value, true)
          }

          // 使用setTimeout确保状态更新后再跳转
          setTimeout(() => {
            // 立即跳转到支付结果页面
            router.push({
              path: '/payment-result',
              query: { orderId: orderId.value }
            })
          }, 500)
        } else {
          ElMessage.error(result.message || '支付失败')
          paying.value = false
        }
      } catch (err) {
        ElMessage.error('支付过程中发生错误')
        paying.value = false
        console.error('支付错误:', err)
      }
    }, 2000)
  } catch {
    // 用户取消操作
  }
}

// 取消支付
const cancelPayment = () => {
  ElMessageBox.confirm(
    '确定要取消支付吗？',
    '取消支付',
    {
      confirmButtonText: '确定',
      cancelButtonText: '返回支付',
      type: 'warning'
    }
  ).then(() => {
    // 跳转到支付结果页面，带上取消标志
    router.push({
      path: '/payment-result',
      query: {
        orderId: orderId.value,
        status: 'cancelled'
      }
    })
  }).catch(() => {
    // 用户取消操作
  })
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 组件挂载时获取订单信息
onMounted(() => {
  fetchOrderInfo()
})
</script>

<style scoped>
.payment-page {
  max-width: var(--container-max-width);
  margin: 0 auto;
}

.payment-container {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.payment-header {
  padding: var(--spacing-6);
  background: linear-gradient(135deg, #1677ff 0%, #0e5cda 100%);
  color: white;
}

.payment-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
}

.payment-logo img {
  height: 40px;
}

.payment-logo h1 {
  font-size: var(--font-size-2xl);
  margin: 0;
}

.payment-steps {
  margin-top: var(--spacing-4);
}

.payment-content {
  padding: var(--spacing-6);
}

.loading-container, .error-container {
  padding: var(--spacing-10) 0;
  text-align: center;
}

.loading-text {
  margin-top: var(--spacing-4);
  color: var(--text-secondary);
}

.order-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.order-card, .payment-methods-card {
  border-radius: var(--border-radius-lg);
  overflow: hidden;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-header h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

.order-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.order-info {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing-4);
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.info-item .label {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.info-item .value {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.order-items {
  margin-top: var(--spacing-4);
}

.order-items h3 {
  margin: 0 0 var(--spacing-4) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.item-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4);
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-3);
}

.item-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.item-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.item-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-1);
}

.price {
  font-weight: var(--font-weight-semibold);
  color: var(--accent-color);
}

.quantity {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.order-summary {
  margin-top: var(--spacing-6);
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--border-color);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-2);
  color: var(--text-secondary);
}

.summary-item.total {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-top: var(--spacing-3);
  padding-top: var(--spacing-3);
  border-top: 1px dashed var(--border-color);
}

.summary-item.total span:last-child {
  color: var(--accent-color);
}

.payment-methods {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.method-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-4);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all var(--transition-fast) ease;
}

.method-item.active {
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.05);
}

.method-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.method-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-4);
}

.method-icon img {
  max-width: 100%;
  max-height: 100%;
}

.method-icon .el-icon {
  font-size: 24px;
  color: var(--text-secondary);
}

.method-info {
  flex: 1;
}

.method-name {
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-1);
}

.method-desc {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.method-check {
  color: var(--primary-color);
  font-size: 20px;
}

.payment-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-4);
  margin-top: var(--spacing-6);
}

@media (max-width: 768px) {
  .payment-header {
    padding: var(--spacing-4);
  }

  .payment-content {
    padding: var(--spacing-4);
  }

  .order-info {
    grid-template-columns: 1fr;
  }

  .item-card {
    flex-direction: column;
    align-items: flex-start;
  }

  .item-price {
    margin-top: var(--spacing-2);
    align-items: flex-start;
  }

  .payment-actions {
    flex-direction: column-reverse;
    gap: var(--spacing-3);
  }

  .payment-actions button {
    width: 100%;
  }
}
</style>
