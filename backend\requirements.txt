# 文件路径: backend/requirements.txt

# Web框架和服务器
fastapi>=0.110.0
uvicorn[standard]>=0.27.0
starlette>=0.36.0

# 数据库相关
sqlalchemy>=2.0.0
asyncpg>=0.29.0  # 异步PostgreSQL驱动
psycopg2-binary>=2.9.9  # 同步PostgreSQL驱动
alembic>=1.13.0  # 数据库迁移
aiosqlite>=0.19.0  # 异步SQLite驱动

# 认证和安全
python-jose[cryptography]>=3.3.0  # JWT
passlib[bcrypt]==1.7.4  # 密码哈希，固定版本以避免 Python 3.13 中 crypt 模块移除带来的潜在问题
pydantic[email]>=2.6.0  # 数据验证
pydantic-settings>=2.1.0  # 配置管理

# 环境变量
python-dotenv>=1.0.0

# 支付宝SDK
python-alipay-sdk>=3.3.0

# 测试相关
pytest>=8.0.0
pytest-asyncio>=0.23.0
pytest-cov>=4.1.0
pytest-mock>=3.12.0
httpx>=0.27.0  # 异步HTTP客户端

# 其他工具
python-multipart>=0.0.9  # 表单处理
requests>=2.31.0  # HTTP客户端
email-validator>=2.1.0  # 邮箱验证
aiofiles>=23.2.1  # 异步文件操作
tenacity>=8.2.3  # 重试机制