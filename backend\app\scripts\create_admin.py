"""
创建管理员用户的脚本
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from app.core.config import settings
from app.core.security import get_password_hash
from app.models.user import User


async def create_admin():
    """创建管理员用户"""
    # 创建异步引擎
    if settings.DATABASE_URL.startswith("sqlite"):
        # SQLite连接
        sqlalchemy_url = settings.DATABASE_URL.replace(
            "sqlite:///", "sqlite+aiosqlite:///"
        )
    else:
        # PostgreSQL连接
        sqlalchemy_url = settings.DATABASE_URL.replace(
            "postgresql://", "postgresql+asyncpg://"
        )

    engine = create_async_engine(sqlalchemy_url)

    # 创建会话
    async_session = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )

    async with async_session() as session:
        # 检查管理员用户是否已存在
        result = await session.execute(
            select(User).where(User.username == settings.FIRST_SUPERUSER_USERNAME)
        )
        admin = result.scalars().first()

        if admin:
            print(f"管理员用户 '{settings.FIRST_SUPERUSER_USERNAME}' 已存在，更新密码")
            admin.password_hash = get_password_hash(settings.FIRST_SUPERUSER_PASSWORD)
            await session.commit()
            print(f"管理员用户 '{settings.FIRST_SUPERUSER_USERNAME}' 密码已更新")
            return

        # 创建管理员用户
        admin_user = User(
            username=settings.FIRST_SUPERUSER_USERNAME,
            email=settings.FIRST_SUPERUSER_EMAIL,
            password_hash=get_password_hash(settings.FIRST_SUPERUSER_PASSWORD),
            is_active=True,
            role="admin"
        )

        session.add(admin_user)
        await session.commit()

        print(f"管理员用户 '{settings.FIRST_SUPERUSER_USERNAME}' 创建成功")


if __name__ == "__main__":
    asyncio.run(create_admin())
