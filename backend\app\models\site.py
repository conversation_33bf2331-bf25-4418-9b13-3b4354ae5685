from sqlalchemy import Column, DateTime, Integer, String
from sqlalchemy.sql import func

from app.core.database import Base


class SiteInfo(Base):
    """
    站点信息数据模型

    Attributes:
        id: 站点信息ID
        company_name: 公司名称
        description: 站点描述
        contact_info: 联系信息
        logo_url: Logo URL
        created_at: 创建时间
        updated_at: 更新时间
    """
    __tablename__ = "site_info"

    id = Column(Integer, primary_key=True, index=True)
    company_name = Column(String(100), nullable=False)
    description = Column(String, nullable=True)
    contact_info = Column(String, nullable=True)
    logo_url = Column(String(255), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())