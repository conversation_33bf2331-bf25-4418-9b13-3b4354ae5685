#!/bin/bash

# 生产环境问题修复脚本
# 修复部署前发现的关键问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. 修复后端模型数据类型
fix_backend_models() {
    log_info "修复后端模型数据类型..."
    
    # 修复 Product 模型
    if [ -f "backend/app/models/product.py" ]; then
        sed -i 's/price = Column(Float, nullable=False)/price = Column(Numeric(10, 2), nullable=False)/' backend/app/models/product.py
        log_success "Product 模型价格字段已修复"
    fi
    
    # 修复 Order 模型
    if [ -f "backend/app/models/order.py" ]; then
        sed -i 's/total_amount = Column(Float(precision=2), nullable=False)/total_amount = Column(Numeric(10, 2), nullable=False)/' backend/app/models/order.py
        sed -i 's/price = Column(Float(precision=2), nullable=False)/price = Column(Numeric(10, 2), nullable=False)/' backend/app/models/order.py
        log_success "Order 模型金额字段已修复"
    fi
    
    # 添加必要的导入
    for file in backend/app/models/*.py; do
        if grep -q "Column.*Numeric" "$file" && ! grep -q "from sqlalchemy import.*Numeric" "$file"; then
            sed -i 's/from sqlalchemy import \(.*\)/from sqlalchemy import \1, Numeric/' "$file"
        fi
    done
    
    log_success "后端模型数据类型修复完成"
}

# 2. 创建环境变量文件
create_env_file() {
    log_info "创建生产环境配置文件..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            log_warning "已创建 .env 文件，请编辑并填入生产环境配置"
        else
            log_error ".env.example 文件不存在"
            return 1
        fi
    else
        log_info ".env 文件已存在"
    fi
    
    # 检查关键配置项
    if ! grep -q "SECRET_KEY=" .env || grep -q "your-super-secret" .env; then
        log_warning "请设置强随机的 SECRET_KEY"
    fi
    
    if ! grep -q "POSTGRES_PASSWORD=" .env || grep -q "password" .env; then
        log_warning "请设置强数据库密码"
    fi
    
    log_success "环境变量文件检查完成"
}

# 3. 修复 Docker 配置
fix_docker_config() {
    log_info "检查 Docker 配置..."
    
    # 检查生产环境配置文件
    if [ -f "docker-compose.prod.yml" ]; then
        log_success "生产环境 Docker 配置文件存在"
    else
        log_error "docker-compose.prod.yml 文件不存在"
        return 1
    fi
    
    # 检查 Dockerfile.prod 文件
    if [ ! -f "backend/Dockerfile.prod" ]; then
        log_info "创建后端生产环境 Dockerfile..."
        cat > backend/Dockerfile.prod << 'EOF'
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非 root 用户
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app
USER app

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
EOF
        log_success "后端生产环境 Dockerfile 已创建"
    fi
    
    if [ ! -f "frontend/Dockerfile.prod" ]; then
        log_info "创建前端生产环境 Dockerfile..."
        cat > frontend/Dockerfile.prod << 'EOF'
# 构建阶段
FROM node:18-alpine as build

WORKDIR /app

# 复制依赖文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产阶段
FROM nginx:alpine

# 复制构建结果
COPY --from=build /app/dist /usr/share/nginx/html

# 复制 Nginx 配置
COPY nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 启动 Nginx
CMD ["nginx", "-g", "daemon off;"]
EOF
        log_success "前端生产环境 Dockerfile 已创建"
    fi
    
    log_success "Docker 配置检查完成"
}

# 4. 创建 Nginx 配置
create_nginx_config() {
    log_info "创建 Nginx 配置..."
    
    mkdir -p nginx
    
    if [ ! -f "nginx/nginx.conf" ]; then
        cat > nginx/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    # 基础配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 上游后端服务
    upstream backend {
        server backend:8000;
    }
    
    server {
        listen 80;
        server_name _;
        
        # 前端静态文件
        location / {
            root /usr/share/nginx/html;
            index index.html;
            try_files $uri $uri/ /index.html;
        }
        
        # API 代理
        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # 健康检查
        location /health {
            proxy_pass http://backend/health;
        }
        
        # 静态文件缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
EOF
        log_success "Nginx 配置文件已创建"
    fi
}

# 5. 设置文件权限
fix_permissions() {
    log_info "修复文件权限..."
    
    # 设置脚本执行权限
    chmod +x scripts/*.sh
    
    # 设置密钥文件权限
    if [ -d "backend/keys" ]; then
        chmod 600 backend/keys/*.pem 2>/dev/null || true
        log_success "密钥文件权限已设置"
    fi
    
    log_success "文件权限修复完成"
}

# 6. 验证配置
validate_config() {
    log_info "验证配置..."
    
    # 检查 Docker Compose 配置
    if docker-compose -f docker-compose.prod.yml config > /dev/null 2>&1; then
        log_success "Docker Compose 配置有效"
    else
        log_error "Docker Compose 配置无效"
        return 1
    fi
    
    # 检查必要文件
    local required_files=(
        ".env"
        "docker-compose.prod.yml"
        "backend/Dockerfile.prod"
        "frontend/Dockerfile.prod"
        "nginx/nginx.conf"
        "database/init/01_init.sql"
        "database/init/02_production_fixes.sql"
    )
    
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            log_success "✓ $file"
        else
            log_error "✗ $file 缺失"
        fi
    done
    
    log_success "配置验证完成"
}

# 主函数
main() {
    log_info "开始修复生产环境问题..."
    
    fix_backend_models
    create_env_file
    fix_docker_config
    create_nginx_config
    fix_permissions
    validate_config
    
    log_success "生产环境问题修复完成！"
    echo ""
    echo "下一步："
    echo "1. 编辑 .env 文件，填入生产环境配置"
    echo "2. 将生产环境密钥文件放入 backend/keys/ 目录"
    echo "3. 运行部署脚本: ./scripts/deploy.sh"
    echo ""
    echo "重要提醒："
    echo "- 请使用强密码和随机密钥"
    echo "- 确保密钥文件安全"
    echo "- 在生产环境中启用 HTTPS"
}

# 错误处理
trap 'log_error "修复过程中发生错误"; exit 1' ERR

# 执行主函数
main "$@"
