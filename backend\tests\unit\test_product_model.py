"""
产品模型单元测试
"""
import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.product import Product
from app.schemas.product import ProductCreate, ProductUpdate
from app.services.product import product_service


@pytest.mark.asyncio
async def test_create_product(db_session: AsyncSession):
    """测试创建产品"""
    # 创建产品
    product_create = ProductCreate(
        name="测试产品",
        description="这是一个测试产品",
        price=100.00,
        product_type="software",
        stock=10,
    )
    product = await product_service.create_product(db_session, product_create)

    # 验证产品字段
    assert product.name == "测试产品"
    assert product.description == "这是一个测试产品"
    assert product.price == 100.00
    assert product.product_type == "software"
    assert product.stock == 10
    assert product.is_active is True


@pytest.mark.asyncio
async def test_get_product_by_id(db_session: AsyncSession):
    """测试通过ID获取产品"""
    # 创建产品
    product_create = ProductCreate(
        name="测试产品",
        description="这是一个测试产品",
        price=100.00,
        product_type="software",
        stock=10,
    )
    created_product = await product_service.create_product(db_session, product_create)

    # 获取产品
    product = await product_service.get_product_by_id(db_session, created_product.id)

    # 验证产品字段
    assert product is not None
    assert product.id == created_product.id
    assert product.name == "测试产品"
    assert product.description == "这是一个测试产品"
    assert product.price == 100.00
    assert product.product_type == "software"
    assert product.stock == 10
    assert product.is_active is True


@pytest.mark.asyncio
async def test_get_products(db_session: AsyncSession):
    """测试获取产品列表"""
    # 创建多个产品
    product_creates = [
        ProductCreate(
            name="软件产品1",
            description="这是一个软件产品",
            price=100.00,
            product_type="software",
            stock=10,
        ),
        ProductCreate(
            name="软件产品2",
            description="这是另一个软件产品",
            price=200.00,
            product_type="software",
            stock=20,
        ),
        ProductCreate(
            name="硬件产品1",
            description="这是一个硬件产品",
            price=300.00,
            product_type="hardware",
            stock=30,
        ),
    ]

    for product_create in product_creates:
        await product_service.create_product(db_session, product_create)

    # 获取所有产品
    products = await product_service.get_products(db_session)
    assert len(products) >= 3

    # 获取软件产品
    software_products = await product_service.get_products(
        db_session, product_type="software"
    )
    assert len(software_products) >= 2
    for product in software_products:
        assert product.product_type == "software"

    # 获取硬件产品
    hardware_products = await product_service.get_products(
        db_session, product_type="hardware"
    )
    assert len(hardware_products) >= 1
    for product in hardware_products:
        assert product.product_type == "hardware"


@pytest.mark.asyncio
async def test_update_product(db_session: AsyncSession):
    """测试更新产品"""
    # 创建产品
    product_create = ProductCreate(
        name="测试产品",
        description="这是一个测试产品",
        price=100.00,
        product_type="software",
        stock=10,
    )
    created_product = await product_service.create_product(db_session, product_create)

    # 更新产品
    product_update = ProductUpdate(
        name="更新后的产品",
        description="这是更新后的产品描述",
        price=200.00,
        stock=20,
    )
    updated_product = await product_service.update_product(
        db_session, created_product.id, product_update
    )

    # 验证更新后的产品字段
    assert updated_product is not None
    assert updated_product.id == created_product.id
    assert updated_product.name == "更新后的产品"
    assert updated_product.description == "这是更新后的产品描述"
    assert updated_product.price == 200.00
    assert updated_product.product_type == "software"  # 未更新
    assert updated_product.stock == 20
    assert updated_product.is_active is True  # 未更新


@pytest.mark.asyncio
async def test_toggle_product_status(db_session: AsyncSession):
    """测试切换产品状态"""
    # 创建产品
    product_create = ProductCreate(
        name="测试产品",
        description="这是一个测试产品",
        price=100.00,
        product_type="software",
        stock=10,
    )
    created_product = await product_service.create_product(db_session, product_create)
    assert created_product.is_active is True

    # 禁用产品
    disabled_product = await product_service.toggle_product_status(
        db_session, created_product.id, False
    )
    assert disabled_product is not None
    assert disabled_product.id == created_product.id
    assert disabled_product.is_active is False

    # 启用产品
    enabled_product = await product_service.toggle_product_status(
        db_session, created_product.id, True
    )
    assert enabled_product is not None
    assert enabled_product.id == created_product.id
    assert enabled_product.is_active is True
