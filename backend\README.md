# 软件和设备销售平台 - 后端

本目录包含软件和设备销售平台的后端代码，基于FastAPI和SQLAlchemy开发。

## 技术栈

- **FastAPI**: 高性能的Python Web框架
- **SQLAlchemy**: ORM框架
- **PostgreSQL/SQLite**: 数据库
- **JWT**: 身份验证
- **Alipay SDK**: 支付宝支付集成
- **Pytest**: 测试框架

## 目录结构

```
backend/
├── app/                # 应用代码
│   ├── api/            # API路由
│   │   ├── auth.py     # 认证相关API
│   │   ├── user.py     # 用户相关API
│   │   ├── product.py  # 产品相关API
│   │   ├── order.py    # 订单相关API
│   │   ├── payment.py  # 支付相关API
│   │   └── admin.py    # 管理员相关API
│   ├── core/           # 核心模块
│   │   ├── config.py   # 配置
│   │   ├── database.py # 数据库连接
│   │   ├── security.py # 安全相关
│   │   ├── schemas.py  # 共享Pydantic模型
│   │   └── dependencies.py # 依赖函数
│   ├── models/         # 数据模型
│   │   ├── user.py     # 用户模型
│   │   ├── product.py  # 产品模型
│   │   ├── order.py    # 订单模型
│   │   └── payment.py  # 支付模型
│   ├── schemas/        # Pydantic模型
│   │   ├── user.py     # 用户模式
│   │   ├── product.py  # 产品模式
│   │   ├── order.py    # 订单模式
│   │   └── payment.py  # 支付模式
│   ├── services/       # 业务逻辑
│   │   ├── user.py     # 用户服务
│   │   ├── product.py  # 产品服务
│   │   ├── order.py    # 订单服务
│   │   └── payment.py  # 支付服务
│   ├── static/         # 静态文件
│   └── main.py         # 应用入口
├── tests/              # 测试代码
│   ├── conftest.py     # 测试配置
│   ├── unit/           # 单元测试
│   └── integration/    # 集成测试
├── alembic/            # 数据库迁移
├── Dockerfile          # Docker配置
├── requirements.txt    # Python依赖
└── pytest.ini          # Pytest配置
```

## 环境要求

- Python 3.12+
- PostgreSQL 16+ (生产环境)
- SQLite (开发/测试环境)

## 本地开发

### 设置虚拟环境

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境 (Windows)
venv\Scripts\activate

# 激活虚拟环境 (Linux/macOS)
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 环境变量

创建`.env`文件，包含以下环境变量：

```
DATABASE_URL=postgresql://user:password@localhost:5432/sales_platform
SECRET_KEY=your_secret_key_here
ACCESS_TOKEN_EXPIRE_MINUTES=1440
ENVIRONMENT=development
ALIPAY_APP_ID=your_alipay_app_id
ALIPAY_PRIVATE_KEY_PATH=path/to/private_key.pem
ALIPAY_PUBLIC_KEY_PATH=path/to/public_key.pem
ALIPAY_NOTIFY_URL=http://localhost:8000/api/v1/payments/notify
ALIPAY_RETURN_URL=http://localhost:5173/payment-result
```

### 运行开发服务器

```bash
# 直接运行
uvicorn app.main:app --reload

# 或者使用Python
python -m uvicorn app.main:app --reload
```

### 数据库迁移

```bash
# 初始化Alembic
alembic init alembic

# 创建迁移
alembic revision --autogenerate -m "Initial migration"

# 应用迁移
alembic upgrade head
```

## API文档

启动服务器后，可以访问以下URL查看API文档：

- Swagger UI: http://localhost:8000/api/v1/docs
- ReDoc: http://localhost:8000/api/v1/redoc

## 测试

### 使用Docker容器运行测试（推荐）

项目已配置好Docker容器化测试环境，这是推荐的测试方式，可以确保测试环境一致：

```bash
# 在项目根目录运行后端测试
docker-compose run --rm backend_tests

# 运行特定测试文件
docker-compose run --rm backend_tests pytest tests/test_auth.py

# 生成测试覆盖率报告
docker-compose run --rm backend_tests pytest --cov=app
```

### 本地运行测试（可选）

如果您已经设置了本地开发环境，也可以直接在本地运行测试：

```bash
# 激活虚拟环境后运行所有测试
pytest

# 运行特定测试
pytest tests/test_auth.py

# 生成测试覆盖率报告
pytest --cov=app
```

注意：本地测试需要正确设置环境变量和依赖，可能与Docker环境有差异。

## Docker部署

```bash
# 构建镜像
docker build -t sales-platform-backend .

# 运行容器
docker run -p 8000:8000 --env-file .env sales-platform-backend
```

## 使用Docker Compose（推荐）

项目已配置好完整的Docker开发环境，这是推荐的开发方式，可以确保环境一致性：

```bash
# 在项目根目录启动所有服务
docker-compose up -d

# 查看后端日志
docker-compose logs -f backend

# 运行后端测试
docker-compose run --rm backend_tests

# 停止所有服务
docker-compose down
```

### 常用Docker Compose命令

```bash
# 重新构建服务（代码或依赖变更后）
docker-compose build backend

# 重启单个服务
docker-compose restart backend

# 查看所有容器状态
docker-compose ps
```

访问应用：
- 后端API文档：http://localhost:8000/api/v1/docs
- 前端应用：http://localhost:5173

## 主要API端点

### 认证

- `POST /api/v1/auth/login`: 用户登录
- `POST /api/v1/auth/refresh`: 刷新令牌

### 用户

- `POST /api/v1/users`: 创建用户
- `GET /api/v1/users/me`: 获取当前用户信息
- `PUT /api/v1/users/me`: 更新当前用户信息

### 产品

- `GET /api/v1/products`: 获取产品列表
- `GET /api/v1/products/{id}`: 获取产品详情

### 订单

- `POST /api/v1/orders`: 创建订单
- `GET /api/v1/orders`: 获取当前用户订单列表
- `GET /api/v1/orders/{id}`: 获取订单详情

### 支付

- `POST /api/v1/payments/initiate/{order_id}`: 发起支付
- `GET /api/v1/payments/status/{order_id}`: 查询支付状态
- `POST /api/v1/payments/notify`: 支付宝异步通知

### 管理员

- `GET /api/v1/admin/users`: 获取所有用户
- `GET /api/v1/admin/orders`: 获取所有订单
- `POST /api/v1/admin/products`: 创建产品
- `PUT /api/v1/admin/products/{id}`: 更新产品

## 贡献指南

1. Fork仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request
