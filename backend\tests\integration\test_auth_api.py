"""
认证API集成测试
"""
import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import User


@pytest.mark.asyncio
async def test_register_user(client: AsyncClient):
    """测试注册用户"""
    # 注册用户
    response = await client.post(
        "/api/v1/users/",
        json={
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "password123"
        }
    )
    
    # 验证响应
    assert response.status_code == 200
    data = response.json()
    assert data["username"] == "newuser"
    assert data["email"] == "<EMAIL>"
    assert "password" not in data
    assert data["is_active"] is True
    assert data["role"] == "user"
    assert "id" in data
    assert "created_at" in data


@pytest.mark.asyncio
async def test_register_user_duplicate_username(client: AsyncClient, test_user: User):
    """测试注册用户（用户名重复）"""
    # 注册用户
    response = await client.post(
        "/api/v1/users/",
        json={
            "username": test_user.username,
            "email": "<EMAIL>",
            "password": "password123"
        }
    )
    
    # 验证响应
    assert response.status_code == 400
    data = response.json()
    assert data["detail"] == "用户名已存在"


@pytest.mark.asyncio
async def test_register_user_duplicate_email(client: AsyncClient, test_user: User):
    """测试注册用户（邮箱重复）"""
    # 注册用户
    response = await client.post(
        "/api/v1/users/",
        json={
            "username": "anotheruser",
            "email": test_user.email,
            "password": "password123"
        }
    )
    
    # 验证响应
    assert response.status_code == 400
    data = response.json()
    assert data["detail"] == "邮箱已存在"


@pytest.mark.asyncio
async def test_login_user(client: AsyncClient, test_user: User):
    """测试登录用户"""
    # 登录用户
    response = await client.post(
        "/api/v1/auth/login",
        data={
            "username": test_user.username,
            "password": "password123"
        }
    )
    
    # 验证响应
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"


@pytest.mark.asyncio
async def test_login_user_wrong_password(client: AsyncClient, test_user: User):
    """测试登录用户（密码错误）"""
    # 登录用户
    response = await client.post(
        "/api/v1/auth/login",
        data={
            "username": test_user.username,
            "password": "wrongpassword"
        }
    )
    
    # 验证响应
    assert response.status_code == 401
    data = response.json()
    assert data["detail"] == "用户名或密码不正确"


@pytest.mark.asyncio
async def test_login_user_not_found(client: AsyncClient):
    """测试登录用户（用户不存在）"""
    # 登录用户
    response = await client.post(
        "/api/v1/auth/login",
        data={
            "username": "nonexistentuser",
            "password": "password123"
        }
    )
    
    # 验证响应
    assert response.status_code == 401
    data = response.json()
    assert data["detail"] == "用户名或密码不正确"


@pytest.mark.asyncio
async def test_get_current_user(client: AsyncClient, test_user: User, user_headers: dict):
    """测试获取当前用户"""
    # 获取当前用户
    response = await client.get(
        "/api/v1/users/me",
        headers=user_headers
    )
    
    # 验证响应
    assert response.status_code == 200
    data = response.json()
    assert data["username"] == test_user.username
    assert data["email"] == test_user.email
    assert data["id"] == test_user.id
    assert data["is_active"] is True
    assert data["role"] == test_user.role


@pytest.mark.asyncio
async def test_get_current_user_unauthorized(client: AsyncClient):
    """测试获取当前用户（未授权）"""
    # 获取当前用户
    response = await client.get("/api/v1/users/me")
    
    # 验证响应
    assert response.status_code == 401
    data = response.json()
    assert data["detail"] == "未提供认证凭证"


@pytest.mark.asyncio
async def test_update_current_user(client: AsyncClient, test_user: User, user_headers: dict):
    """测试更新当前用户"""
    # 更新当前用户
    response = await client.put(
        "/api/v1/users/me",
        headers=user_headers,
        json={
            "address": "123 Test St"
        }
    )
    
    # 验证响应
    assert response.status_code == 200
    data = response.json()
    assert data["username"] == test_user.username
    assert data["email"] == test_user.email
    assert data["address"] == "123 Test St"


@pytest.mark.asyncio
async def test_update_current_user_username(client: AsyncClient, test_user: User, user_headers: dict):
    """测试更新当前用户用户名"""
    # 更新当前用户
    response = await client.put(
        "/api/v1/users/me",
        headers=user_headers,
        json={
            "username": "newusername"
        }
    )
    
    # 验证响应
    assert response.status_code == 200
    data = response.json()
    assert data["username"] == "newusername"
    assert data["email"] == test_user.email


@pytest.mark.asyncio
async def test_update_current_user_email(client: AsyncClient, test_user: User, user_headers: dict):
    """测试更新当前用户邮箱"""
    # 更新当前用户
    response = await client.put(
        "/api/v1/users/me",
        headers=user_headers,
        json={
            "email": "<EMAIL>"
        }
    )
    
    # 验证响应
    assert response.status_code == 200
    data = response.json()
    assert data["username"] == test_user.username
    assert data["email"] == "<EMAIL>"


@pytest.mark.asyncio
async def test_update_current_user_duplicate_username(
    client: AsyncClient, test_user: User, admin_user: User, user_headers: dict
):
    """测试更新当前用户用户名（用户名重复）"""
    # 更新当前用户
    response = await client.put(
        "/api/v1/users/me",
        headers=user_headers,
        json={
            "username": admin_user.username
        }
    )
    
    # 验证响应
    assert response.status_code == 400
    data = response.json()
    assert data["detail"] == "用户名已存在"


@pytest.mark.asyncio
async def test_update_current_user_duplicate_email(
    client: AsyncClient, test_user: User, admin_user: User, user_headers: dict
):
    """测试更新当前用户邮箱（邮箱重复）"""
    # 更新当前用户
    response = await client.put(
        "/api/v1/users/me",
        headers=user_headers,
        json={
            "email": admin_user.email
        }
    )
    
    # 验证响应
    assert response.status_code == 400
    data = response.json()
    assert data["detail"] == "邮箱已存在"


@pytest.mark.asyncio
async def test_get_users_admin(client: AsyncClient, admin_headers: dict):
    """测试获取用户列表（管理员）"""
    # 获取用户列表
    response = await client.get(
        "/api/v1/users/",
        headers=admin_headers
    )
    
    # 验证响应
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) >= 2


@pytest.mark.asyncio
async def test_get_users_not_admin(client: AsyncClient, user_headers: dict):
    """测试获取用户列表（非管理员）"""
    # 获取用户列表
    response = await client.get(
        "/api/v1/users/",
        headers=user_headers
    )
    
    # 验证响应
    assert response.status_code == 403
    data = response.json()
    assert data["detail"] == "权限不足"
