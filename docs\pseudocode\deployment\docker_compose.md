# Docker Compose 配置伪代码

## 1. 开发环境 (docker-compose.yml)

```yaml
version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:8000/api/v1
    depends_on:
      - backend
    networks:
      - app-network

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    volumes:
      - ./backend:/app
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=sqlite:///./app.db
      - SECRET_KEY=dev_secret_key
      - ALIPAY_APP_ID=your_dev_alipay_app_id
      - ALIPAY_PRIVATE_KEY_PATH=/app/certs/dev_private_key.pem
      - ALIPAY_PUBLIC_KEY_PATH=/app/certs/dev_alipay_public_key.pem
      - ALIPAY_NOTIFY_URL=http://localhost:8000/api/v1/payments/notify
      - ALIPAY_RETURN_URL=http://localhost:3000/payment-result
    depends_on:
      - db
    networks:
      - app-network

  # 数据库服务（开发环境使用SQLite，此处为PostgreSQL容器，用于测试）
  db:
    image: postgres:16-alpine
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=app
    ports:
      - "5432:5432"
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  postgres_data:
```

## 2. 生产环境 (docker-compose.prod.yml)

```yaml
version: '3.8'

services:
  # Nginx服务（前端静态文件 + 反向代理）
  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - ./frontend/dist:/usr/share/nginx/html
    depends_on:
      - backend
    networks:
      - app-network
    restart: always

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    expose:
      - "8000"
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=**************************************/app
      - SECRET_KEY=${SECRET_KEY}
      - ALIPAY_APP_ID=${ALIPAY_APP_ID}
      - ALIPAY_PRIVATE_KEY_PATH=/app/certs/private_key.pem
      - ALIPAY_PUBLIC_KEY_PATH=/app/certs/alipay_public_key.pem
      - ALIPAY_NOTIFY_URL=${ALIPAY_NOTIFY_URL}
      - ALIPAY_RETURN_URL=${ALIPAY_RETURN_URL}
    volumes:
      - ./certs:/app/certs
    depends_on:
      - db
    networks:
      - app-network
    restart: always

  # 数据库服务
  db:
    image: postgres:16-alpine
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=app
    networks:
      - app-network
    restart: always

networks:
  app-network:
    driver: bridge

volumes:
  postgres_data:
```

## 3. 前端Dockerfile (frontend/Dockerfile.dev)

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package.json package-lock.json ./

RUN npm install

COPY . .

EXPOSE 3000

CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
```

## 4. 前端生产Dockerfile (frontend/Dockerfile)

```dockerfile
# 构建阶段
FROM node:18-alpine as build

WORKDIR /app

COPY package.json package-lock.json ./

RUN npm install

COPY . .

RUN npm run build

# 生产阶段
FROM nginx:alpine

COPY --from=build /app/dist /usr/share/nginx/html

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

## 5. 后端Dockerfile (backend/Dockerfile.dev)

```dockerfile
FROM python:3.12-slim

WORKDIR /app

COPY requirements.txt .

RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
```

## 6. 后端生产Dockerfile (backend/Dockerfile.prod)

```dockerfile
FROM python:3.12-slim

WORKDIR /app

COPY requirements.txt .

RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
```

## 7. Nginx配置 (nginx/conf/default.conf)

```nginx
server {
    listen 80;
    server_name _;
    
    # 重定向到HTTPS
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name _;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    # 前端静态文件
    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # 后端API代理
    location /api/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://backend:8000/health;
    }
}
```
