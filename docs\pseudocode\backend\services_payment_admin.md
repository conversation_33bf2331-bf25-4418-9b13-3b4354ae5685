# 支付和管理员服务伪代码

## 1. 支付服务 (services/payment.py)

```python
# 支付服务
class PaymentService:
    # 创建新订单
    async def create_order(self, db: AsyncSession, user_id: int, order_create: OrderCreate) -> Order:
        # 检查产品是否存在
        product = await product_service.get_product_by_id(db, order_create.product_id)
        if not product or not product.is_active:
            raise HTTPException(status_code=404, detail="产品不存在或已下架")
        
        # 生成订单号
        order_number = f"ORD-{int(time.time())}-{user_id}-{random.randint(1000, 9999)}"
        
        # 创建订单
        db_order = Order(
            order_number=order_number,
            user_id=user_id,
            product_id=order_create.product_id,
            status="pending"
        )
        
        db.add(db_order)
        await db.commit()
        await db.refresh(db_order)
        return db_order
    
    # 生成支付链接
    async def generate_payment_url(self, db: AsyncSession, order_id: int) -> str:
        # 获取订单信息
        order = await self.get_order_by_id(db, order_id)
        if not order:
            raise HTTPException(status_code=404, detail="订单不存在")
        
        if order.status != "pending":
            raise HTTPException(status_code=400, detail="订单状态不正确")
        
        # 获取产品信息
        product = await product_service.get_product_by_id(db, order.product_id)
        
        # 创建支付宝客户端
        alipay_client = AliPay(
            appid=settings.ALIPAY_APP_ID,
            app_notify_url=settings.ALIPAY_NOTIFY_URL,
            app_private_key_string=open(settings.ALIPAY_PRIVATE_KEY_PATH).read(),
            alipay_public_key_string=open(settings.ALIPAY_PUBLIC_KEY_PATH).read(),
            sign_type="RSA2",
            debug=False
        )
        
        # 生成支付链接
        order_string = alipay_client.api_alipay_trade_page_pay(
            out_trade_no=order.order_number,
            total_amount=str(product.price),
            subject=f"购买 {product.name}",
            return_url=settings.ALIPAY_RETURN_URL,
            notify_url=settings.ALIPAY_NOTIFY_URL
        )
        
        return f"https://openapi.alipay.com/gateway.do?{order_string}"
    
    # 处理支付回调
    async def handle_payment_notification(self, db: AsyncSession, data: dict) -> bool:
        # 创建支付宝客户端
        alipay_client = AliPay(
            appid=settings.ALIPAY_APP_ID,
            app_notify_url=settings.ALIPAY_NOTIFY_URL,
            app_private_key_string=open(settings.ALIPAY_PRIVATE_KEY_PATH).read(),
            alipay_public_key_string=open(settings.ALIPAY_PUBLIC_KEY_PATH).read(),
            sign_type="RSA2",
            debug=False
        )
        
        # 验证签名
        signature = data.pop("sign", None)
        if not alipay_client.verify(data, signature):
            return False
        
        # 获取订单信息
        order_number = data.get("out_trade_no")
        trade_status = data.get("trade_status")
        
        if trade_status == "TRADE_SUCCESS":
            # 更新订单状态
            order = await self.get_order_by_order_number(db, order_number)
            if order and order.status == "pending":
                # 生成软件密钥（如果是软件产品）
                product = await product_service.get_product_by_id(db, order.product_id)
                if product.type == "software":
                    license_key = self.generate_license_key()
                    order.license_key = license_key
                
                order.status = "paid"
                await db.commit()
                return True
        
        return False
    
    # 生成软件密钥
    def generate_license_key(self) -> str:
        # 生成随机密钥
        key_parts = []
        for _ in range(5):
            part = ''.join(random.choices(string.ascii_uppercase + string.digits, k=5))
            key_parts.append(part)
        
        return '-'.join(key_parts)
    
    # 通过ID获取订单
    async def get_order_by_id(self, db: AsyncSession, order_id: int) -> Optional[Order]:
        result = await db.execute(
            select(Order).where(Order.id == order_id)
        )
        return result.scalars().first()
    
    # 通过订单号获取订单
    async def get_order_by_order_number(self, db: AsyncSession, order_number: str) -> Optional[Order]:
        result = await db.execute(
            select(Order).where(Order.order_number == order_number)
        )
        return result.scalars().first()
    
    # 获取用户订单
    async def get_user_orders(self, db: AsyncSession, user_id: int) -> List[Order]:
        result = await db.execute(
            select(Order)
            .options(joinedload(Order.product))
            .where(Order.user_id == user_id)
            .order_by(Order.created_at.desc())
        )
        return result.scalars().all()

# 创建支付服务实例
payment_service = PaymentService()
```

## 2. 管理员服务 (services/admin.py)

```python
# 管理员服务
class AdminService:
    # 获取站点信息
    async def get_site_info(self, db: AsyncSession) -> Optional[SiteInfo]:
        result = await db.execute(select(SiteInfo).limit(1))
        return result.scalars().first()
    
    # 更新站点信息
    async def update_site_info(self, db: AsyncSession, site_info_update: SiteInfoUpdate) -> SiteInfo:
        # 获取现有站点信息
        site_info = await self.get_site_info(db)
        
        # 如果不存在，则创建新的
        if not site_info:
            site_info = SiteInfo(**site_info_update.dict(exclude_unset=False))
            db.add(site_info)
        else:
            # 更新现有站点信息
            update_data = site_info_update.dict(exclude_unset=True)
            for key, value in update_data.items():
                setattr(site_info, key, value)
        
        await db.commit()
        await db.refresh(site_info)
        return site_info
    
    # 获取销售统计
    async def get_sales_statistics(self, db: AsyncSession) -> dict:
        # 获取软件销售数量
        software_sales = await product_service.get_product_sales_count(db, product_type="software")
        
        # 获取设备销售数量
        device_sales = await product_service.get_product_sales_count(db, product_type="device")
        
        # 获取用户总数
        user_count = await user_service.get_user_count(db)
        
        return {
            "software_sales": software_sales,
            "device_sales": device_sales,
            "user_count": user_count
        }
    
    # 获取所有订单
    async def get_all_orders(self, db: AsyncSession, skip: int = 0, limit: int = 100) -> List[Order]:
        result = await db.execute(
            select(Order)
            .options(joinedload(Order.product), joinedload(Order.user))
            .order_by(Order.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

# 创建管理员服务实例
admin_service = AdminService()
```
