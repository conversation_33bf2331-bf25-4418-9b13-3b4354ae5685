<template>
  <PageContainer customClass="admin-page">
    <div class="admin-site-info">
      <div class="page-header">
        <div class="header-content">
          <h1 class="page-title">站点信息管理</h1>
          <p class="page-subtitle">管理站点基本信息和配置</p>
        </div>
      </div>
      
      <el-row :gutter="24">
        <el-col :xs="24" :lg="12">
          <!-- 站点信息表单 -->
          <el-card shadow="never" class="site-info-card">
            <template #header>
              <div class="card-header">
                <h2 class="section-title">站点信息设置</h2>
                <div class="header-actions">
                  <AppButton 
                    type="primary" 
                    @click="saveSiteInfo" 
                    :loading="saving"
                    round
                  >
                    <el-icon><Check /></el-icon>
                    保存设置
                  </AppButton>
                  <AppButton 
                    @click="resetForm"
                    round
                  >
                    <el-icon><RefreshRight /></el-icon>
                    重置
                  </AppButton>
                </div>
              </div>
            </template>

            <div v-if="loading" class="loading-container">
              <el-skeleton :rows="6" animated />
            </div>
            <el-form
              v-else
              ref="siteInfoFormRef"
              :model="siteInfoForm"
              :rules="siteInfoRules"
              label-position="top"
              class="site-form"
            >
              <el-form-item label="公司名称" prop="company_name">
                <el-input 
                  v-model="siteInfoForm.company_name" 
                  placeholder="请输入公司名称"
                  maxlength="100"
                  show-word-limit
                />
              </el-form-item>

              <el-form-item label="站点描述" prop="description">
                <el-input
                  v-model="siteInfoForm.description"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入站点描述"
                  maxlength="1000"
                  show-word-limit
                />
                <div class="form-tip">
                  站点描述将显示在首页和搜索引擎结果中，建议简洁明了地描述您的业务
                </div>
              </el-form-item>

              <el-form-item label="联系信息" prop="contact_info">
                <el-input
                  v-model="siteInfoForm.contact_info"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入联系信息"
                  maxlength="500"
                  show-word-limit
                />
                <div class="form-tip">
                  联系信息将显示在页脚和联系页面，可以包含电话、邮箱、地址等
                </div>
              </el-form-item>

              <el-form-item label="Logo URL" prop="logo_url">
                <el-input 
                  v-model="siteInfoForm.logo_url" 
                  placeholder="请输入Logo URL"
                  maxlength="255"
                  show-word-limit
                >
                  <template #append>
                    <el-button @click="previewLogo">
                      <el-icon><View /></el-icon>
                    </el-button>
                  </template>
                </el-input>
                <div class="form-tip">
                  输入Logo图片的URL地址，建议使用HTTPS链接，推荐尺寸：200x60像素
                </div>
              </el-form-item>
              
              <el-form-item label="版权信息" prop="copyright">
                <el-input 
                  v-model="siteInfoForm.copyright" 
                  placeholder="请输入版权信息"
                  maxlength="200"
                  show-word-limit
                />
                <div class="form-tip">
                  版权信息将显示在页脚，例如：© 2025 软件和设备销售平台 版权所有
                </div>
              </el-form-item>
              
              <el-form-item label="备案信息" prop="icp">
                <el-input 
                  v-model="siteInfoForm.icp" 
                  placeholder="请输入备案信息"
                  maxlength="50"
                  show-word-limit
                />
                <div class="form-tip">
                  如果您的网站已备案，请填写备案号，将显示在页脚
                </div>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>

        <el-col :xs="24" :lg="12">
          <!-- 站点信息预览 -->
          <el-card shadow="never" class="site-preview-card">
            <template #header>
              <div class="card-header">
                <h2 class="section-title">预览效果</h2>
                <el-tag type="info" effect="plain">实时预览</el-tag>
              </div>
            </template>

            <div v-if="loading" class="loading-container">
              <el-skeleton :rows="10" animated />
            </div>
            <div v-else class="site-preview">
              <!-- 导航栏预览 -->
              <div class="preview-navbar">
                <div class="preview-logo-container">
                  <div v-if="siteInfoForm.logo_url" class="preview-logo">
                    <img 
                      :src="siteInfoForm.logo_url" 
                      alt="Logo" 
                      @error="handleLogoError" 
                    />
                  </div>
                  <div v-else class="preview-logo-placeholder">
                    <el-icon><Picture /></el-icon>
                  </div>
                  <div class="preview-site-name">
                    {{ siteInfoForm.company_name || '公司名称' }}
                  </div>
                </div>
                <div class="preview-nav-links">
                  <div class="preview-nav-item active">首页</div>
                  <div class="preview-nav-item">产品</div>
                  <div class="preview-nav-item">关于我们</div>
                  <div class="preview-nav-item">联系我们</div>
                </div>
              </div>
              
              <!-- 内容预览 -->
              <div class="preview-content">
                <div class="preview-section">
                  <h3 class="preview-section-title">站点描述</h3>
                  <div class="preview-description">
                    {{ siteInfoForm.description || '暂无描述信息' }}
                  </div>
                </div>
                
                <div class="preview-section">
                  <h3 class="preview-section-title">联系方式</h3>
                  <div class="preview-contact">
                    {{ siteInfoForm.contact_info || '暂无联系信息' }}
                  </div>
                </div>
              </div>
              
              <!-- 页脚预览 -->
              <div class="preview-footer">
                <div class="preview-footer-content">
                  <div class="preview-copyright">
                    {{ siteInfoForm.copyright || '© 2025 公司名称 版权所有' }}
                  </div>
                  <div v-if="siteInfoForm.icp" class="preview-icp">
                    {{ siteInfoForm.icp }}
                  </div>
                </div>
              </div>
            </div>
          </el-card>
          
          <!-- SEO设置卡片 -->
          <el-card shadow="never" class="seo-card">
            <template #header>
              <div class="card-header">
                <h2 class="section-title">SEO设置</h2>
                <el-tag type="success" effect="plain">即将推出</el-tag>
              </div>
            </template>
            
            <div class="coming-soon">
              <el-empty description="SEO设置功能即将推出">
                <template #image>
                  <el-icon class="coming-soon-icon"><Connection /></el-icon>
                </template>
                <template #description>
                  <p>SEO设置功能即将推出，敬请期待！</p>
                  <p class="coming-soon-desc">该功能将支持设置网站标题、关键词、描述等元数据，帮助提升网站在搜索引擎中的排名。</p>
                </template>
              </el-empty>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- Logo预览对话框 -->
    <el-dialog
      v-model="logoPreviewVisible"
      title="Logo预览"
      width="400px"
      center
    >
      <div class="logo-preview-container">
        <div v-if="siteInfoForm.logo_url" class="logo-preview">
          <img 
            :src="siteInfoForm.logo_url" 
            alt="Logo预览" 
            @error="handleLogoPreviewError" 
          />
        </div>
        <el-empty v-else description="请输入有效的Logo URL">
          <template #image>
            <el-icon class="empty-logo-icon"><Picture /></el-icon>
          </template>
        </el-empty>
      </div>
    </el-dialog>
  </PageContainer>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Check, 
  RefreshRight, 
  Picture, 
  View, 
  Connection 
} from '@element-plus/icons-vue'
import { useUserStore, useSiteStore } from '../../store'
import api from '../../api'
import PageContainer from '../../components/layout/PageContainer.vue'

const router = useRouter()
const userStore = useUserStore()
const siteStore = useSiteStore()

// 检查是否是管理员
onMounted(() => {
  if (!userStore.isAdmin) {
    ElMessage.error('您没有权限访问此页面')
    router.push('/')
  }
  fetchSiteInfo()
})

// 站点信息数据
const loading = ref(false)
const saving = ref(false)
const siteInfoFormRef = ref(null)
const originalSiteInfo = ref({})
const logoPreviewVisible = ref(false)

const siteInfoForm = reactive({
  company_name: '',
  description: '',
  contact_info: '',
  logo_url: '',
  copyright: '',
  icp: ''
})

// 表单验证规则
const siteInfoRules = {
  company_name: [
    { required: true, message: '请输入公司名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度应在2到100个字符之间', trigger: 'blur' }
  ],
  description: [
    { max: 1000, message: '长度不能超过1000个字符', trigger: 'blur' }
  ],
  contact_info: [
    { max: 500, message: '长度不能超过500个字符', trigger: 'blur' }
  ],
  logo_url: [
    { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
  ],
  copyright: [
    { max: 200, message: '长度不能超过200个字符', trigger: 'blur' }
  ],
  icp: [
    { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
  ]
}

// 获取站点信息
const fetchSiteInfo = async () => {
  loading.value = true
  try {
    const response = await api.site.getSiteInfo()
    if (response.data.success) {
      const siteInfo = response.data.data
      // 保存原始数据，用于重置表单
      originalSiteInfo.value = { ...siteInfo }
      // 更新表单数据
      Object.assign(siteInfoForm, siteInfo)
      
      // 添加默认值
      if (!siteInfoForm.copyright) {
        siteInfoForm.copyright = `© ${new Date().getFullYear()} ${siteInfoForm.company_name} 版权所有`
      }
    } else {
      ElMessage.error(response.data.message || '获取站点信息失败')
    }
  } catch (error) {
    console.error('获取站点信息失败:', error)
    ElMessage.error('获取站点信息失败')
  } finally {
    loading.value = false
  }
}

// 保存站点信息
const saveSiteInfo = async () => {
  if (!siteInfoFormRef.value) return

  await siteInfoFormRef.value.validate(async (valid) => {
    if (!valid) return

    saving.value = true
    try {
      const response = await api.admin.updateSiteInfo(siteInfoForm)
      if (response.data.success) {
        ElMessage.success('站点信息保存成功')
        // 更新原始数据
        originalSiteInfo.value = { ...siteInfoForm }
        // 更新全局站点信息
        await siteStore.fetchSiteInfo()
      } else {
        ElMessage.error(response.data.message || '保存站点信息失败')
      }
    } catch (error) {
      console.error('保存站点信息失败:', error)
      ElMessage.error('保存站点信息失败')
    } finally {
      saving.value = false
    }
  })
}

// 重置表单
const resetForm = () => {
  Object.assign(siteInfoForm, originalSiteInfo.value)
  ElMessage.info('表单已重置')
}

// 预览Logo
const previewLogo = () => {
  logoPreviewVisible.value = true
}

// 处理Logo加载错误
const handleLogoError = (e) => {
  e.target.src = ''
  e.target.classList.add('logo-error')
  e.target.parentNode.classList.add('logo-error-container')
}

// 处理Logo预览加载错误
const handleLogoPreviewError = (e) => {
  e.target.src = ''
  e.target.classList.add('logo-preview-error')
  e.target.parentNode.classList.add('logo-preview-error-container')
}
</script>

<style scoped>
.admin-page {
  max-width: var(--container-max-width);
  margin: 0 auto;
}

.admin-site-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.page-header {
  margin-bottom: var(--spacing-4);
}

.page-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0;
}

.page-subtitle {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin: var(--spacing-2) 0 0 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: var(--spacing-3);
}

.site-info-card,
.site-preview-card,
.seo-card {
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  margin-bottom: var(--spacing-6);
}

.loading-container {
  padding: var(--spacing-6);
}

.site-form {
  padding: var(--spacing-2);
}

.form-tip {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
  margin-top: var(--spacing-2);
}

/* 预览样式 */
.site-preview {
  padding: var(--spacing-4);
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
}

.preview-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4);
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-6);
}

.preview-logo-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.preview-logo {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.preview-logo img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.preview-logo-placeholder {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-tertiary);
  color: var(--text-tertiary);
  border-radius: var(--border-radius-md);
  font-size: 20px;
}

.preview-site-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.preview-nav-links {
  display: flex;
  gap: var(--spacing-4);
}

.preview-nav-item {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-fast) ease;
}

.preview-nav-item:hover {
  color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.1);
}

.preview-nav-item.active {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.preview-content {
  padding: var(--spacing-6);
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-6);
}

.preview-section {
  margin-bottom: var(--spacing-6);
}

.preview-section:last-child {
  margin-bottom: 0;
}

.preview-section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-3) 0;
  padding-bottom: var(--spacing-2);
  border-bottom: 1px solid var(--border-color);
}

.preview-description,
.preview-contact {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  line-height: 1.6;
  white-space: pre-line;
}

.preview-footer {
  padding: var(--spacing-4);
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
}

.preview-footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
}

.preview-copyright,
.preview-icp {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

/* Logo预览对话框 */
.logo-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-6);
}

.logo-preview {
  max-width: 100%;
  max-height: 200px;
  display: flex;
  justify-content: center;
}

.logo-preview img {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
}

.logo-preview-error,
.logo-error {
  display: none;
}

.logo-preview-error-container::after,
.logo-error-container::after {
  content: '图片加载失败';
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--danger-color);
  font-size: var(--font-size-sm);
  height: 100%;
}

/* 即将推出 */
.coming-soon {
  padding: var(--spacing-6);
  text-align: center;
}

.coming-soon-icon {
  font-size: 60px;
  color: var(--primary-color);
}

.coming-soon-desc {
  color: var(--text-tertiary);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-2);
}

.empty-logo-icon {
  font-size: 60px;
  color: var(--text-tertiary);
}

@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-3);
  }
  
  .header-actions {
    width: 100%;
  }
  
  .preview-navbar {
    flex-direction: column;
    gap: var(--spacing-4);
  }
  
  .preview-nav-links {
    width: 100%;
    justify-content: space-between;
  }
}
</style>
