from typing import Annotated

from fastapi import APIRouter, Depends

from app.core.database import get_db
from app.core.dependencies import get_current_admin_user
from app.core.schemas import ResponseModel
from app.models.user import User
from app.schemas.site import SiteInfo as SiteInfoSchema
from app.schemas.site import SiteInfoUpdate
from app.services.site import site_service

# 定义路由
router = APIRouter(prefix="/site-info", tags=["site"])

# 定义数据库依赖
DB = Annotated[get_db, Depends()]


@router.get("", response_model=ResponseModel[SiteInfoSchema])
async def get_site_info(db: DB):
    """
    获取站点信息

    Args:
        db: 数据库会话

    Returns:
        ResponseModel: 站点信息
    """
    # 获取站点信息
    site_info = await site_service.get_site_info(db)
    
    # 如果不存在，则创建默认站点信息
    if not site_info:
        site_info_update = SiteInfoUpdate(
            company_name="软件和设备销售平台",
            description="提供高质量的软件和设备销售服务",
            contact_info="电话：123-456-7890 邮箱：<EMAIL>",
            logo_url=""
        )
        site_info = await site_service.update_site_info(db, site_info_update)

    return ResponseModel(
        success=True,
        message="获取站点信息成功",
        data=site_info,
    )


@router.put("", response_model=ResponseModel[SiteInfoSchema])
async def update_site_info(
    site_info_update: SiteInfoUpdate,
    db: DB,
    current_user: Annotated[User, Depends(get_current_admin_user)],
):
    """
    更新站点信息（仅管理员）

    Args:
        site_info_update: 站点信息更新模型
        db: 数据库会话
        current_user: 当前管理员用户

    Returns:
        ResponseModel: 更新后的站点信息
    """
    # 更新站点信息
    site_info = await site_service.update_site_info(db, site_info_update)

    return ResponseModel(
        success=True,
        message="更新站点信息成功",
        data=site_info,
    )
