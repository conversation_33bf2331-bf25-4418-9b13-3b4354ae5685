"""
运行测试脚本
"""
import os
import sys
import pytest


def run_tests():
    """运行测试"""
    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 获取项目根目录
    root_dir = os.path.dirname(os.path.dirname(current_dir))
    # 切换到项目根目录
    os.chdir(root_dir)
    
    # 运行测试
    args = [
        "-v",  # 详细输出
        "--cov=app",  # 覆盖率报告
        "--cov-report=term-missing",  # 显示未覆盖的行
    ]
    
    # 添加命令行参数
    if len(sys.argv) > 1:
        args.extend(sys.argv[1:])
    
    # 运行测试
    return pytest.main(args)


if __name__ == "__main__":
    sys.exit(run_tests())
