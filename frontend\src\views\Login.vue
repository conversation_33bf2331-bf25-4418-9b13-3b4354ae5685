<template>
  <PageContainer customClass="auth-page">
    <div class="auth-container">
      <div class="auth-card">
        <div class="auth-header">
          <h1 class="auth-title">欢迎回来</h1>
          <p class="auth-subtitle">登录您的账户以继续</p>
        </div>
        
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          @submit.prevent="handleLogin"
          class="auth-form"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="用户名"
              :prefix-icon="User"
              size="large"
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="密码"
              :prefix-icon="Lock"
              show-password
              size="large"
              @keyup.enter="handleLogin"
            />
          </el-form-item>
          
          <div class="form-options">
            <el-checkbox v-model="rememberMe">记住我</el-checkbox>
            <el-button type="text" @click="forgotPassword">忘记密码？</el-button>
          </div>
          
          <el-form-item class="auth-actions">
            <AppButton
              type="primary"
              :loading="loading"
              class="auth-button"
              @click="handleLogin"
              size="large"
              round
            >
              登录
            </AppButton>
          </el-form-item>
        </el-form>
        
        <div class="auth-footer">
          <p>还没有账号？ <router-link to="/register" class="auth-link">立即注册</router-link></p>
        </div>
      </div>
      
      <div class="auth-image">
        <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80" alt="Login" />
        <div class="image-overlay">
          <div class="overlay-content">
            <h2>软件和设备销售平台</h2>
            <p>提供高质量的软件和硬件产品</p>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '../store'
import PageContainer from '../components/layout/PageContainer.vue'
import { User, Lock } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const loginFormRef = ref(null)
const loading = ref(false)
const rememberMe = ref(false)

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度应在3-50个字符之间', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  await loginFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    loading.value = true
    try {
      const result = await userStore.login(loginForm.username, loginForm.password)
      
      if (result.success) {
        ElMessage({
          message: '登录成功，欢迎回来！',
          type: 'success',
          duration: 2000
        })
        
        // 如果有重定向地址，则跳转到该地址
        const redirectPath = route.query.redirect || '/'
        router.push(redirectPath)
      } else {
        ElMessage.error(result.message || '登录失败，请检查用户名和密码')
      }
    } catch (error) {
      ElMessage.error('登录过程中发生错误，请稍后再试')
      console.error('登录错误:', error)
    } finally {
      loading.value = false
    }
  })
}

// 忘记密码
const forgotPassword = () => {
  ElMessage.info('忘记密码功能暂未开放，请联系管理员')
}
</script>

<style scoped>
.auth-page {
  max-width: 100% !important;
  padding: 0 !important;
}

.auth-container {
  display: flex;
  min-height: calc(100vh - 80px - 300px);
  background-color: var(--bg-primary);
}

.auth-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: var(--spacing-10);
  max-width: 500px;
  margin: 0 auto;
}

.auth-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.auth-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-2);
}

.auth-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
}

.auth-form {
  width: 100%;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
}

.auth-actions {
  margin-top: var(--spacing-6);
}

.auth-button {
  width: 100%;
}

.auth-footer {
  text-align: center;
  margin-top: var(--spacing-6);
  color: var(--text-secondary);
}

.auth-link {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: color var(--transition-fast) ease;
}

.auth-link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.auth-image {
  flex: 1;
  position: relative;
  display: none;
}

.auth-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.7));
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
}

.overlay-content {
  color: white;
  text-align: center;
}

.overlay-content h2 {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--spacing-4);
}

.overlay-content p {
  font-size: var(--font-size-lg);
  opacity: 0.9;
}

@media (min-width: 992px) {
  .auth-image {
    display: block;
  }
}

@media (max-width: 768px) {
  .auth-card {
    padding: var(--spacing-6);
  }
}
</style>
