# 测试计划

本文档描述了软件和设备销售平台的测试计划，包括单元测试和集成测试。

## 1. 用户认证模块测试

### 1.1 用户模型单元测试

**文件路径**: `backend/tests/unit/test_user_model.py`

**测试内容**:

1. **测试用户创建和查询功能**
   - 测试创建用户并验证字段值
   - 测试通过ID查询用户
   - 测试通过用户名查询用户
   - 测试通过邮箱查询用户

2. **测试用户更新功能**
   - 测试更新用户名
   - 测试更新邮箱
   - 测试更新地址
   - 测试更新多个字段

3. **测试用户认证功能**
   - 测试正确密码认证成功
   - 测试错误密码认证失败
   - 测试不存在的用户认证失败

**示例代码**:

```python
import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from app.models.user import User
from app.schemas.user import UserCreate
from app.services.user import user_service
from app.core.security import verify_password

@pytest.mark.asyncio
async def test_create_user(db_session: AsyncSession):
    # 创建用户
    user_create = UserCreate(
        username="testuser",
        email="<EMAIL>",
        password="password123"
    )
    user = await user_service.create_user(db_session, user_create)
    
    # 验证用户字段
    assert user.username == "testuser"
    assert user.email == "<EMAIL>"
    assert verify_password("password123", user.password_hash)
    assert user.is_active is True
    assert user.role == "user"
```

### 1.2 安全模块单元测试

**文件路径**: `backend/tests/unit/test_security.py`

**测试内容**:

1. **测试密码哈希和验证功能**
   - 测试密码哈希生成
   - 测试正确密码验证
   - 测试错误密码验证

2. **测试JWT令牌生成和验证**
   - 测试令牌生成
   - 测试令牌解码
   - 测试过期令牌
   - 测试无效令牌

3. **测试认证依赖功能**
   - 测试获取当前用户
   - 测试获取当前活跃用户
   - 测试获取当前管理员用户
   - 测试无效令牌的情况
   - 测试非活跃用户的情况
   - 测试非管理员用户的情况

**示例代码**:

```python
import pytest
from datetime import timedelta
from jose import jwt
from app.core.security import get_password_hash, verify_password, create_access_token, ALGORITHM
from app.core.config import settings

def test_password_hash():
    # 测试密码哈希
    password = "password123"
    hashed = get_password_hash(password)
    
    # 验证哈希不等于原始密码
    assert hashed != password
    
    # 验证密码验证功能
    assert verify_password(password, hashed) is True
    assert verify_password("wrongpassword", hashed) is False

def test_create_access_token():
    # 测试令牌生成
    data = {"sub": "1"}
    expires_delta = timedelta(minutes=15)
    token = create_access_token(data["sub"], expires_delta=expires_delta)
    
    # 解码令牌
    payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[ALGORITHM])
    
    # 验证令牌内容
    assert payload["sub"] == "1"
    assert "exp" in payload
```

### 1.3 API集成测试

**文件路径**: `backend/tests/integration/test_auth_api.py`

**测试内容**:

1. **测试用户注册流程**
   - 测试成功注册
   - 测试重复用户名注册
   - 测试重复邮箱注册
   - 测试无效数据注册

2. **测试用户登录流程**
   - 测试成功登录
   - 测试错误密码登录
   - 测试不存在的用户登录

3. **测试获取和更新用户信息**
   - 测试获取当前用户信息
   - 测试更新用户信息
   - 测试未授权访问

4. **测试权限控制功能**
   - 测试普通用户访问管理员资源
   - 测试管理员访问管理员资源

**示例代码**:

```python
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_register_user():
    # 测试用户注册
    response = client.post(
        "/api/v1/users",
        json={
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "password123"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["username"] == "testuser"
    assert data["email"] == "<EMAIL>"
    assert "password" not in data

def test_login_user():
    # 测试用户登录
    response = client.post(
        "/api/v1/auth/login",
        data={
            "username": "testuser",
            "password": "password123"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"
```

## 2. 测试环境设置

### 2.1 测试数据库

测试使用SQLite内存数据库，以确保测试的隔离性和速度。

### 2.2 测试夹具

创建以下测试夹具：

1. **db_session**：提供数据库会话
2. **client**：提供测试客户端
3. **test_user**：创建测试用户
4. **admin_user**：创建管理员用户
5. **user_token**：生成普通用户令牌
6. **admin_token**：生成管理员令牌

### 2.3 测试配置

在`backend/tests/conftest.py`中配置测试环境：

```python
import pytest
import asyncio
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
from app.core.database import Base
from app.core.config import settings
from app.main import app
from app.core.dependencies import get_db

# 创建测试数据库引擎
@pytest.fixture(scope="session")
def engine():
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        connect_args={"check_same_thread": False},
    )
    return engine

# 创建测试数据库会话
@pytest.fixture(scope="function")
async def db_session(engine):
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    async_session = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)

# 创建测试客户端
@pytest.fixture(scope="function")
def client(db_session):
    def override_get_db():
        yield db_session
    
    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as client:
        yield client
    app.dependency_overrides.clear()
```

## 3. 运行测试

### 3.1 运行单元测试

```bash
docker-compose exec backend pytest tests/unit/ -v
```

### 3.2 运行集成测试

```bash
docker-compose exec backend pytest tests/integration/ -v
```

### 3.3 运行所有测试

```bash
docker-compose exec backend pytest -v
```

### 3.4 生成测试覆盖率报告

```bash
docker-compose exec backend pytest --cov=app --cov-report=html tests/
```

## 4. 测试最佳实践

1. **测试隔离**：每个测试应该是独立的，不依赖于其他测试的状态。
2. **使用夹具**：使用pytest夹具来设置和清理测试环境。
3. **模拟外部依赖**：使用mock来模拟外部依赖，如第三方API。
4. **测试边界条件**：测试正常情况和边界条件。
5. **测试错误处理**：测试错误情况和异常处理。
6. **保持测试简单**：每个测试应该只测试一个功能点。
7. **使用有意义的断言消息**：使用有意义的断言消息来帮助调试。
