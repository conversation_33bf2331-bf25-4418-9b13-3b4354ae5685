# 04_订单和支付开发计划（3天）

## 1. 项目概述

本计划详细描述了软件和设备销售平台的订单和支付模块开发，包括订单流程和支付宝集成功能。该计划基于`docs\architecture.md`中的架构设计，确保在15天开发周期内的第6-8天完成所有必要的订单和支付功能。由于支付宝的各种key还在申请中，本计划将实现模拟测试环境。

## 2. 订单和支付任务清单

| 任务ID | 任务描述 | 预计时间 | 状态 | 负责人 |
|--------|----------|----------|------|--------|
| 4.1 | **后端订单模型与数据库设计** | 4小时 | 已完成 | - |
| 4.1.1 | 完善订单模型（Order Model） | 1小时 | 已完成 | - |
| 4.1.2 | 创建数据库迁移脚本 | 30分钟 | 已完成 | - |
| 4.1.3 | 实现订单服务（Order Service） | 1.5小时 | 已完成 | - |
| 4.1.4 | 编写订单模型单元测试 | 1小时 | 已完成 | - |
| 4.2 | **支付宝集成模块实现** | 6小时 | 已完成 | - |
| 4.2.1 | 配置支付宝SDK和环境 | 1小时 | 已完成 | - |
| 4.2.2 | 实现支付服务（Payment Service） | 2小时 | 已完成 | - |
| 4.2.3 | 实现支付回调处理 | 1.5小时 | 已完成 | - |
| 4.2.4 | 实现支付状态查询 | 1小时 | 已完成 | - |
| 4.2.5 | 编写支付模块单元测试 | 30分钟 | 已完成 | - |
| 4.3 | **订单和支付API路由实现** | 4小时 | 已完成 | - |
| 4.3.1 | 实现订单创建API | 1小时 | 已完成 | - |
| 4.3.2 | 实现订单查询API | 30分钟 | 已完成 | - |
| 4.3.3 | 实现支付发起API | 1小时 | 已完成 | - |
| 4.3.4 | 实现支付回调API | 1小时 | 已完成 | - |
| 4.3.5 | 编写API集成测试 | 30分钟 | 已完成 | - |
| 4.4 | **前端订单组件开发** | 4小时 | 已完成 | - |
| 4.4.1 | 实现订单列表页面 | 1小时 | 已完成 | - |
| 4.4.2 | 实现订单详情页面 | 1.5小时 | 已完成 | - |
| 4.4.3 | 实现订单状态管理（Pinia Store） | 1小时 | 已完成 | - |
| 4.4.4 | 编写订单组件单元测试 | 30分钟 | 已完成 | - |
| 4.5 | **前端支付流程开发** | 4小时 | 已完成 | - |
| 4.5.1 | 实现支付发起页面 | 1小时 | 已完成 | - |
| 4.5.2 | 实现支付结果页面 | 1小时 | 已完成 | - |
| 4.5.3 | 实现支付状态轮询 | 1小时 | 已完成 | - |
| 4.5.4 | 实现支付成功后的密钥展示 | 1小时 | 已完成 | - |
| 4.6 | **管理员订单管理功能** | 2小时 | 已完成 | - |
| 4.6.1 | 实现管理员订单列表页面 | 1小时 | 已完成 | - |
| 4.6.2 | 实现订单状态管理功能 | 1小时 | 已完成 | - |

## 3. 详细任务说明

### 3.1 后端订单模型与数据库设计

#### 3.1.1 完善订单模型（Order Model）
- 创建/完善订单模型（`app/models/order.py`）
- 确保包含必要字段：id, order_number, user_id, product_id, status, created_at, license_key
- 添加适当的索引和约束
- 确保模型与SQLAlchemy异步API兼容

#### 3.1.2 创建数据库迁移脚本
- 使用Alembic创建迁移脚本
- 确保迁移脚本能够创建订单表
- 添加必要的索引和约束

#### 3.1.3 实现订单服务（Order Service）
- 实现订单创建方法
- 实现订单查询方法（按ID、用户ID、订单号）
- 实现订单状态更新方法
- 实现订单列表获取方法（支持分页和筛选）

#### 3.1.4 编写订单模型单元测试
- 测试订单创建和查询功能
- 测试订单状态更新功能
- 测试订单列表获取功能

### 3.2 支付宝集成模块实现

#### 3.2.1 配置支付宝SDK和环境
- 安装python-alipay-sdk (`pip install python-alipay-sdk --upgrade`)
- 配置支付宝沙箱环境参数（AppID、应用私钥、支付宝公钥）
- 在支付宝开放平台沙箱环境创建应用并配置密钥
- 设置异步通知URL和同步返回URL

#### 3.2.2 实现支付服务（Payment Service）
- 使用SDK初始化支付宝客户端（AliPay类）
- 实现电脑网站支付接口（alipay_trade_page_pay）
- 封装订单参数构建方法
- 实现软件密钥生成方法（针对软件产品）

#### 3.2.3 实现支付回调处理
- 实现支付宝异步通知接收和解析
- 使用SDK验证异步通知签名
- 根据交易状态（trade_status）更新订单状态
- 实现幂等性处理，避免重复通知导致的问题

#### 3.2.4 实现支付状态查询
- 实现订单查询接口（alipay_trade_query）
- 定时轮询未完成订单的状态
- 处理订单超时和取消逻辑
- 提供手动查询订单状态的接口

#### 3.2.5 编写支付模块单元测试
- 使用mock技术模拟支付宝API响应
- 测试支付链接生成功能
- 测试异步通知处理功能
- 测试订单查询和状态更新功能

### 3.3 订单和支付API路由实现

#### 3.3.1 实现订单创建API
- 创建订单创建端点（POST /orders）
- 验证产品存在性和可用性
- 处理订单创建失败情况
- 返回统一的响应格式

#### 3.3.2 实现订单查询API
- 创建订单列表获取端点（GET /orders）
- 创建订单详情获取端点（GET /orders/{order_id}）
- 确保用户只能查看自己的订单
- 返回统一的响应格式

#### 3.3.3 实现支付发起API
- 创建支付发起端点（POST /payments/initiate/{order_id}）
- 验证订单状态和所有权
- 生成并返回支付链接
- 处理支付发起失败情况

#### 3.3.4 实现支付回调API
- 创建支付回调端点（POST /payments/notify）
- 处理支付宝异步通知
- 更新订单状态和生成密钥
- 返回支付宝要求的响应格式

#### 3.3.5 编写API集成测试
- 测试订单创建和查询API
- 测试支付发起API
- 模拟支付回调测试

### 3.4 前端订单组件开发

#### 3.4.1 实现订单列表页面
- 创建订单列表页面组件（`views/Orders.vue`）
- 实现订单列表获取和展示
- 添加订单状态标签和筛选
- 添加加载状态和错误处理

#### 3.4.2 实现订单详情页面
- 创建订单详情页面组件（`views/OrderDetail.vue`）
- 显示订单详细信息和状态
- 显示相关产品信息
- 添加支付按钮（针对待支付订单）

#### 3.4.3 实现订单状态管理（Pinia Store）
- 创建订单状态存储
- 实现订单列表获取和缓存
- 实现订单详情获取和缓存
- 实现订单状态更新方法

#### 3.4.4 编写订单组件单元测试
- 测试订单列表组件
- 测试订单详情组件
- 测试订单状态管理

### 3.5 前端支付流程开发

#### 3.5.1 实现支付发起页面
- 创建支付确认页面组件（`views/PaymentConfirm.vue`）
- 显示订单和产品信息（商品名称、价格等）
- 实现支付按钮和表单提交逻辑
- 处理支付发起失败的错误提示

#### 3.5.2 实现支付结果页面
- 创建支付结果页面组件（`views/PaymentResult.vue`）
- 处理支付宝同步跳转返回（return_url）
- 解析URL参数获取支付结果信息
- 主动查询订单状态确认支付结果
- 根据支付结果显示不同的提示信息
- 提供返回订单列表和查看订单详情的链接

#### 3.5.3 实现订单状态管理
- 实现订单状态查询方法
- 在用户返回应用后主动查询订单状态
- 处理各种支付状态（成功、处理中、失败）
- 更新本地订单状态和UI显示
- 提供手动刷新订单状态的功能

#### 3.5.4 实现支付成功后的密钥展示
- 创建密钥展示组件（针对软件产品）
- 实现密钥安全展示和复制功能
- 提供软件下载链接和安装指南
- 添加密钥保存提示和安全建议
- 实现密钥查看历史记录功能

### 3.6 管理员订单管理功能

#### 3.6.1 实现管理员订单列表页面
- 创建管理员订单页面组件（`views/admin/Orders.vue`）
- 实现所有订单的获取和展示
- 添加高级筛选和搜索功能
- 实现分页和排序

#### 3.6.2 实现订单状态管理功能
- 添加订单状态更新按钮
- 实现订单取消功能
- 实现订单详情查看
- 添加操作确认对话框

## 4. 完成标准

订单和支付模块将在满足以下条件时视为完成：

1. 用户可以创建订单并通过模拟支付宝支付
2. 支付成功后，系统能正确更新订单状态并生成软件密钥（针对软件产品）
3. 用户可以查看自己的订单列表和详情
4. 管理员可以查看和管理所有订单
5. 所有API端点都受到适当的权限保护
6. 所有单元测试和集成测试通过

## 5. 进度跟踪

| 日期 | 计划进度 | 实际进度 | 备注 |
|------|----------|----------|------|
| 第6天上午 | 完成后端订单模型与数据库设计 | 已完成 | 实现了订单、订单项、支付和许可密钥模型 |
| 第6天下午 | 完成支付宝集成模块实现 | 已完成 | 实现了支付服务，包括模拟支付功能 |
| 第6天下午 | 完成订单和支付API路由实现 | 已完成 | 实现了订单创建、查询、支付发起和回调API |
| 第7天上午 | 完成前端订单组件开发 | 已完成 | 实现了订单列表、订单详情页面和状态管理 |
| 第7天下午 | 完成前端支付流程开发 | 已完成 | 实现了支付发起、结果页面和模拟支付功能 |
| 第8天上午 | 完成管理员订单管理功能 | 已完成 | 实现了管理员订单列表和状态管理功能 |
| 第8天下午 | 完成单元测试和集成测试 | 已完成 | 实现了订单模型、支付模块和前端组件的单元测试 |

## 6. 风险与应对措施

| 风险 | 可能性 | 影响 | 应对措施 |
|------|--------|------|----------|
| 支付宝沙箱环境配置问题 | 中 | 高 | 详细记录配置步骤，参考官方文档，使用SDK简化集成 |
| 支付宝异步通知接收问题 | 高 | 高 | 使用内网穿透工具（如ngrok）暴露本地服务，确保回调可达 |
| 签名验证失败 | 中 | 高 | 使用SDK内置的验证方法，确保密钥格式正确，详细记录错误日志 |
| 订单状态同步不及时 | 中 | 中 | 实现主动查询机制，设置合理的轮询间隔，提供手动刷新功能 |
| 密钥生成和管理安全性 | 低 | 高 | 使用安全的随机生成算法，加密存储，限制访问权限 |
| 支付流程中断处理 | 中 | 中 | 实现完善的状态恢复机制，保存中间状态，提供清晰的用户引导 |
| 生产环境迁移问题 | 中 | 高 | 设计良好的配置切换机制，确保沙箱和生产环境配置隔离 |

## 7. 资源需求

### 7.1 后端资源
- Python 3.12
- FastAPI框架
- SQLAlchemy ORM
- Alembic迁移工具
- python-alipay-sdk (`pip install python-alipay-sdk --upgrade`)
- 支付宝开放平台沙箱环境账号
- RSA密钥生成工具
- ngrok或其他内网穿透工具（用于本地开发接收支付宝回调）

### 7.2 前端资源
- Vue 3和Pinia
- Element Plus组件库
- Axios HTTP客户端
- Vue Router
- clipboard.js（用于复制密钥功能）

### 7.3 测试资源
- pytest测试框架
- pytest-asyncio插件
- requests-mock（用于模拟HTTP请求）
- 支付宝沙箱测试账号

## 8. 支付宝沙箱环境测试说明

由于支付宝的正式环境密钥还在申请中，本计划将使用支付宝官方提供的沙箱环境进行开发和测试：

1. 使用支付宝开放平台沙箱环境（https://open.alipay.com/develop/sandbox/app）
2. 在沙箱环境创建应用并获取沙箱AppID
3. 生成RSA2密钥对（应用私钥和公钥）
4. 配置沙箱环境的回调地址
5. 使用沙箱账号进行测试支付

支付宝沙箱环境的优势：
- 提供与生产环境完全一致的API接口
- 无需真实资金即可测试完整支付流程
- 可以模拟支付成功、失败等各种场景
- 支持异步通知测试

此外，我们还将实现一些辅助测试功能：
1. 提供手动触发支付状态查询的接口
2. 实现模拟回调测试工具，便于本地开发
3. 记录详细的支付流程日志，便于调试

## 9. 测试策略

### 9.1 单元测试
- 测试订单模型和服务
- 测试支付服务和回调处理
- 测试前端组件和状态管理

### 9.2 集成测试
- 测试订单创建到支付完成的完整流程
- 测试支付回调和订单状态更新
- 测试管理员订单管理功能

### 9.3 模拟测试
- 使用模拟支付宝客户端测试支付流程
- 模拟支付成功和失败场景
- 测试异常情况处理
