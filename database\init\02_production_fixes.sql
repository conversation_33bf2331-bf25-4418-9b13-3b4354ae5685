-- 生产环境数据库修复脚本
-- 修复数据类型不一致和添加必要的索引

-- 1. 修复数据类型不一致问题
-- 注意：在生产环境中执行这些 ALTER 语句前，请确保备份数据

-- 修复产品价格字段类型（如果需要）
-- ALTER TABLE products ALTER COLUMN price TYPE DECIMAL(10, 2);

-- 修复订单金额字段类型（如果需要）
-- ALTER TABLE orders ALTER COLUMN total_amount TYPE DECIMAL(10, 2);

-- 修复订单项价格字段类型（如果需要）
-- ALTER TABLE order_items ALTER COLUMN price TYPE DECIMAL(10, 2);

-- 修复支付金额字段类型（如果需要）
-- ALTER TABLE payments ALTER COLUMN amount TYPE DECIMAL(10, 2);

-- 2. 添加性能优化索引
-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- 产品表索引
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);
CREATE INDEX IF NOT EXISTS idx_products_product_type ON products(product_type);
CREATE INDEX IF NOT EXISTS idx_products_is_active ON products(is_active);
CREATE INDEX IF NOT EXISTS idx_products_price ON products(price);
CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at);

-- 订单表索引
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_orders_updated_at ON orders(updated_at);
CREATE INDEX IF NOT EXISTS idx_orders_order_number ON orders(order_number);

-- 订单项表索引
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);
CREATE INDEX IF NOT EXISTS idx_order_items_created_at ON order_items(created_at);

-- 支付记录表索引
CREATE INDEX IF NOT EXISTS idx_payments_order_id ON payments(order_id);
CREATE INDEX IF NOT EXISTS idx_payments_payment_id ON payments(payment_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_payment_method ON payments(payment_method);
CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at);
CREATE INDEX IF NOT EXISTS idx_payments_payment_time ON payments(payment_time);

-- 许可密钥表索引
CREATE INDEX IF NOT EXISTS idx_license_keys_user_id ON license_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_license_keys_product_id ON license_keys(product_id);
CREATE INDEX IF NOT EXISTS idx_license_keys_order_id ON license_keys(order_id);
CREATE INDEX IF NOT EXISTS idx_license_keys_is_active ON license_keys(is_active);
CREATE INDEX IF NOT EXISTS idx_license_keys_created_at ON license_keys(created_at);

-- 站点信息表索引
CREATE INDEX IF NOT EXISTS idx_site_info_created_at ON site_info(created_at);

-- 3. 添加数据完整性约束
-- 确保产品类型只能是 software 或 hardware
ALTER TABLE products ADD CONSTRAINT check_product_type 
    CHECK (product_type IN ('software', 'hardware'));

-- 确保订单状态只能是指定值
ALTER TABLE orders ADD CONSTRAINT check_order_status 
    CHECK (status IN ('pending', 'paid', 'cancelled'));

-- 确保支付状态只能是指定值
ALTER TABLE payments ADD CONSTRAINT check_payment_status 
    CHECK (status IN ('pending', 'success', 'failed'));

-- 确保用户角色只能是指定值
ALTER TABLE users ADD CONSTRAINT check_user_role 
    CHECK (role IN ('user', 'admin'));

-- 确保价格为正数
ALTER TABLE products ADD CONSTRAINT check_product_price_positive 
    CHECK (price > 0);

-- 确保订单金额为正数
ALTER TABLE orders ADD CONSTRAINT check_order_amount_positive 
    CHECK (total_amount > 0);

-- 确保订单项价格为正数
ALTER TABLE order_items ADD CONSTRAINT check_order_item_price_positive 
    CHECK (price > 0);

-- 确保订单项数量为正数
ALTER TABLE order_items ADD CONSTRAINT check_order_item_quantity_positive 
    CHECK (quantity > 0);

-- 确保支付金额为正数
ALTER TABLE payments ADD CONSTRAINT check_payment_amount_positive 
    CHECK (amount > 0);

-- 确保库存不为负数
ALTER TABLE products ADD CONSTRAINT check_product_stock_non_negative 
    CHECK (stock >= 0);

-- 4. 创建视图以简化常用查询
-- 订单详情视图
CREATE OR REPLACE VIEW order_details AS
SELECT 
    o.id,
    o.order_number,
    o.user_id,
    u.username,
    u.email,
    o.total_amount,
    o.status,
    o.payment_method,
    o.created_at,
    o.updated_at,
    COUNT(oi.id) as item_count,
    STRING_AGG(p.name, ', ') as product_names
FROM orders o
JOIN users u ON o.user_id = u.id
LEFT JOIN order_items oi ON o.id = oi.order_id
LEFT JOIN products p ON oi.product_id = p.id
GROUP BY o.id, u.username, u.email;

-- 用户统计视图
CREATE OR REPLACE VIEW user_stats AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.role,
    u.created_at,
    COUNT(o.id) as total_orders,
    COALESCE(SUM(o.total_amount), 0) as total_spent,
    COUNT(CASE WHEN o.status = 'paid' THEN 1 END) as paid_orders,
    COUNT(lk.id) as license_keys_count
FROM users u
LEFT JOIN orders o ON u.id = o.user_id
LEFT JOIN license_keys lk ON u.id = lk.user_id
GROUP BY u.id;

-- 产品统计视图
CREATE OR REPLACE VIEW product_stats AS
SELECT 
    p.id,
    p.name,
    p.product_type,
    p.price,
    p.stock,
    p.is_active,
    p.created_at,
    COUNT(oi.id) as total_sales,
    COALESCE(SUM(oi.quantity), 0) as total_quantity_sold,
    COALESCE(SUM(oi.price * oi.quantity), 0) as total_revenue
FROM products p
LEFT JOIN order_items oi ON p.id = oi.product_id
LEFT JOIN orders o ON oi.order_id = o.id AND o.status = 'paid'
GROUP BY p.id;

-- 5. 创建函数用于常用操作
-- 生成订单号的函数
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TEXT AS $$
BEGIN
    RETURN 'ORD-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || 
           LPAD(NEXTVAL('order_number_seq')::TEXT, 6, '0');
END;
$$ LANGUAGE plpgsql;

-- 创建序列（如果不存在）
CREATE SEQUENCE IF NOT EXISTS order_number_seq START 1;

-- 生成许可密钥的函数
CREATE OR REPLACE FUNCTION generate_license_key()
RETURNS TEXT AS $$
DECLARE
    chars TEXT := 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    result TEXT := '';
    i INTEGER;
BEGIN
    FOR i IN 1..25 LOOP
        IF i % 6 = 0 AND i < 25 THEN
            result := result || '-';
        ELSE
            result := result || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
        END IF;
    END LOOP;
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 6. 创建触发器
-- 自动更新 updated_at 字段的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为需要的表创建触发器
CREATE TRIGGER update_products_updated_at 
    BEFORE UPDATE ON products 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at 
    BEFORE UPDATE ON orders 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payments_updated_at 
    BEFORE UPDATE ON payments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_license_keys_updated_at 
    BEFORE UPDATE ON license_keys 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_site_info_updated_at 
    BEFORE UPDATE ON site_info 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 7. 创建管理员用户（生产环境密码）
-- 注意：在生产环境中，应该使用更强的密码
INSERT INTO users (username, email, password_hash, role)
VALUES (
    'admin',
    '<EMAIL>',  -- 请替换为实际的管理员邮箱
    '$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', -- 请替换为更强的密码哈希
    'admin'
) ON CONFLICT (username) DO UPDATE SET
    email = EXCLUDED.email,
    password_hash = EXCLUDED.password_hash;

-- 8. 插入生产环境站点信息
INSERT INTO site_info (company_name, description, contact_info)
VALUES (
    '您的公司名称',  -- 请替换为实际的公司名称
    '您的公司描述',  -- 请替换为实际的公司描述
    '联系电话：xxx-xxxx-xxxx 邮箱：<EMAIL>'  -- 请替换为实际的联系信息
) ON CONFLICT (id) DO UPDATE SET
    company_name = EXCLUDED.company_name,
    description = EXCLUDED.description,
    contact_info = EXCLUDED.contact_info;

-- 9. 创建数据库用户权限（可选）
-- 创建只读用户用于报表查询
-- CREATE USER readonly_user WITH PASSWORD 'strong_readonly_password';
-- GRANT CONNECT ON DATABASE sales_platform TO readonly_user;
-- GRANT USAGE ON SCHEMA public TO readonly_user;
-- GRANT SELECT ON ALL TABLES IN SCHEMA public TO readonly_user;
-- ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO readonly_user;
