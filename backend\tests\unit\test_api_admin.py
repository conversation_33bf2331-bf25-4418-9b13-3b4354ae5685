import pytest
from unittest.mock import patch, MagicMock

from fastapi import status
from fastapi.testclient import TestClient

from app.main import app
from app.models.user import User
from app.services.admin import admin_service


@pytest.fixture
def admin_user():
    return User(
        id=1,
        username="admin",
        email="<EMAIL>",
        password_hash="hashed_password",
        is_active=True,
        role="admin"
    )


@pytest.fixture
def regular_user():
    return User(
        id=2,
        username="user",
        email="<EMAIL>",
        password_hash="hashed_password",
        is_active=True,
        role="user"
    )


def test_get_users_admin_access(admin_user):
    with patch("app.api.admin.get_current_admin_user", return_value=admin_user), \
         patch("app.services.user.user_service.get_users", return_value=[admin_user]), \
         patch("app.services.user.user_service.get_user_count", return_value=1):
        client = TestClient(app)
        response = client.get("/api/v1/admin/users")
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["success"] is True
        assert response.json()["message"] == "获取用户列表成功"
        assert len(response.json()["data"]) == 1
        assert response.json()["total"] == 1


def test_get_users_unauthorized(regular_user):
    with patch("app.api.admin.get_current_admin_user", side_effect=Exception("权限不足")):
        client = TestClient(app)
        response = client.get("/api/v1/admin/users")
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


def test_get_statistics(admin_user):
    mock_stats = {
        "user_count": 10,
        "order_count": 20,
        "paid_order_count": 15,
        "software_sales": 10,
        "hardware_sales": 5,
        "product_count": 8,
        "active_product_count": 6
    }
    
    with patch("app.api.admin.get_current_admin_user", return_value=admin_user), \
         patch("app.services.admin.admin_service.get_sales_statistics", return_value=mock_stats):
        client = TestClient(app)
        response = client.get("/api/v1/admin/statistics")
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["success"] is True
        assert response.json()["message"] == "获取销售统计成功"
        assert response.json()["data"] == mock_stats


def test_get_all_orders(admin_user):
    mock_orders = [
        {"id": 1, "order_number": "ORD-001", "status": "paid"},
        {"id": 2, "order_number": "ORD-002", "status": "pending"}
    ]
    
    with patch("app.api.admin.get_current_admin_user", return_value=admin_user), \
         patch("app.services.admin.admin_service.get_all_orders", return_value=mock_orders), \
         patch("app.services.admin.admin_service.get_orders_count", return_value=2):
        client = TestClient(app)
        response = client.get("/api/v1/admin/orders")
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["success"] is True
        assert response.json()["message"] == "获取订单列表成功"
        assert len(response.json()["data"]) == 2
        assert response.json()["total"] == 2


def test_toggle_user_status(admin_user):
    mock_user = User(
        id=3,
        username="test_user",
        email="<EMAIL>",
        password_hash="hashed_password",
        is_active=True,
        role="user"
    )
    
    with patch("app.api.admin.get_current_admin_user", return_value=admin_user), \
         patch("app.services.admin.admin_service.toggle_user_status", return_value=mock_user):
        client = TestClient(app)
        response = client.patch(f"/api/v1/admin/users/{mock_user.id}/status?is_active=false")
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["success"] is True
        assert response.json()["message"] == "用户已禁用"


def test_toggle_user_status_not_found(admin_user):
    with patch("app.api.admin.get_current_admin_user", return_value=admin_user), \
         patch("app.services.admin.admin_service.toggle_user_status", return_value=None):
        client = TestClient(app)
        response = client.patch("/api/v1/admin/users/999/status?is_active=false")
        assert response.status_code == status.HTTP_404_NOT_FOUND


def test_toggle_own_status(admin_user):
    with patch("app.api.admin.get_current_admin_user", return_value=admin_user):
        client = TestClient(app)
        response = client.patch(f"/api/v1/admin/users/{admin_user.id}/status?is_active=false")
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["detail"] == "不能修改自己的状态"
