# 前后端对接与测试策略FAQ (SPARC实践)

## Q1: 前后端都完成了TDD，现在打算对接，应该怎么做？是否需要对齐伪代码和OpenAPI？

**A1:**
前后端对接的核心在于双方都遵循一个共同的“接口契约”。在SPARC中，我们通常使用OpenAPI规范 (例如 [`docs/openapi/openapi.yaml`](docs/openapi/openapi.yaml)) 来定义这个契约。

*   **首要任务**：确保您的前端实现和后端实现都严格遵守OpenAPI文档中定义的契约。如果代码与OpenAPI有任何不一致，应以OpenAPI为准进行调整。
*   **伪代码的角色**：伪代码 (例如 [`docs/4_pseudocode_user_auth.md`](docs/4_pseudocode_user_auth.md)) 主要用于设计和验证模块内部的逻辑（通过TDD）。在对接阶段，伪代码与OpenAPI在逻辑上应该是对齐的，因为它们都源于相同的需求和规格。如果发现不一致，优先以OpenAPI为准。
*   **对接步骤建议**：
    1.  **确认OpenAPI的最终性**：确保当前的OpenAPI文档是最新且双方都认可的最终版本。
    2.  **前端API调用实现**：前端根据OpenAPI规范，正确构建请求并调用后端接口。
    3.  **后端接口实现验证**：后端确保其接口的实际行为与OpenAPI规范一致（通过API层集成测试）。
    4.  **数据联调**：实际运行前后端，发送请求，检查响应，确保数据流正确无误。
    5.  **集成测试**：编写测试来验证前后端集成后的关键路径和场景。

## Q2: 我设想的对接逻辑是：1. 检查伪代码具体要求 -> 2. 检查OpenAPI具体规则 -> 3. 检查后端代码是否符合OpenAPI规则 -> 4. 开始对接，修改前端对接后端代码 -> 5. 然后测试。这个逻辑对吗？

**A2:**
这个逻辑非常清晰和系统，完全符合SPARC提倡的结构化开发方法！逐个功能进行对齐和测试是一个非常好的实践。在这个流程中，OpenAPI文档是所有接口实现的最终裁判。

## Q3: 是否会出现前后端代码都“跑偏”（即与规范不符）的情况？

**A3:**
是的，完全有可能。主要原因可能包括：
*   **需求理解偏差**：即使有伪代码和OpenAPI，开发者对细节的理解仍可能存在差异。
*   **沟通不充分**：前后端开发者之间未能就接口约定达成完全一致。
*   **规格变更未同步**：需求或接口设计变更后，未能及时更新所有相关文档并通知所有开发者。
*   **OpenAPI文档维护不当**：OpenAPI文档没有被视为“单一事实来源”并严格维护。

SPARC通过强调**单一事实来源 (OpenAPI)**、**TDD**、**契约先行**和**持续集成测试**来帮助减少这种风险。应对方法是以OpenAPI为基准，通过对比发现并修正偏差。

## Q4: 后端除了单元测试外，还需要其他测试吗？（我们目前并没有对接数据库）

**A4:**
是的，后端除了单元测试外，还需要**API层集成测试**。
*   **单元测试**：验证独立的业务逻辑单元（如services层函数），此时数据库等外部依赖通常会被模拟(mock)。
*   **API层集成测试**：验证API端点（如routers层）的行为是否符合OpenAPI规范。这些测试会模拟HTTP请求，并检查响应的状态码、数据格式等。即使没有真实数据库，也可以通过模拟数据和服务依赖来完成。
*   **端到端测试**：在前后端对接完成，并且数据库等基础设施集成后进行，从用户角度验证整个系统流程。

## Q5: 我们如何知道后端测试代码覆盖了多少？

**A5:**
通过**代码覆盖率 (Code Coverage)** 工具。对于Python后端 (如本项目)，可以使用 `pytest-cov` (pytest的插件)。
1.  **安装**：`pip install pytest-cov` (如果尚未安装)。
2.  **运行**：执行 `pytest` 时加入覆盖率参数，例如 `pytest --cov=./backend/app --cov-report=html --cov-report=term`。
    *   `--cov=./backend/app` 指定被测代码路径。
    *   `--cov-report=html` 生成HTML格式的详细报告。
    *   `--cov-report=term` 在终端输出摘要。
3.  **分析**：查看HTML报告 (通常在 `htmlcov/index.html`)，识别未覆盖的代码行和模块。
**注意**：高覆盖率是目标，但并非唯一标准，测试用例的质量和对关键路径的覆盖更为重要。

## Q6: 我们后端测试使用的是 `docker-compose run --rm backend_tests`，如何获取覆盖率？

**A6:**
1.  **确保 `pytest-cov` 已安装在测试容器的镜像中** (检查Dockerfile和requirements.txt)。
2.  **修改 `docker-compose` 命令或 `docker-compose.yml` 中服务的 `command`**，以包含 `pytest` 的覆盖率参数。
3.  **使用Volume映射将报告输出到宿主机**。
    例如，在 `docker-compose run` 命令中：
    \`\`\`bash
    docker-compose run --rm -v ./backend/htmlcov_output:/app/htmlcov_output backend_tests pytest --cov=./app --cov-report=html:htmlcov_output --cov-report=term
    \`\`\`
    这里假设容器内代码在 \`/app\`，报告输出到容器内 \`/app/htmlcov_output\`，并映射到宿主机的 \`./backend/htmlcov_output\`。

## Q7: 运行覆盖率后发现后端TDD并未完全完成，例如 \`app/user/router.py\` 覆盖率只有55%。接下来怎么办？

**A7:**
这是一个重要的发现。如果后端TDD不完整，特别是API接口层的测试覆盖率较低，对接风险会显著增加。
*   **强烈建议**：在进行大规模前后端对接之前，优先投入时间提升后端代码的测试覆盖率，尤其是像 \`app/user/router.py\` 这样直接暴露给前端的关键模块。
*   **行动计划**：
    1.  仔细分析HTML覆盖率报告，找出未覆盖的代码。
    2.  优先为关键路径和低覆盖率模块（如用户认证相关的API）补充测试用例。
    3.  遵循TDD的“红-绿-重构”思路，即使是补充测试。
    4.  迭代进行，直到关键模块达到可接受的覆盖率水平（例如85%-90%+）。
“磨刀不误砍柴工”，加固后端测试能显著提高后续对接效率和系统质量。

## Q8: 那我们必须将前端的 \`@/frontend/src/api\` 修正到完全符合OpenAPI吗？如果不这么做，是否会导致前端错误很多，对接错误过多？

**A8:**
是的，您的理解完全正确！
*   **OpenAPI是契约**：前端的API调用层 (如 \`frontend/src/api/\`) 和后端的API实现都必须严格遵守OpenAPI规范。
*   **重要性**：
    *   **减少对接错误**：避免因接口定义不一致导致的问题。
    *   **支持并行开发**：双方有共同的明确目标。
    *   **清晰的责任边界**：便于定位问题。
    *   **利用自动化工具**：如代码生成、文档、测试。
*   **不遵守的后果**：前端可能发送无效请求或无法处理响应，后端可能无法正确处理请求，导致对接时错误频发，调试极为困难。

## Q9: 我在编写前端TDD之前，是否就应该先按照OpenAPI来编写 \`@/frontend/src/api\` 中的所有模拟接口？

**A9:**
是的，这正是理想的、SPARC推荐的“**契约先行 (Contract-First)**”的TDD流程。
1.  **OpenAPI规范先行**：定义并共同认可。
2.  **前端API层模拟**：基于OpenAPI创建模拟接口 (mocked/stubbed API calls) 在 \`frontend/src/api/\` 中。
3.  **前端业务/UI组件TDD**：调用这些基于OpenAPI的模拟API接口进行测试。
4.  **后端并行开发与TDD**：基于同一份OpenAPI。
5.  **对接与集成测试**：前端切换到真实API调用，进行集成验证。
这种做法能早期发现契约问题、解耦开发、保证测试稳定性和从源头确保一致性。

## Q10: 后端也需要像前端那样创建模拟的“测试接口”吗？还是后端直接按照OpenAPI编写测试就行？

**A10:**
后端与前端的测试策略有所不同：
*   **前端**：需要模拟API接口（即模拟后端行为），因为前端是API的 *消费者*。这样做是为了独立测试前端自身的UI和业务逻辑，而不依赖真实的后端。
*   **后端**：不需要模拟自己的API接口，因为后端是API的 *提供者*。后端的测试目标是 *验证* 自己实现的API接口行为是否严格符合OpenAPI规范。
    *   **单元测试**：可能会模拟更深层次的依赖（如数据库、第三方服务）来测试内部业务逻辑单元。
    *   **API层集成测试**：直接向后端API端点发送真实的HTTP请求（根据OpenAPI定义），并断言响应是否符合OpenAPI规范。

总结：前端模拟“外部的后端”，后端验证“自身的API契约”。