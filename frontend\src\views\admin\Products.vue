<template>
  <PageContainer customClass="admin-page">
    <div class="admin-products">
      <div class="page-header">
        <div class="header-content">
          <h1 class="page-title">产品管理</h1>
          <p class="page-subtitle">管理产品信息、上下架产品</p>
        </div>
        <div class="header-actions">
          <AppButton type="primary" @click="showAddProductDialog" round>
            <el-icon><Plus /></el-icon>
            添加产品
          </AppButton>
        </div>
      </div>
      
      <!-- 筛选工具栏 -->
      <div class="filter-toolbar">
        <div class="filter-group">
          <el-select 
            v-model="productType" 
            placeholder="产品类型" 
            clearable
            @change="handleFilterChange"
          >
            <el-option label="全部类型" value="" />
            <el-option label="软件" value="software" />
            <el-option label="硬件" value="hardware" />
          </el-select>
          
          <el-select 
            v-model="isActive" 
            placeholder="产品状态" 
            clearable
            @change="handleFilterChange"
          >
            <el-option label="全部状态" value="" />
            <el-option label="已上架" value="true" />
            <el-option label="已下架" value="false" />
          </el-select>
        </div>
        
        <div class="search-group">
          <el-input
            v-model="searchQuery"
            placeholder="搜索产品名称"
            clearable
            @clear="handleFilterChange"
            @keyup.enter="handleFilterChange"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <AppButton type="primary" @click="handleFilterChange" round>
            <el-icon><Search /></el-icon>
            搜索
          </AppButton>
        </div>
      </div>
      
      <!-- 批量操作工具栏 -->
      <div class="batch-toolbar" v-if="selectedProducts.length > 0">
        <div class="selection-info">
          已选择 <span class="selection-count">{{ selectedProducts.length }}</span> 个产品
        </div>
        <div class="batch-actions">
          <AppButton 
            type="success" 
            @click="batchUpdateStatus(true)" 
            :disabled="!hasInactiveSelected"
            round
          >
            <el-icon><Top /></el-icon>
            批量上架
          </AppButton>
          <AppButton 
            type="danger" 
            @click="batchUpdateStatus(false)" 
            :disabled="!hasActiveSelected"
            round
          >
            <el-icon><Bottom /></el-icon>
            批量下架
          </AppButton>
        </div>
      </div>
      
      <!-- 产品表格 -->
      <el-card shadow="never" class="products-card">
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="10" animated />
        </div>
        <div v-else-if="products.length === 0" class="empty-container">
          <el-empty description="暂无产品数据">
            <template #extra>
              <AppButton type="primary" @click="showAddProductDialog" round>
                <el-icon><Plus /></el-icon>
                添加产品
              </AppButton>
            </template>
          </el-empty>
        </div>
        <el-table
          v-else
          ref="productTableRef"
          :data="products"
          style="width: 100%"
          row-key="id"
          @selection-change="handleSelectionChange"
          :border="false"
          :stripe="true"
          class="products-table"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column label="产品信息" min-width="300">
            <template #default="scope">
              <div class="product-info-cell">
                <div class="product-image">
                  <el-image 
                    :src="scope.row.image_url || 'https://via.placeholder.com/50'" 
                    fit="cover"
                    :preview-src-list="scope.row.image_url ? [scope.row.image_url] : []"
                  >
                    <template #error>
                      <div class="image-placeholder">
                        <el-icon><Picture /></el-icon>
                      </div>
                    </template>
                  </el-image>
                </div>
                <div class="product-details">
                  <div class="product-name">{{ scope.row.name }}</div>
                  <div class="product-meta">
                    <el-tag 
                      size="small" 
                      :type="scope.row.product_type === 'software' ? 'success' : 'warning'"
                      effect="plain"
                    >
                      {{ scope.row.product_type === 'software' ? '软件' : '硬件' }}
                    </el-tag>
                    <el-tag 
                      size="small" 
                      :type="scope.row.is_active ? 'success' : 'info'"
                      effect="plain"
                    >
                      {{ scope.row.is_active ? '已上架' : '已下架' }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="price" label="价格" width="120">
            <template #default="scope">
              <span class="price">¥{{ scope.row.price.toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="stock" label="库存" width="100">
            <template #default="scope">
              <span :class="{ 'low-stock': scope.row.stock < 10 }">
                {{ scope.row.stock }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="销量" width="100">
            <template #default="scope">
              {{ scope.row.sales_count || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <div class="action-buttons">
                <AppButton 
                  type="primary" 
                  size="small" 
                  @click="handleEdit(scope.row)"
                  text
                >
                  编辑
                </AppButton>
                <AppButton 
                  :type="scope.row.is_active ? 'danger' : 'success'" 
                  size="small" 
                  @click="toggleProductStatus(scope.row)"
                  text
                >
                  {{ scope.row.is_active ? '下架' : '上架' }}
                </AppButton>
                <AppButton 
                  type="danger" 
                  size="small" 
                  @click="handleDelete(scope.row)"
                  text
                >
                  删除
                </AppButton>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            background
          />
        </div>
      </el-card>
    </div>
    
    <!-- 添加/编辑产品对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑产品' : '添加产品'"
      width="600px"
      destroy-on-close
    >
      <el-form
        ref="productFormRef"
        :model="productForm"
        :rules="productRules"
        label-position="top"
      >
        <el-form-item label="产品名称" prop="name">
          <el-input v-model="productForm.name" placeholder="请输入产品名称" />
        </el-form-item>
        
        <el-form-item label="产品类型" prop="product_type">
          <el-radio-group v-model="productForm.product_type">
            <el-radio label="software">软件</el-radio>
            <el-radio label="hardware">硬件</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="产品描述" prop="description">
          <el-input
            v-model="productForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入产品描述"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="价格" prop="price">
              <el-input-number
                v-model="productForm.price"
                :precision="2"
                :step="0.01"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="库存" prop="stock">
              <el-input-number
                v-model="productForm.stock"
                :min="0"
                :precision="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="图片URL" prop="image_url">
          <el-input v-model="productForm.image_url" placeholder="请输入图片URL" />
        </el-form-item>
        
        <el-form-item label="产品状态">
          <el-switch
            v-model="productForm.is_active"
            active-text="上架"
            inactive-text="下架"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitProductForm" :loading="submitting">
            {{ isEdit ? '保存修改' : '添加产品' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </PageContainer>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { useUserStore } from '../../store/index'
import api from '../../api'
import PageContainer from '../../components/layout/PageContainer.vue'
import { 
  Plus, 
  Search, 
  Top, 
  Bottom, 
  Picture 
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()
const productTableRef = ref(null)

// 检查是否是管理员
onMounted(() => {
  if (!userStore.isAdmin) {
    ElMessage.error('您没有权限访问此页面')
    router.push('/')
  }
  fetchProducts()
})

// 产品列表数据
const loading = ref(false)
const submitting = ref(false)
const products = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const productType = ref('')
const isActive = ref('')
const searchQuery = ref('')
const selectedProducts = ref([])

// 添加/编辑产品对话框
const dialogVisible = ref(false)
const isEdit = ref(false)
const productFormRef = ref(null)
const productForm = reactive({
  name: '',
  description: '',
  price: 0,
  stock: 0,
  product_type: 'software',
  image_url: '',
  is_active: true
})

// 表单验证规则
const productRules = {
  name: [
    { required: true, message: '请输入产品名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在2到100个字符之间', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入产品描述', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格必须大于等于0', trigger: 'blur' }
  ],
  stock: [
    { required: true, message: '请输入库存', trigger: 'blur' },
    { type: 'number', min: 0, message: '库存必须大于等于0', trigger: 'blur' }
  ],
  product_type: [
    { required: true, message: '请选择产品类型', trigger: 'change' }
  ]
}

// 计算属性：是否有已上架的产品被选中
const hasActiveSelected = computed(() => {
  return selectedProducts.value.some(product => product.is_active)
})

// 计算属性：是否有已下架的产品被选中
const hasInactiveSelected = computed(() => {
  return selectedProducts.value.some(product => !product.is_active)
})

// 获取产品列表
const fetchProducts = async () => {
  loading.value = true
  try {
    const params = {
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value
    }

    if (productType.value) {
      params.product_type = productType.value
    }

    if (isActive.value !== '') {
      params.is_active = isActive.value === 'true'
    }

    if (searchQuery.value) {
      params.search = searchQuery.value
    }

    // 使用产品API获取所有产品
    params.admin = true

    const response = await api.product.getProducts(params)
    if (response.data.success) {
      products.value = response.data.data
      total.value = response.data.total
    } else {
      ElMessage.error(response.data.message || '获取产品列表失败')
    }
  } catch (error) {
    console.error('获取产品列表失败:', error)
    ElMessage.error('获取产品列表失败')
  } finally {
    loading.value = false
  }
}

// 处理筛选条件变化
const handleFilterChange = () => {
  currentPage.value = 1
  fetchProducts()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchProducts()
}

// 处理每页数量变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchProducts()
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedProducts.value = selection
}

// 显示添加产品对话框
const showAddProductDialog = () => {
  isEdit.value = false
  Object.assign(productForm, {
    name: '',
    description: '',
    price: 0,
    stock: 0,
    product_type: 'software',
    image_url: '',
    is_active: true
  })
  dialogVisible.value = true
}

// 处理编辑产品
const handleEdit = (row) => {
  isEdit.value = true
  Object.assign(productForm, row)
  dialogVisible.value = true
}

// 提交产品表单
const submitProductForm = async () => {
  if (!productFormRef.value) return

  await productFormRef.value.validate(async (valid) => {
    if (!valid) return

    submitting.value = true
    try {
      let response
      if (isEdit.value) {
        // 编辑产品
        response = await api.product.updateProduct(productForm.id, productForm)
      } else {
        // 添加产品
        response = await api.product.createProduct(productForm)
      }

      if (response.data.success) {
        ElMessage.success(isEdit.value ? '产品更新成功' : '产品添加成功')
        dialogVisible.value = false
        fetchProducts()
      } else {
        ElMessage.error(response.data.message || (isEdit.value ? '更新产品失败' : '添加产品失败'))
      }
    } catch (error) {
      console.error(isEdit.value ? '更新产品失败:' : '添加产品失败:', error)
      ElMessage.error(isEdit.value ? '更新产品失败' : '添加产品失败')
    } finally {
      submitting.value = false
    }
  })
}

// 切换产品状态
const toggleProductStatus = async (product) => {
  const action = product.is_active ? '下架' : '上架'
  
  try {
    await ElMessageBox.confirm(
      `确定要${action}产品 "${product.name}" 吗？`,
      `${action}产品`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const updatedProduct = { ...product, is_active: !product.is_active }
    const response = await api.product.updateProduct(product.id, updatedProduct)
    
    if (response.data.success) {
      ElMessage.success(`产品${action}成功`)
      fetchProducts()
    } else {
      ElMessage.error(response.data.message || `产品${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`产品${action}失败:`, error)
      ElMessage.error(`产品${action}失败`)
    }
  }
}

// 批量更新产品状态
const batchUpdateStatus = async (status) => {
  const action = status ? '上架' : '下架'
  
  try {
    await ElMessageBox.confirm(
      `确定要批量${action}选中的 ${selectedProducts.value.length} 个产品吗？`,
      `批量${action}产品`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const productIds = selectedProducts.value
      .filter(product => product.is_active !== status)
      .map(product => product.id)
    
    if (productIds.length === 0) {
      ElMessage.info(`没有需要${action}的产品`)
      return
    }
    
    loading.value = true
    try {
      // 这里应该调用批量更新API，但目前后端可能没有实现
      // 所以我们使用循环逐个更新
      const promises = productIds.map(id => {
        const product = selectedProducts.value.find(p => p.id === id)
        return api.product.updateProduct(id, { ...product, is_active: status })
      })
      
      await Promise.all(promises)
      ElMessage.success(`批量${action}产品成功`)
      fetchProducts()
      // 清除选择
      productTableRef.value?.clearSelection()
    } catch (error) {
      console.error(`批量${action}产品失败:`, error)
      ElMessage.error(`批量${action}产品失败`)
    } finally {
      loading.value = false
    }
  } catch (error) {
    // 用户取消操作
  }
}

// 处理删除产品
const handleDelete = async (product) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除产品 "${product.name}" 吗？此操作不可逆。`,
      '删除产品',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'danger'
      }
    )
    
    const response = await api.product.deleteProduct(product.id)
    
    if (response.data.success) {
      ElMessage.success('产品删除成功')
      fetchProducts()
    } else {
      ElMessage.error(response.data.message || '删除产品失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除产品失败:', error)
      ElMessage.error('删除产品失败')
    }
  }
}
</script>

<style scoped>
.admin-page {
  max-width: var(--container-max-width);
  margin: 0 auto;
}

.admin-products {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0;
}

.page-subtitle {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin: var(--spacing-2) 0 0 0;
}

.filter-toolbar {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-4);
}

.filter-group, .search-group {
  display: flex;
  gap: var(--spacing-3);
}

.batch-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
}

.selection-info {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
}

.selection-count {
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

.batch-actions {
  display: flex;
  gap: var(--spacing-3);
}

.products-card {
  border-radius: var(--border-radius-lg);
  overflow: hidden;
}

.loading-container, .empty-container {
  padding: var(--spacing-8) 0;
  text-align: center;
}

.products-table {
  --el-table-border-color: var(--border-color-light);
  --el-table-header-bg-color: var(--bg-secondary);
  --el-table-row-hover-bg-color: var(--bg-hover);
}

.product-info-cell {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.product-image {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  flex-shrink: 0;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-secondary);
  color: var(--text-tertiary);
  font-size: 20px;
}

.product-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.product-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.product-meta {
  display: flex;
  gap: var(--spacing-2);
}

.price {
  font-weight: var(--font-weight-semibold);
  color: var(--accent-color);
}

.low-stock {
  color: var(--danger-color);
  font-weight: var(--font-weight-semibold);
}

.action-buttons {
  display: flex;
  gap: var(--spacing-2);
}

.pagination-container {
  margin-top: var(--spacing-6);
  display: flex;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .page-header, .filter-toolbar, .batch-toolbar {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-4);
  }
  
  .filter-group, .search-group, .batch-actions {
    width: 100%;
  }
  
  .search-group {
    flex-direction: column;
  }
  
  .pagination-container {
    justify-content: center;
  }
}
</style>
