<template>
  <footer class="app-footer">
    <div class="footer-container">
      <div class="footer-content">
        <div class="footer-section company-info">
          <h3>{{ siteInfo.company_name || '软件和设备销售平台' }}</h3>
          <p>{{ siteInfo.description || '提供高质量的软件和设备销售服务' }}</p>
        </div>
        
        <div class="footer-section quick-links">
          <h3>快速链接</h3>
          <ul>
            <li><router-link to="/">首页</router-link></li>
            <li><router-link to="/products">产品列表</router-link></li>
            <li v-if="userStore.isLoggedIn"><router-link to="/profile">个人中心</router-link></li>
            <li v-if="userStore.isLoggedIn && !userStore.isAdmin"><router-link to="/orders">我的订单</router-link></li>
            <li v-if="userStore.isAdmin"><router-link to="/admin">管理后台</router-link></li>
            <li v-if="!userStore.isLoggedIn"><router-link to="/login">登录</router-link></li>
            <li v-if="!userStore.isLoggedIn"><router-link to="/register">注册</router-link></li>
          </ul>
        </div>
        
        <div class="footer-section contact-info">
          <h3>联系我们</h3>
          <p>{{ siteInfo.contact_info || '电话：123-456-7890\n邮箱：<EMAIL>' }}</p>
          <div class="social-links">
            <a href="#" aria-label="微信" title="微信">
              <el-icon><ChatDotRound /></el-icon>
            </a>
            <a href="#" aria-label="微博" title="微博">
              <el-icon><Share /></el-icon>
            </a>
            <a href="#" aria-label="邮箱" title="邮箱">
              <el-icon><Message /></el-icon>
            </a>
          </div>
        </div>
      </div>
      
      <div class="footer-bottom">
        <p>&copy; {{ currentYear }} {{ siteInfo.company_name || '软件和设备销售平台' }}. 保留所有权利.</p>
        <p>
          <router-link to="/privacy">隐私政策</router-link> | 
          <router-link to="/terms">使用条款</router-link>
        </p>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { computed } from 'vue'
import { useSiteStore, useUserStore } from '../../store'
import { ChatDotRound, Share, Message } from '@element-plus/icons-vue'

const siteStore = useSiteStore()
const userStore = useUserStore()

const siteInfo = computed(() => siteStore.siteInfo)
const currentYear = computed(() => new Date().getFullYear())
</script>

<style scoped>
.app-footer {
  background-color: var(--neutral-800);
  color: var(--neutral-200);
  padding: var(--spacing-8) 0 var(--spacing-4);
  margin-top: var(--spacing-8);
}

.footer-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-6);
}

.footer-section h3 {
  color: var(--text-light);
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-4);
  position: relative;
}

.footer-section h3::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 40px;
  height: 2px;
  background-color: var(--primary-color);
}

.quick-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.quick-links li {
  margin-bottom: var(--spacing-2);
}

.quick-links a {
  color: var(--neutral-300);
  text-decoration: none;
  transition: color var(--transition-fast) ease;
}

.quick-links a:hover {
  color: var(--primary-light);
  text-decoration: none;
}

.contact-info p {
  margin-bottom: var(--spacing-4);
  white-space: pre-line;
}

.social-links {
  display: flex;
  gap: var(--spacing-3);
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--neutral-700);
  color: var(--neutral-200);
  transition: all var(--transition-fast) ease;
}

.social-links a:hover {
  background-color: var(--primary-color);
  color: var(--text-light);
  transform: translateY(-3px);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--neutral-700);
  font-size: var(--font-size-sm);
  color: var(--neutral-400);
}

.footer-bottom a {
  color: var(--neutral-400);
  text-decoration: none;
  transition: color var(--transition-fast) ease;
}

.footer-bottom a:hover {
  color: var(--primary-light);
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: var(--spacing-2);
    text-align: center;
  }
}
</style>
