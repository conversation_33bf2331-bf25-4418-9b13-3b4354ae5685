# 数据库管理策略

本文档描述了软件和设备销售平台的数据库管理策略，包括初始化、迁移和维护。

## 1. 数据库初始化

系统提供了两种数据库初始化方法：

### 1.1 Docker容器首次启动初始化

当Docker容器首次启动时，PostgreSQL会自动执行`/docker-entrypoint-initdb.d`目录中的所有SQL脚本。在我们的项目中，这个目录被挂载到`./database/init`目录，其中包含`01_init.sql`文件。

这个脚本会创建所有必要的表和初始数据，包括：
- 用户表（users）
- 产品表（products）
- 订单表（orders）
- 订单项表（order_items）
- 支付记录表（payments）
- 软件密钥表（license_keys）
- 站点信息表（site_info）

同时，它还会插入默认的管理员用户和站点信息。

**注意**：这个初始化过程只会在数据库卷为空时执行。如果数据库卷已经包含数据，这个脚本不会被执行。

### 1.2 Python脚本初始化

系统还提供了两个Python脚本来初始化数据库：

- `backend/app/scripts/create_tables.py`：创建数据库表
- `backend/app/scripts/create_admin.py`：创建管理员用户

这些脚本可以在任何时候手动执行，用于创建不存在的表和用户。它们使用SQLAlchemy的ORM功能，与代码中的模型保持同步。

执行这些脚本的命令：

```bash
# 创建数据库表
docker-compose exec backend python app/scripts/create_tables.py

# 创建管理员用户
docker-compose exec backend python app/scripts/create_admin.py
```

## 2. 数据库迁移

目前，系统没有使用自动化的数据库迁移工具（如Alembic）。如果需要对数据库结构进行更改，有以下几种方法：

### 2.1 更新初始化脚本

更新`database/init/01_init.sql`文件，添加新的表或列。然后，可以手动执行这个脚本：

```bash
docker-compose exec database psql -U user -d sales_platform -f /docker-entrypoint-initdb.d/01_init.sql
```

由于脚本中使用了`IF NOT EXISTS`条件，它不会覆盖已存在的表，只会创建不存在的表。

### 2.2 更新Python脚本

更新`backend/app/scripts/create_tables.py`脚本，添加新的表或列。然后，执行这个脚本：

```bash
docker-compose exec backend python app/scripts/create_tables.py
```

### 2.3 手动执行SQL命令

直接执行SQL命令来修改数据库结构：

```bash
docker-compose exec database psql -U user -d sales_platform -c "ALTER TABLE users ADD COLUMN new_column VARCHAR(100);"
```

## 3. 数据库备份和恢复

### 3.1 备份数据库

```bash
docker-compose exec database pg_dump -U user -d sales_platform > backup.sql
```

### 3.2 恢复数据库

```bash
cat backup.sql | docker-compose exec -T database psql -U user -d sales_platform
```

## 4. 开发环境重置

如果需要完全重置数据库，可以删除数据库卷并重新创建：

```bash
# 停止所有容器并删除卷
docker-compose down -v

# 重新启动所有容器
docker-compose up -d
```

这将删除所有数据，并触发初始化脚本的执行。

## 5. 最佳实践

1. **保持模型和数据库同步**：确保SQLAlchemy模型与数据库结构保持同步。
2. **使用版本控制**：将数据库初始化脚本和Python脚本纳入版本控制。
3. **定期备份**：在生产环境中定期备份数据库。
4. **测试迁移**：在应用到生产环境之前，先在测试环境中测试数据库更改。
5. **记录更改**：记录所有对数据库结构的更改，包括添加的表和列。
