import asyncio
import os
from typing import AsyncGenerator, Generator

import pytest
from fastapi import FastAP<PERSON>
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from app.core.config import settings
from app.core.database import Base, get_db
from app.main import app as fastapi_app

# 设置测试环境
os.environ["TESTING"] = "true"
os.environ["ENVIRONMENT"] = "testing"
os.environ["DATABASE_URL"] = "sqlite:///./test.db"
os.environ["SECRET_KEY"] = "test_secret_key"
os.environ["ACCESS_TOKEN_EXPIRE_MINUTES"] = "60"
os.environ["ALIPAY_APP_ID"] = "test_alipay_app_id"
os.environ["ALIPAY_NOTIFY_URL"] = "http://localhost:8000/api/v1/payments/notify"
os.environ["ALIPAY_RETURN_URL"] = "http://localhost:5173/payment-result"

# 创建测试数据库引擎
SQLALCHEMY_DATABASE_URL = "sqlite+aiosqlite:///./test.db"
engine = create_async_engine(
    SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False}
)
TestingSessionLocal = sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
async def db() -> AsyncGenerator[AsyncSession, None]:
    """创建测试数据库会话"""
    # 创建表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    # 创建会话
    async with TestingSessionLocal() as session:
        yield session

    # 清理表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest.fixture(scope="function")
async def client(db: AsyncSession) -> AsyncGenerator[AsyncClient, None]:
    """创建测试客户端"""

    # 覆盖依赖
    async def override_get_db():
        try:
            yield db
        finally:
            pass

    # 应用依赖覆盖
    fastapi_app.dependency_overrides[get_db] = override_get_db

    # 创建测试客户端
    async with AsyncClient(base_url="http://test") as client:
        yield client

    # 清理依赖覆盖
    fastapi_app.dependency_overrides = {}
